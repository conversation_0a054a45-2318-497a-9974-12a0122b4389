import { defineConfig, presetAttributify, presetIcons, presetTypography, presetUno, transformerDirectives, transformerVariantGroup } from 'unocss'
import presetAutoprefixer from './presets/autoprefixer'
import { allMenuIcons, colorOpacity, colorScales, directions, fontSizes, gapSizes, themeColors } from './src/components/x/utils/themes'
const fontSizeKeys = Object.keys(fontSizes)
const colorTypes = Object.keys(themeColors)

// 生成主题色
function generateColorCombinations(): Record<string, string> {
	const result: Record<string, string> = {}
	// 获取 :root 的变量
	for (const type of colorTypes) {
		colorOpacity.forEach((opacity) => {
			// 使用RGB变量和rgba()函数替代color-mix，提高浏览器兼容性
			result[`${type}_${opacity}`] = `rgba(var(--color-${type}-rgb), ${opacity / 100})`
			// 色阶变体
			colorScales.forEach((scale) => {
				result[`${type}-${scale}`] = `var(--color-${type}-${scale})`
				// 为每个色阶添加透明度变体
				// 使用RGB变量和rgba()函数替代color-mix，提高浏览器兼容性
				result[`${type}-${scale}_${opacity}`] = `rgba(var(--color-${type}-${scale}-rgb), ${opacity / 100})`
			})
		})
		// 基础颜色 todo 这里如果是 `var(--)`就不能用 bg-primary/10这种，但是如果直接是颜色值，则可以自动生成透明度
		result[type] = themeColors[type]
	}
	result.test = 'var(--color-test)'
	return result
}

// Mapping for single-letter directions to full names
const dirMapping = Object.fromEntries(directions.map((dir) => [dir[0], dir]))

function generateRules() {
	const rules = []

	// Font size and height rules
	rules.push([new RegExp(`^fs-(${fontSizeKeys.join('|')})$`), ([, size]) => ({ 'font-size': `var(--font-size-${size})` })])
	rules.push([new RegExp(`^text-(${fontSizeKeys.join('|')})$`), ([, size]) => ({ 'font-size': `var(--font-size-${size})` })])
	rules.push([
		new RegExp(`^h-(${fontSizeKeys.join('|')})$`),
		([, size]) => {
			const height = (parseFloat(fontSizes[size]) || 0) * 1.51 + 'rem'
			return { height, 'min-height': height }
		},
	])
	// Margin directions
	rules.push([
		new RegExp(`^m(${directions.map((d) => d[0]).join('|')})-(${Object.keys(gapSizes).join('|')})$`),
		([, dir, size]) => ({ [`margin-${dirMapping[dir]}`]: gapSizes[size] }),
	])
	rules.push([
		new RegExp(`^m(${directions.map((d) => d[0]).join('|')})-(${Object.keys(gapSizes).join('|')})-([0-9]*\\.?[0-9]+)$`),
		([, dir, size, num]) => ({
			[`margin-${dirMapping[dir]}`]: `${parseFloat(gapSizes[size]) * parseFloat(num)}rem`,
		}),
	])
	// Padding directions
	rules.push([
		new RegExp(`^p(${directions.map((d) => d[0]).join('|')})-(${Object.keys(gapSizes).join('|')})$`),
		([, dir, size]) => ({ [`padding-${dirMapping[dir]}`]: gapSizes[size] }),
	])
	rules.push([
		new RegExp(`^p(${directions.map((d) => d[0]).join('|')})-(${Object.keys(gapSizes).join('|')})-([0-9]*\\.?[0-9]+)$`),
		([, dir, size, num]) => ({
			[`padding-${dirMapping[dir]}`]: `${parseFloat(gapSizes[size]) * parseFloat(num)}rem`,
		}),
	])

	// Margin
	rules.push([
		new RegExp(`^(m|p)-(${Object.keys(gapSizes).join('|')})$`),
		([, type, size]) => ({
			[type === 'm' ? 'margin' : 'padding']: gapSizes[size],
		}),
	])
	rules.push([
		new RegExp(`^(m|p)-(${Object.keys(gapSizes).join('|')})-(${Object.keys(gapSizes).join('|')})$`),
		([, type, size1, size2]) => ({
			[type === 'm' ? 'margin' : 'padding']: `${gapSizes[size1]} ${gapSizes[size2]}`,
		}),
	])
	rules.push([
		new RegExp(`^(m|p)-(${Object.keys(gapSizes).join('|')})-([0-9]*\\.?[0-9]+)$`),
		([, type, size, num]) => ({
			[type === 'm' ? 'margin' : 'padding']: `${parseFloat(gapSizes[size]) * parseFloat(num)}rem`,
		}),
	])

	// Margin and padding X and Y rules
	rules.push([
		new RegExp(`^(mx|my|px|py)-(${Object.keys(gapSizes).join('|')})$`),
		([, side, size]) => {
			const [prefix, direction] = side.split('')
			const propertyPrefix = prefix === 'm' ? 'margin' : 'padding'
			const directions = direction === 'x' ? ['left', 'right'] : ['top', 'bottom']
			return directions.reduce((acc, dir) => {
				acc[`${propertyPrefix}-${dir}`] = gapSizes[size]
				return acc
			}, {})
		},
	])
	rules.push([
		new RegExp(`^(mx|my|px|py)-(${Object.keys(gapSizes).join('|')})-([0-9]*\\.?[0-9]+)$`),
		([, side, size, num]) => {
			const [prefix, direction] = side.split('')
			const propertyPrefix = prefix === 'm' ? 'margin' : 'padding'
			const directions = direction === 'x' ? ['left', 'right'] : ['top', 'bottom']
			return directions.reduce((acc, dir) => {
				acc[`${propertyPrefix}-${dir}`] = `${parseFloat(gapSizes[size]) * parseFloat(num)}rem`
				return acc
			}, {})
		},
	])

	// top bottom left right
	rules.push([
		new RegExp(`^(${directions.map((d) => d[0]).join('|')})-(${Object.keys(gapSizes).join('|')})$`),
		([, dir, size]) => ({ [`${dirMapping[dir]}`]: gapSizes[size] }),
	])

	// gap
	rules.push([
		new RegExp(`^gap-(${Object.keys(gapSizes).join('|')})$`),
		([, size]) => ({
			gap: `${gapSizes[size]}`,
		}),
	])
	rules.push([
		new RegExp(`^gap-(${Object.keys(gapSizes).join('|')})-([0-9]*\\.?[0-9]+)$`),
		([, size, num]) => ({
			gap: `${parseFloat(gapSizes[size]) * parseFloat(num)}rem`,
		}),
	])
	// gap-x-base gap-y-base
	rules.push([
		new RegExp(`^gap-x-(${Object.keys(gapSizes).join('|')})$`),
		([, size]) => ({
			'column-gap': `${gapSizes[size]}`,
		}),
	])
	rules.push([
		new RegExp(`^gap-y-(${Object.keys(gapSizes).join('|')})$`),
		([, size]) => ({
			'row-gap': `${gapSizes[size]}`,
		}),
	])
	rules.push([
		new RegExp(`^gap-x-(${Object.keys(gapSizes).join('|')})-([0-9]*\\.?[0-9]+)$`),
		([, size, num]) => ({
			'column-gap': `${parseFloat(gapSizes[size]) * parseFloat(num)}rem`,
		}),
	])
	rules.push([
		new RegExp(`^gap-y-(${Object.keys(gapSizes).join('|')})-([0-9]*\\.?[0-9]+)$`),
		([, size, num]) => ({
			'row-gap': `${parseFloat(gapSizes[size]) * parseFloat(num)}rem`,
		}),
	])

	// border
	rules.push([
		new RegExp(`^border-(${directions.map((d) => d[0]).join('|')})-(${colorTypes.join('|')}|[#\\w]+)$`), // 在原有基础上加上|[#\\w]+，匹配自定义颜色
		([, direction, color]) => ({
			[`border-${dirMapping[direction]}`]: `1px solid ${themeColors[color] || color}`, // 如果是colorTypes中的颜色，则用themeColors，否则直接使用传入的color
		}),
	])

	rules.push([
		new RegExp(`^border-(${colorTypes.join('|')}|[#\\w]+)$`), // 同样对第二个正则做同样的修改
		([, color]) => ({
			[`border`]: color === 'none' ? 'none' : `1px solid ${themeColors[color] || color}`, // 如果是colorTypes中的颜色，则用themeColors，否则直接使用传入的color
		}),
	])

	return rules
}

function generateShortcuts(): Record<string, string> {
	const result: Record<string, string> = {}
	return result
}
export default defineConfig({
	theme: {
		colors: generateColorCombinations(),
		bgs: generateColorCombinations(),
	},
	shortcuts: { ...generateShortcuts() },
	rules: [...generateRules()],
	// 确保动态规则可以生成
	safelist: [
		...Array.from({ length: 5 }, (_, i) => `pl-base-${i + 1}`),
		...'from-blue-600/60 to-blue-700/60 from-green-600/60 to-green-700/60 from-purple-600/60 to-purple-700/60 from-orange-600/60 to-orange-700/60 from-red-600 to-red-700'.split(
			' ',
		),
		...'i-carbon:catalog i-carbon:exam-mode'.split(' '),
		...'i-carbon:face-dissatisfied i-carbon:face-neutral i-carbon:face-satisfied i-carbon:face-cool'.split(' '),
		...'!color-danger-400 !bg-danger-400 !color-danger-600 !bg-danger-600 !color-warning-600 !bg-warning-600 !color-success-700 !bg-success-400 !bg-success-700 !color-success-900 !bg-success-900'.split(
			' ',
		),
		...allMenuIcons,
	],
	transformers: [transformerDirectives(), transformerVariantGroup()],
	presets: [
		presetAttributify(),
		presetIcons({
			autoInstall: true,
		}),
		presetUno(),
		presetTypography(),
		presetAutoprefixer(),
	],
})
