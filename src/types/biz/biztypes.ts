export type TypeZsjyExamStatus = (typeof BIZ_ExamStatus_CONSTANTS)[keyof typeof BIZ_ExamStatus_CONSTANTS]

/**
 * 试题资料类型定义
 */
export interface TypeZsjyQuestionData {
	id: number
	uuid: number
	content: string
}

/**
 * 答题项类型定义
 */
export interface TypeZsjyRecordItem {
	/** 题目ID */
	questionId: number
	/** 正确答案 */
	correctAnswer: string
	/** 用户答案 */
	userAnswer: string
	/** 是否正确 */
	isCorrect?: boolean
	/** 得分 */
	score?: number
	/** 答题时间（秒）- 从考试开始到答题的时间 */
	answerTime?: number
	/** 答题顺序（第几个回答的） */
	answerOrder?: number
	/** 是否标记 */
	isMarked?: boolean
	/** 是否收藏 */
	isCollected?: boolean
	/** 收藏时间 */
	collectedTime?: number
	/** 题目分类ID */
	cateId?: number
	/** 题目分类名称 */
	cateName?: string
	/** 题目难度 */
	difficulty?: string
	/** 题目分值 */
	questionScore?: number
	/** 答题用时（秒）- 实际花费在答题上的时间 */
	timeSpent?: number
	/** 答题开始时间（毫秒时间戳） */
	answerStartTime?: number
	/** 答题结束时间（毫秒时间戳） */
	answerEndTime?: number
	/** 题目内容摘要 */
	questionSummary?: string
	/** 答案修改次数 */
	answerChangeCount?: number
	/** 用户反馈 */
	userFeedback?: string
	/** 试题分类ID 0 */
	cateId0?: number
	/** 试题分类ID 1 */
	cateId1: number
	cateId1Name: string
	/** 知识点路径 */
	catePath?: string
}
/**
 * 试题类型定义
 */
export interface TypeZsjyQuestion {
	/** 编号 */
	id: number
	/** 分类 */
	cateId: number
	/** 考试类型cateid */
	cateId0: number
	/** 科目cateid */
	cateId1: number
	/** 关键词 */
	keyword: string
	/** 题干 */
	content: string
	/** 难度等级 */
	difficulty: string
	/** 分值 */
	score: number
	/** 答题限时（s） */
	timeLimit?: number
	/** 答案类型 */
	answerType?: string
	/** 答案内容JSON */
	optionsJson: string
	/** 解析 */
	analysis?: string
	/** 正确答案 */
	correctAnswer: string
	/** 试题状态 */
	status?: string
	/** 练习次数 */
	practiceTotal?: number
	/** 正确次数 */
	correctTotal?: number
	/** 错误次数 */
	errorTotal?: number
	/** 上次练习时间 */
	practiceLastTime?: string
	/** 收藏次数 */
	collectTotal: number
	/** 关联资料ID */
	dataId?: number
	/** 正确数量 */
	practiceCorrectTotal?: number
	/** 知识点 */
	catePath?: string
	/** 知识点id路径 */
	cateIdPath?: string

	userAnswer: string
	options: {
		/** 编号 */
		value: string
		/** 选项 */
		label: string
		/** 内容 */
		content: string
	}[]
	cateId0Name: string
	cateId1Name: string
	cateId2Name?: string
	isFirstDataQuestion?: boolean // 添加：标识是否是资料题的第一题
	isLastDataQuestion?: boolean
	/*资料*/
	data?: TypeZsjyQuestionData

	recordItem?: TypeZsjyRecordItem
	userQuestion?: TypeZsjyUserQuestion

	questionNo?: number
}
/**
 * 试卷类型定义
 */
export interface TypeZsjyPaper {
	/** 编号 */
	id: number
	/** 试卷类型 */
	type: string
	examTypeTags: string[]
	/** 试卷名称 */
	name: string
	/** 试卷描述 */
	description?: string
	/** 关联试题 */
	questionIds: string
	/** 试卷分数 */
	score: number
	/** 练习总分 */
	scoreTotal: number
	/** 练习平均分 */
	scoreAverage: number
	/** 试卷状态 */
	status: string
	/** 练习次数 */
	practiceTotal: number
	/** 上次练习时间 */
	practiceLastTime?: string
	/** 年份 */
	year?: string
	/** 省份 */
	province?: string
	/** 分类id */
	cateId?: number
	/** 一级分类 */
	cateId0: number
	/** 二级分类 */
	cateId1: number
	/** 最高分 */
	maxScore?: number
	/** 分类路径(知识点) */
	catePath?: string
	/** 分类id路径 */
	cateIdPath?: string
	/** 最低分 */
	minScore?: number
	/** 试题数量 */
	questionTotal?: number
	/** 做题时长(秒) */
	timeLimit: number
	/** 做题时长(分) */
	timeLimitM: number
	/** 备注 */
	mark?: string
	/** 最高分用户 */
	maxScoreUser?: string
	/** 最低分用户 */
	minScoreUser?: string
	/** 创建类型 */
	createType?: string
	/** 试题列表 */
	questionList?: TypeZsjyQuestion[]
	/** 资料列表 */
	dataList?: TypeZsjyQuestionData[]
	// cateid1 list 注意是cateid1
	cateList?: TypeZsjyQuestionCate[]
	/** 是否为模拟考试模式 */
	isSimulation: boolean
	/** 体验结束时间*/
	experienceEndTime?: number
}
/**
 * 作业类型定义
 */
export interface TypeZsjyJob {
	/** 编号 */
	id: number
	/** 考试名称 */
	name: string
	/** 试卷名称 */
	paperName: string
	/** 总分 */
	score: number
	/** 时长 */
	timeLimit: number
	/** 描述 */
	description?: string
	/** 试卷编号 */
	paperId: number
	/** 考生编号集合 */
	userIds: string
	/** 考生分类编号 */
	userCateId: number
	/** 用户路径 */
	userCatePath: string
	/** 总数 */
	userTotal?: number
	/** 考试状态 */
	status: string
	/** 试卷路径 */
	paperCatePath?: string
	paperType?: string
	/** 开始时间 */
	startTime?: string
	/** 结束时间 */
	endTime?: string
	/** 试题ids */
	questionIds: string
}
/**
 * 试题分类类型定义
 */
export interface TypeZsjyQuestionCate {
	/** 编号 */
	id: number
	/** 分类名称 */
	name: string
	/** 描述 */
	description?: string
	/** 排序 */
	sort: number
	/** 上级分类 */
	parentId: number
	/** 状态 */
	status: number
	/** 试题数量 */
	questionTotal: number
	/** 试卷数量 */
	paperTotal: number
	/** 考试类型 */
	examTypeTags: string
	/** 练习次数 */
	practiceTotal?: number
	/** 正确次数 */
	correctTotal?: number
	/** 错误次数 */
	errorTotal?: number
	mediaUrl: string
	mediaName: string
	configQuestionScore: number
	configQuestionTotal: number
	configScore: number
	configMinAnswerTime: number
	configTimeLimit: number
}

export interface TypeZsjyCateProgress {
	id: number
	timeSpent: number // 该分类花费的时间（秒）
	correctCount: number // 正确题数
	wrongCount: number // 错误题数
	unansweredCount: number // 未答题数
	lastActiveTime: number // 最后活动时间（毫秒时间戳）
	timeLimit: number // 该分类答题时间限制（秒）
	remainingTime: number // 该分类剩余时间（秒）
	questionIds: number[]
	questionTotal: number // 总题目数量
	score: number // 该分类总分
	userScore: number // 该分类得分
}
export interface TypeZsjyRecordInfo {
	/** 编号 */
	id: number
	/** 试卷编号 */
	paperId: number
	/** 试卷名称 */
	paperName: string
	/** 试卷类型 */
	paperType: string
	/** 试卷路径 */
	paperCatePath: string
	/** 试题ids */
	questionIds: string
	/** 试题总数 */
	questionTotal: number
	/** 试卷限时 */
	paperTimeLimit: number
	/** 用户编号 */
	userId: number
	/** 得分 */
	score: number
	/** 参与状态 */
	status: string
	/** 试卷cateid */
	paperCateId?: number
	/** 开始时间 */
	startTime: string
	/** 交卷时间 */
	endTime?: string
	/** 考试id */
	jobId?: number
	/** 用户试卷id */
	userPapersId?: number
	/** 试卷分数 */
	paperScore: number
	/** 当前答题索引 */
	currentQuestionIndex: number
	/** 剩余时间 */
	remainingTime: number
	/** 总花费时间 */
	totalTimeSpent: number
	/** cate进度 */
	cateProgress: Record<string, TypeZsjyCateProgress>
	/** 知识点统计 */
	pointStats: Record<
		string,
		{
			id: number
			correctCount: number
			wrongCount: number
			unansweredCount: number
			questionIds: number[]
			questionTotal: number
			score: number
			userScore: number
		}
	>
	/** 正确总数 */
	correctCount: number
	/** 错误总数 */
	wrongCount: number
	/** 未答数量 */
	unansweredCount: number
	/** 答题项列表 */
	itemList?: TypeZsjyRecordItem[]
	/** 试卷信息 */
	paperInfo?: TypeZsjyPaper
	/** 试题列表 */
	questionList?: TypeZsjyQuestion[]
	/** 资料列表 */
	dataList?: TypeZsjyQuestionData[]

	isHorizontalLayout?: boolean
	isAutoNavigate?: boolean
}

/**
 * 用户错题记录类型定义
 */
export interface TypeZsjyUserQuestion {
	/** 编号 */
	id: number
	/** 用户编号 */
	userId: number
	/** 试题编号 */
	questionId: number
	/** 类型 */
	type: string
	/** 题干摘要 */
	questionSummary: string
	/** 科目 */
	cateId1Name: string
	/** 分类ID */
	cateId: number
	/** 分类名称 */
	cateName: string
	/** 分类路径 */
	catePath: string
	/** 科目ID */
	cateId1: number
	/** 首次错误时间 */
	firstWrongTime: string
	/** 最近错误时间 */
	lastWrongTime: string
	/** 错误次数 */
	wrongCount: number
	/** 正确次数 */
	correctCount: number
	/** 总次数 */
	totalCount: number
	/** 是否收藏 */
	isCollected?: boolean
	/** 是否正确 */
	isCorrect?: boolean
	/** 收藏时间 */
	collectedTime: string
	/** 是否掌握 */
	isMastered?: boolean
	/** 当前记忆 */
	masteryLevel: number
	/** 复习掌握程度 */
	masteryLevels: any[]
	/** 首次学习时间 */
	firstLearnTime?: string
	/** 上次复习时间 */
	lastReviewTime?: string
	/** 下次复习时间 */
	nextReviewTime?: string
	/** 复习次数 */
	reviewCount: number
	/** 复习间隔 */
	reviewIntervals: any[]
	/** 记忆强度 */
	memoryStrength: number
	/** 查询解析时间 */
	viewedAnalysisTime?: string
	/** 上次查询解析时间 */
	lastViewedAnalysisTime?: string
	/** 答题信心度 */
	confidence?: number
}
