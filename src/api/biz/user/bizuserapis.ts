// 试卷 API
export const BIZ_USER_APIS = {
	getPaper: (id: number, jobId = undefined) => xRequest.get(`/user-api/x/zsjy-paper/get?id=${id}${Number(jobId) > 0 ? `&jobId=${jobId}` : ''}`),
	getRandomPaper: (params: any) => xRequest.get(`/user-api/x/zsjy-paper/get-random`, params),
	getQuestion: (id: number) => xRequest.get(`/user-api/x/zsjy-question/get?id=${id}`),
	getJobPage: (params: any) => xRequest.get(`/user-api/x/zsjy-job/page`, params),
	// 考试相关 API
	savePaperRecord: (params: any) => xRequest.put(`/user-api/x/zsjy-paper-record/save`, params),
	getPaperRecord: (id: number) => xRequest.get(`/user-api/x/zsjy-paper-record/get?id=${id}`),
	getPaperRecordPage: (params: any) => xRequest.get(`/user-api/x/zsjy-paper-record/page`, params),
	getUserQuestionsPage: (params: any) => xRequest.get(`/user-api/x/zsjy-user-questions/page`, params),
	getPaperRecordItemPage: (params: any) => xRequest.get(`/user-api/x/zsjy-paper-record-item/page`, params),
	getPaperRecordItemStats: (params: any) => xRequest.get(`/user-api/x/zsjy-paper-record-item/question-stats`, params),
	getPaperRecordItemTrend: (params: any) => xRequest.get(`/user-api/x/zsjy-paper-record-item/trend`, params),
	getPaperRecordItemSubjectAccuracy: (params: any) => xRequest.get(`/user-api/x/zsjy-paper-record-item/subject-accuracy`, params),
	updateUserQuestionCollected: (data: any) => xRequest.put(`/user-api/x/zsjy-user-questions/update-collected`, data),
	getUserPapersExamStats: (params: any) => xRequest.get(`/user-api/x/zsjy-user-papers/exam-stats`, params),
	//个人信息
	updateUserAvatar: (data: any) => xRequest.put(`/user-api/x/common-user/update-avatar`, data),
	updatePassword: (data: any) => xRequest.put(`/user-api/x/common-user/update-password`, data),
	// 题目
	updateUserQuestionMastery: (data: any) => xRequest.put(`/user-api/x/zsjy-user-questions/update-mastery`, data),
}
