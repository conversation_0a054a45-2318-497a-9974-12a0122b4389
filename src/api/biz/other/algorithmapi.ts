const baseUrl = 'https://viseem.natapp4.cc'
// http://cklocal.natapp1.cc
const params1Keys = ['mchd', 'xcxs', 'gzmzdcs', 'zyxsj', 'mcqj', 'mcqx', 'spydxs', 'kcyxcbj', 'xsgdpj', 'ssgdpj', 'zbjgdpj', 'ybjdpj']
export const BIZ_APIS_ALGORITHM = {
	processCalParams(_params) {
		const params = { workspaces: [], step: _params.step || 100 }
		for (const item of _params.workspaces) {
			const arr = [item.objectid || 0]
			for (const key of params1Keys) {
				arr.push(item?.[key] || 0)
			}
			// lodash展平数组
			// arr.push(...lodash.flatten(item?.points?.split(';').map((item) => item.split(','))))
			arr.push(item.points)
			params.workspaces.push(arr)
			// 把item的points字符串(先以逗号分隔，又以分号分隔)分隔成数组
		}
		return params
	},
	getCalData: async (_params = []): Promise<any> => {
		return request.post(baseUrl + '/calculate', _params)
	},
}
