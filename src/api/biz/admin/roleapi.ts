// 角色信息 API
export const BIZ_Role_APIS = {
	getPage: (params: any) => xRequest.get(`/admin-api/system/role/page`, params),
	getList: (params) => xRequest.get(`/admin-api/system/role/list`, params),
	getUserRoleList: (userId) => xRequest.get('/admin-api/system/permission/list-user-roles?userId=' + userId),
	getSimpleList: (params) => xRequest.get(`/admin-api/system/role/simple-list`, params),
	get: (id: number) => xRequest.get(`/admin-api/system/role/get?id=` + id),
	create: (data: any) => xRequest.post(`/admin-api/system/role/create`, data),
	update: (data: any) => xRequest.put(`/admin-api/system/role/update`, data),
	delete: (id: number) => xRequest.delete(`/admin-api/system/role/delete?id=` + id),
	export: (params) => xRequest.download(`/admin-api/system/role/export-excel`, params),
	assignRoleMenus: (data: any) => xRequest.post(`/admin-api/system/permission/assign-role-menu`, data),
	assignUserRoles: (data: any) => xRequest.post(`/admin-api/system/permission/assign-user-role`, data),
	assignRoleDataScope: (data: any) => xRequest.post(`/admin-api/system/permission/assign-role-data-scope`, data),
}
