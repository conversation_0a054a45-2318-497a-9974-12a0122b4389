// 代码生成表定义 API
export const BIZ_CodegenTable_APIS = {
	getPage: (params: any) => xRequest.get(`/admin-api/infra/codegen/table/page`, params),
	getList: (params) => xRequest.get(`/infra/codegen-table/list`, params),
	get: (id: number) => xRequest.get(`admin-api/infra/codegen/detail?tableId=` + id),
	create: (data: any) => xRequest.post(`/infra/codegen-table/create`, data),
	update: (data: any) => xRequest.put(`/admin-api/infra/codegen/update`, data),
	export: (params) => xRequest.download(`/infra/codegen-table/export-excel`, params),
	getDatabaseTableList: (params: {} = {}) => xRequest.get('/admin-api/infra/codegen/db/table/list', params),
	createList: (params: {} = {}) => xRequest.post('/admin-api/infra/codegen/create-list', params),
	download: (id) => xRequest.download('/admin-api/infra/codegen/download?tableId=' + id),
	delete: (id) => xRequest.delete('/admin-api/infra/codegen/delete?tableId=' + id),
	syncTable: (id) => xRequest.put('/admin-api/infra/codegen/sync-from-db?tableId=' + id),
}
