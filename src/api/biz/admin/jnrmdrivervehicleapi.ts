// 驾驶员-车辆关联 API
export const BIZ_JnrmDriverVehicle_APIS = {
	getPage: (params: any) => xRequest.get(`/admin-api/x/jnrm-driver-vehicle/page`, params),
	getList: (params) => xRequest.get(`/admin-api/x/jnrm-driver-vehicle/list`, params),
	get: (id: number) => xRequest.get(`/admin-api/x/jnrm-driver-vehicle/get?id=` + id),
	create: (data: any) => xRequest.post(`/admin-api/x/jnrm-driver-vehicle/create`, data),
	update: (data: any) => xRequest.put(`/admin-api/x/jnrm-driver-vehicle/update`, data),
	delete: (id: number) => xRequest.delete(`/admin-api/x/jnrm-driver-vehicle/delete?id=` + id),
	export: (params) => xRequest.download(`/admin-api/x/jnrm-driver-vehicle/export-excel`, params),
}
