// 公告 API
export const BIZ_CommonAnnouncement_APIS = {
	getPage: (params: any) => xRequest.get(`/admin-api/x/common-announcement/page`, params),
	getList: (params) => xRequest.get(`/admin-api/x/common-announcement/list`, params),
	get: (id: number) => xRequest.get(`/admin-api/x/common-announcement/get?id=` + id),
	create: (data: any) => xRequest.post(`/admin-api/x/common-announcement/create`, data),
	update: (data: any) => xRequest.put(`/admin-api/x/common-announcement/update`, data),
	delete: (id: number) => xRequest.delete(`/admin-api/x/common-announcement/delete?id=` + id),
	export: (params) => xRequest.download(`/admin-api/x/common-announcement/export-excel`, params),
}
