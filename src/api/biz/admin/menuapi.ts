// 菜单权限 API
export const BIZ_Menu_APIS = {
	getPage: async (params: any) => await xRequest.get(`/system/menu/page`, params),
	getList: async (params) => await xRequest.get(`/admin-api/system/menu/list`, params),
	getSimpleList: async (params) => await xRequest.get(`/admin-api/system/menu/simple-list`, params),
	getRoleMenuList: async (roleId) => await xRequest.get(`/admin-api/system/permission/list-role-menus?roleId=` + roleId),
	get: async (id: number) => await xRequest.get(`/admin-api/system/menu/get?id=` + id),
	create: async (data: any) => await xRequest.post(`/admin-api/system/menu/create`, data),
	update: async (data: any) => await xRequest.put(`/admin-api/system/menu/update`, data),
	delete: async (id: number) => await xRequest.delete(`/admin-api/system/menu/delete?id=` + id),
	export: async (params) => await xRequest.download(`/system/menu/export-excel`, params),
	getPermissionMenus: async (params: {} = {}) => {
		const res = await xRequest.get('/admin-api/system/auth/get-permission-info', params)
		useUserStore().permissions = res.permissions
		return res
	},
}
