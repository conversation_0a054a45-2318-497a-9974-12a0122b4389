// 告警历史 API
export const BIZ_JnrmAlertHistory_APIS = {
	getPage: (params: any) => xRequest.get(`/admin-api/x/jnrm-alert-history/page`, params),
	getList: (params) => xRequest.get(`/admin-api/x/jnrm-alert-history/list`, params),
	get: (id: number) => xRequest.get(`/admin-api/x/jnrm-alert-history/get?id=` + id),
	create: (data: any) => xRequest.post(`/admin-api/x/jnrm-alert-history/create`, data),
	update: (data: any) => xRequest.put(`/admin-api/x/jnrm-alert-history/update`, data),
	delete: (id: number) => xRequest.delete(`/admin-api/x/jnrm-alert-history/delete?id=` + id),
	export: (params) => xRequest.download(`/admin-api/x/jnrm-alert-history/export-excel`, params),
}
