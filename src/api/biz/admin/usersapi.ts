// 用户信息 API
export const BIZ_Users_APIS = {
	getPage: async (params: any) => await xRequest.get(`/admin-api/x/users/page`, params),
	getList: async (params) => await xRequest.get(`/admin-api/x/users/list`, params),
	get: async (id: number) => await xRequest.get(`/admin-api/x/users/get?id=` + id),
	create: async (data: any) => await xRequest.post(`/admin-api/x/users/create`, data),
	update: async (data: any) => await xRequest.put(`/admin-api/x/users/update`, data),
	delete: async (id: number) => await xRequest.delete(`/admin-api/x/users/delete?id=` + id),
	export: async (params) => await xRequest.download(`/admin-api/x/users/export-excel`, params),
}
