export const BIZ_CUSTOMER_APIS = {
	getList: async (params: {} = {}) => xRequest.get('/admin-api/jl/customer/page', params),
	getSimpleList: async (params: {} = {}) => xRequest.get('/admin-api/jl/customer/simple-page', params),
	create: async (data: any = {}) => xRequest.post('/admin-api/jl/customer/create', data),
	update: async (data: any = {}) => xRequest.post('/admin-api/jl/customer/update', data),
	delete: async (id) => xRequest.delete('/admin-api/jl/customer/delete', { id }),
	get: async (id) => xRequest.get('/admin-api/jl/customer/get', { id }),
}
