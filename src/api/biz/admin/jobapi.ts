// 定时任务 API
export const BIZ_Job_APIS = {
	getPage: (params: any) => xRequest.get(`/admin-api/infra/job/page`, params),
	getList: (params) => xRequest.get(`/admin-api/infra/job/list`, params),
	get: (id: number) => xRequest.get(`/admin-api/infra/job/get?id=` + id),
	create: (data: any) => xRequest.post(`/admin-api/infra/job/create`, data),
	update: (data: any) => xRequest.put(`/admin-api/infra/job/update`, data),
	updateJobStatus: (id, status) => xRequest.put(`/admin-api/infra/job/update-status?id=${id}&status=${status}`),
	runJob: (id) => xRequest.put(`/admin-api/infra/job/trigger?id=${id}`),
	delete: (id: number) => xRequest.delete(`/admin-api/infra/job/delete?id=` + id),
	export: (params) => xRequest.download(`/admin-api/infra/job/export-excel`, params),
}
