// 标签分组 API
export const BIZ_CommonTagGroup_APIS = {
	getPage: async (params: any) => await xRequest.get(`/admin-api/x/common-tag-group/page`, params),
	getList: async (params) => await xRequest.get(`/admin-api/x/common-tag-group/list`, params),
	get: async (id: number) => await xRequest.get(`/admin-api/x/common-tag-group/get?id=` + id),
	create: async (data: any) => await xRequest.post(`/admin-api/x/common-tag-group/create`, data),
	update: async (data: any) => await xRequest.put(`/admin-api/x/common-tag-group/update`, data),
	delete: async (id: number) => await xRequest.delete(`/admin-api/x/common-tag-group/delete?id=` + id),
	export: async (params) => await xRequest.download(`/admin-api/x/common-tag-group/export-excel`, params),
}
