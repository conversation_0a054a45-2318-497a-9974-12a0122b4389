import { createGetRoutes, setupLayouts } from 'virtual:meta-layouts'
import { createRouter, createWebHistory } from 'vue-router'
import { routes as fileRoutes } from 'vue-router/auto-routes'

declare module 'vue-router' {
	// eslint-disable-next-line no-unused-vars
	interface RouteMeta {
		title?: string
		layout?: string
	}
}

/**
 * 不带尾部 / 的 BASE_URL
 */
export const BASE_URL_WITHOUT_TAIL = import.meta.env.BASE_URL.endsWith('/') ? import.meta.env.BASE_URL.slice(0, -1) : import.meta.env.BASE_URL

/**
 * base 安全的路径解析
 * @param path 路径
 */
export function safeResolve(path: string) {
	return BASE_URL_WITHOUT_TAIL + path
}

// 重定向 BASE_URL
fileRoutes.flat(Infinity).forEach((route) => {
	route.path = safeResolve(route.path)
})

export const router = createRouter({
	history: createWebHistory(),
	routes: setupLayouts(fileRoutes),
})

export const getRoutes = createGetRoutes(router)

export default router
