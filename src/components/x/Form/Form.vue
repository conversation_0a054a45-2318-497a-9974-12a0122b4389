<template>
	<form autocomplete="off" @submit.prevent="submit" @reset.prevent="reset">
		<slot></slot>
	</form>
</template>

<script setup>
const props = defineProps({
	model: { type: Object, default: () => ({}) },
	rules: { type: Object, default: () => ({}) },
	// 表单布局配置
	labelWidth: { type: String, default: '80px' },
	itemWidth: { type: String, default: '' },
	labelPosition: { type: String, default: 'top' },
	errorVisible: {
		type: Boolean,
		default: true,
	},
})

// 处理嵌套属性
const getNestedValue = (obj, path) => {
	if (!path) return undefined
	if (!obj || typeof obj !== 'object') return undefined

	const keys = path.split('.')
	let current = obj

	for (const key of keys) {
		if (current === null || current === undefined) return undefined
		if (typeof current !== 'object') return undefined
		current = current[key]
	}

	return current
}

const setNestedValue = (obj, path, value) => {
	if (!path) return
	if (!obj || typeof obj !== 'object') return

	const keys = path.split('.')
	const lastKey = keys.pop()
	let current = obj

	for (const key of keys) {
		if (current[key] === undefined || current[key] === null) {
			current[key] = {}
		} else if (typeof current[key] !== 'object') {
			// 如果中间路径不是对象，则覆盖为对象
			current[key] = {}
		}
		current = current[key]
	}

	current[lastKey] = value
}

const emit = defineEmits(['submit'])
const formItems = ref([])
const initialModel = JSON.parse(JSON.stringify(props.model))
// 提供全局上下文
provide('formContext', {
	model: props.model,
	rules: props.rules,
	labelWidth: props.labelWidth,
	itemWidth: props.itemWidth,
	labelPosition: props.labelPosition,
	addItem: (item) => {
		// 确保不重复添加相同的表单项
		const existingIndex = formItems.value.findIndex((i) => i.prop === item.prop)
		if (existingIndex === -1) {
			formItems.value.push(item)
		}
	},
	removeItem: (prop) => {
		const index = formItems.value.findIndex((item) => item.prop === prop)
		if (index !== -1) {
			formItems.value.splice(index, 1)
		}
	},
	getFieldValue: (prop) => getNestedValue(props.model, prop), // 跨字段校验支持
	validateField: async (prop, trigger) => {
		const item = formItems.value.find((item) => item.prop === prop)
		return item?.validate(trigger)
	},
	errorVisible: props.errorVisible,
})

// 全局校验
const validate = async () => {
	try {
		const results = await Promise.all(formItems.value.map((item) => item.validate().catch((error) => error)))
		// 检查是否有校验失败的表单项
		const hasError = results.some((result) => !result.valid)
		if (hasError) {
			return { valid: false, errors: results.filter((result) => !result.valid) }
		}

		return { valid: true, results }
	} catch (error) {
		console.error('校验失败:', error)
		return { valid: false, errors: [error] }
	}
}

// 提交处理
const submit = async () => {
	const { valid } = await validate()
	if (valid) {
		emit('submit', props.model)
	}
}

// 重置表单
const reset = () => {
	// 重置初始值
	Object.keys(initialModel).forEach((key) => {
		setNestedValue(props.model, key, initialModel[key])
	})

	// 处理没有初始值的表单项
	formItems.value
		?.filter((item) => !(item?.prop in initialModel))
		?.forEach((item) => {
			setNestedValue(props.model, item.prop, undefined)
		})

	clearValidate()
}

// 清除校验结果
const clearValidate = () => {
	formItems.value.forEach((item) => item.clearError())
}

defineExpose({ validate, reset, clearValidate, submit })
</script>
