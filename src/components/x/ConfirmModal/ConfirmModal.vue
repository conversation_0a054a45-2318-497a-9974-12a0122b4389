<script setup lang="ts">
interface ConfirmOptions {
	title?: string
	content?: string
	confirmText?: string
	cancelText?: string
}

const state = reactive({
	visible: false,
	title: '提示',
	content: '确定要继续吗？',
	confirmText: '确认',
	cancelText: '取消',
	// eslint-disable-next-line no-unused-vars
	resolve: null as ((value: boolean) => void) | null,
	// eslint-disable-next-line no-unused-vars
	reject: null as ((reason: Error) => void) | null,
})

const open = (options?: ConfirmOptions) => {
	return new Promise<boolean>((resolve, reject) => {
		Object.assign(state, options || {}, {
			visible: true,
			resolve,
			reject,
		})
	})
}

const handleConfirm = () => {
	if (state.resolve) {
		state.resolve(true) // 确认操作 resolve
	}
	cleanup()
}

const handleCancel = () => {
	if (state.reject) {
		state.reject(new Error('cancel')) // 取消操作 reject
	}
	cleanup()
}

const cleanup = () => {
	state.visible = false
	state.resolve = null
	state.reject = null
}

// 监听 visible 变化，确保在外部关闭时也能正确清理
watch(
	() => state.visible,
	(newVisible) => {
		if (!newVisible && (state.resolve || state.reject)) {
			// 如果是外部关闭的弹窗（如点击 X 或 ESC），则执行取消操作
			handleCancel()
		}
	},
)

// 添加键盘事件处理函数
const handleKeyDown = (event: KeyboardEvent) => {
	if (state.visible && event.key === 'Enter') {
		event.preventDefault() // 阻止默认行为
		event.stopPropagation() // 阻止事件冒泡
		handleConfirm()
	}
}

// 组件挂载时添加键盘事件监听
onMounted(() => {
	window.addEventListener('keydown', handleKeyDown, true) // 使用捕获阶段
})

// 组件卸载时移除键盘事件监听
onUnmounted(() => {
	window.removeEventListener('keydown', handleKeyDown, true) // 使用捕获阶段
})

defineExpose({ open })
</script>

<template>
	<XModal v-model="state.visible" :z-index="7" :title="state.title" container-class="!w-auto">
		<div class="min-w-280px w20vw overflow-hidden">
			<div class="break-all color-content" v-html="state.content"></div>
			<div class="flex items-center justify-end mt-lg gap-sm p-xxs">
				<XButton class="!bg-white !text-content !border-border" @click="handleCancel">{{ state.cancelText }}</XButton>
				<XButton @click="handleConfirm">{{ state.confirmText }}</XButton>
			</div>
		</div>
	</XModal>
</template>

<style scoped></style>
