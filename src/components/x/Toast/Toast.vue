<template>
	<XTransition mode="elastic">
		<div
			class="flex items-center rounded p-xxs-sm gap-xs"
			:style="{ color: `var(--color-${type})`, border: `2px solid var(--color-${type})`, background: `var(--color-${type}-100)` }"
		>
			<div :class="icons[type]"></div>
			<div v-html="message"></div>
		</div>
	</XTransition>
</template>

<script setup>
defineProps({
	message: {
		type: String,
		required: true,
	},
	list: {
		type: Array,
		required: true,
	},
	type: {
		type: String,
		default: 'info',
	},
})
const emit = defineEmits(['close'])
const icons = {
	info: 'i-carbon:information-filled',
	success: 'i-carbon:checkmark-filled',
	warning: 'i-carbon:warning-filled',
	danger: 'i-carbon:warning-alt-filled',
	error: 'i-carbon:warning-alt-filled',
}
setTimeout(() => {
	emit('close')
}, 3000)
</script>

<style scoped></style>
