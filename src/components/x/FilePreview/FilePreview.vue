<script setup lang="ts">
import { computed } from 'vue'

const props = defineProps({
	fileUrl: {
		type: String,
		default: '',
	},
	fileName: {
		type: String,
		default: '',
	},
})

// Determine file type based on extension
const fileType = computed(() => {
	if (!props.fileUrl) return 'unknown'

	// 优先使用文件名
	const extension = X_FILE_UTILS.getUrlExtension(props.fileName) || X_FILE_UTILS.getUrlExtension(props.fileUrl)

	if (X_FILE_UTILS.isImageExtension(null, extension)) {
		return 'image'
	} else if (extension === 'pdf') {
		return 'pdf'
	} else if (['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt'].includes(extension)) {
		return 'office'
	} else if (['mp4', 'webm', 'ogg', 'mov', 'avi', 'wmv'].includes(extension)) {
		return 'video'
	} else {
		return 'unknown'
	}
})

// 获取显示用的文件名
const displayFileName = computed(() => {
	if (props.fileName) return props.fileName
	// 如果没有文件名，尝试从URL中提取
	const urlWithoutParams = props.fileUrl.split('?')[0]
	const filePath = urlWithoutParams.split('/').pop() || ''
	return decodeURIComponent(filePath)
})
function handleUrlClick() {
	// 创建一个临时的a标签，并模拟点击
	window.open(props.fileUrl, '_blank')
}

function handleDownload() {
	// 创建一个临时的a标签来触发下载
	const link = document.createElement('a')
	link.href = props.fileUrl
	link.download = displayFileName.value // 使用计算属性中的文件名
	document.body.appendChild(link)
	link.click()
	document.body.removeChild(link)
}
</script>

<template>
	<div v-if="fileUrl" class="max-h90vh flex flex-1 flex-col pb-base gap-xs">
		<div class="relative rounded-md bg-page text-sm p-xxs-xs">
			<x-button class="top-0 !absolute !right-0 !text-xs" @click="handleDownload">下载</x-button>
			<div v-if="displayFileName" class="wfull flex">
				<span class="min-w-4.5rem shrink-0 text-secondary">文件名：</span>
				<div class="min-w-0 flex flex-grow items-center text-content" :title="displayFileName">
					<div class="flex items-center overflow-hidden">
						<span class="min-w-1rem truncate">{{ X_FILE_UTILS.getUrlWithoutExtension(displayFileName) }}</span>
						<span v-if="X_FILE_UTILS.getUrlExtension(displayFileName)" class="shrink-0 whitespace-nowrap">
							{{ '.' + X_FILE_UTILS.getUrlExtension(displayFileName) }}
						</span>
					</div>
				</div>
			</div>
			<div class="wfull flex">
				<span class="min-w-4.5rem shrink-0 text-secondary">文件类型：</span>
				<div class="min-w-0 flex-grow text-secondary">
					<span>{{ fileType }}</span>
					<span v-if="X_FILE_UTILS.formatUrlFileSize(fileUrl)">({{ X_FILE_UTILS.formatUrlFileSize(fileUrl) }})</span>
				</div>
			</div>
			<div class="wfull flex">
				<span class="min-w-4.5rem shrink-0 text-secondary">链接：</span>
				<div class="min-w-0 flex flex-grow cursor-pointer items-center text-link" :title="fileUrl" @click="handleUrlClick">
					<div class="wfull flex items-center overflow-hidden text-xs">
						<!-- 分离URL主体、文件后缀和查询参数 -->
						<span class="min-w-1rem truncate">{{ X_FILE_UTILS.getUrlWithoutExtension(fileUrl) }}</span>
						<span v-if="X_FILE_UTILS.getUrlExtension(fileUrl)" class="shrink-0 whitespace-nowrap">
							{{ '.' + X_FILE_UTILS.getUrlExtension(fileUrl) }}
						</span>
					</div>
				</div>
			</div>
		</div>
		<div class="flex flex-1 items-center justify-center overflow-auto">
			<!-- 图片预览 -->
			<img v-if="fileType === 'image'" :src="fileUrl" class="max-w-full object-contain" alt="图片预览" />

			<!-- 视频预览 -->
			<video v-else-if="fileType === 'video'" class="max-h-full max-w-full" controls autoplay muted>
				<source :src="fileUrl" :type="'video/' + X_FILE_UTILS.getUrlExtension(fileUrl)" />
				您的浏览器不支持视频播放
			</video>

			<!-- PDF预览 -->
			<iframe v-else-if="fileType === 'pdf'" :src="fileUrl" class="h80vh wfull border-none" frameborder="0"></iframe>

			<!-- Office文档预览 -->
			<iframe
				v-else-if="fileType === 'office'"
				class="h-75vh wfull flex-1 border-none"
				:src="`https://view.officeapps.live.com/op/view.aspx?src=${encodeURIComponent(fileUrl)}&rs:toolbar=0`"
				frameborder="0"
			></iframe>

			<!-- 未知文件类型 -->
			<div v-else class="flex flex-col items-center justify-center text-center text-aid p-sm">
				<div class="mb-base">
					<div class="i-heroicons:document-text text-5xl text-gray-400"></div>
				</div>
				<p>无法预览此类型的文件</p>
				<a :href="fileUrl" target="_blank" class="cursor-pointer color-primary underline-aid mt-base">下载文件</a>
			</div>
		</div>
	</div>
	<div v-else>
		<x-empty />
	</div>
</template>

<style scoped></style>
