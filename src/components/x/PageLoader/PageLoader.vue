<template>
	<div v-if="showPreloader" class="preloader-container">
		<div class="w-[70%]" flex flex-col items-center justify-center>
			<div border="2px solid #888" wfull overflow-hidden rounded-4 p-2px>
				<div
					class="preloader-bar rounded-4"
					:style="{ width: progress + '%' }"
				></div>
			</div>
			<div class="mt-1rem flex items-center gap-1rem">
				<div class="loader text-1.2rem font-bold"></div>
				<span>{{ progress }}%</span>
			</div>
		</div>
	</div>
</template>

<script lang="ts" setup>
const showPreloader = ref(true)
const progress = ref(0)

const emit = defineEmits(['success'])

const resources = [
	{
		type: 'image',
		src: 'https://img.freepik.com/free-photo/portrait-people-with-colorful-rainbow-from-their-thoughts-brain-blue-background_23-2151475666.jpg?t=st=1719416938~exp=1719420538~hmac=a244022aa1478521cc32a5e7a7e2f3657512e565d20b886a1655e56c62b1a9cc&w=996',
	},
	// {
	// 	type: 'func',
	// 	func: () => {},
	// },
]
const resourceLen = resources.length * 2
let loadCount = 0
// 进行资源加载
const preloadResources = async () => {
	for (const resource of resources) {
		try {
			await loadResource(resource)
			updateProgress()
		} catch (error) {
			console.error('Error loading resource:', resource.src)
		}
	}

	// 隐藏预加载组件
	showPreloader.value = false
	emit('success')
}

// 加载单个资源
const loadResource = (resource) => {
	const type = resource.type
	return new Promise((resolve, reject) => {
		if (type === 'image') {
			const img = new Image()
			img.src = resource.src
			img.onload = () => {
				resolve('Image is ready to use!')
			}
			img.onerror = reject
			return
		}
		if (type === 'func') {
			resource?.func && resource.func()?.then(resolve)?.catch(reject)
		}
		/*		if (type === 'font') {
			const font = new FontFaceObserver(src)
			font.load(null, 10000).then(resolve).catch(reject)
			return
		}*/
	})
}

// 更新进度条
const updateProgress = () => {
	progress.value = Math.floor((++loadCount / resourceLen) * 100)
}

// 在组件挂载时开始加载资源
onMounted(() => {
	showPreloader.value = true
	preloadResources()
})
</script>

<style scoped>
/*@font-face {
	font-family: yinghei;
	src: url('https://nclimg.sjzhuaruo.com/data/yinghei.otf');
}
@font-face {
	font-family: shouxie;
	src: url('https://nclimg.sjzhuaruo.com/data/shouxie.ttf');
}
@font-face {
	font-family: fangzheng;
	src: url('https://nclimg.sjzhuaruo.com/data/%E6%96%B9%E6%AD%A3%E5%85%B0%E4%BA%AD%E7%B2%97%E9%BB%91.TTF');
}*/

.preloader-container {
	position: fixed;
	top: 0;
	left: 0;
	width: 100vw;
	height: 100vh;
	background-color: #f9e6cb;
	z-index: 9999;
	display: flex;
	align-items: center;
	justify-content: center;
	animation: hueRotate 20s infinite alternate;
}
@keyframes hueRotate {
	100% {
		filter: hue-rotate(360deg);
	}
}
.preloader-bar {
	height: 0.8rem;
	background-image: linear-gradient(to right, #bf994b, #d7bc84, #bc9645);
	transition: width 0.3s;
}

/* HTML: <div class="loader"></div> */
/* HTML: <div class="loader"></div> */
.loader {
	font-family: monospace;
	display: inline-grid;
}
.loader:before,
.loader:after {
	content: 'Loading...';
	grid-area: 1/1;
	-webkit-mask: linear-gradient(90deg, #000 50%, #0000 0) 0 50%/2ch 100%;
	animation: l11 1s infinite cubic-bezier(0.5, 220, 0.5, -220);
}
.loader:after {
	-webkit-mask-position: 1ch 50%;
	--s: -1;
}
@keyframes l11 {
	100% {
		transform: translateY(calc(var(--s, 1) * 0.1%));
	}
}
</style>
