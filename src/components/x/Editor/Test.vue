<template>
	<div class="rich-text-editor">
		<!-- 控制栏 -->
		<div class="controls">
			<div class="control-group">
				<input v-model="textColor" type="color" title="文字颜色" @change="updateActiveStyle('fill')" />
				<select v-model="fontSize" @change="updateActiveStyle('fontSize')">
					<option value="16">16px</option>
					<option value="24">24px</option>
					<option value="32">32px</option>
					<option value="48">48px</option>
				</select>
				<select v-model="fontFamily" @change="updateActiveStyle('fontFamily')">
					<option value="Arial">Arial</option>
					<option value="Times New Roman">Times New Roman</option>
					<option value="Courier New">Courier New</option>
					<option value="SimSun">宋体</option>
					<option value="Microsoft YaHei">微软雅黑</option>
				</select>
			</div>
			<div class="control-group">
				<button :class="{ active: isBold }" title="粗体" @click="toggleBold()">B</button>
				<button :class="{ active: isItalic }" title="斜体" @click="toggleItalic()">I</button>
				<button :class="{ active: isUnderline }" title="下划线" @click="toggleUnderline()">U</button>
			</div>
			<div class="control-group">
				<button :class="{ active: textAlign === 'left' }" title="左对齐" @click="setTextAlign('left')">
					<span class="align-icon">&#9664;</span>
				</button>
				<button :class="{ active: textAlign === 'center' }" title="居中对齐" @click="setTextAlign('center')">
					<span class="align-icon">&#9679;</span>
				</button>
				<button :class="{ active: textAlign === 'right' }" title="右对齐" @click="setTextAlign('right')">
					<span class="align-icon">&#9654;</span>
				</button>
			</div>
			<div class="control-group">
				<button title="添加文本" @click="addText()">添加文本</button>
				<button title="删除选中" @click="deleteSelected()">删除</button>
				<button title="清空画布" @click="clearCanvas()">清空</button>
				<button title="导出HTML" @click="exportToHtml()">导出HTML</button>
			</div>
		</div>

		<!-- 画布区域 -->
		<div class="canvas-container">
			<canvas ref="canvas"></canvas>
		</div>

		<!-- 添加文本对话框 -->
		<div v-if="showAddTextDialog" class="dialog-overlay">
			<div class="dialog-content">
				<h3>添加文本</h3>
				<textarea v-model="newText" placeholder="请输入文本内容"></textarea>
				<div class="dialog-actions">
					<button @click="confirmAddText">确定</button>
					<button @click="showAddTextDialog = false">取消</button>
				</div>
			</div>
		</div>

		<!-- HTML导出结果对话框 -->
		<div v-if="showExportDialog" class="dialog-overlay">
			<div class="dialog-content">
				<h3>HTML导出结果</h3>
				<textarea v-model="exportedHtml" readonly></textarea>
				<div class="dialog-actions">
					<button @click="copyToClipboard">复制到剪贴板</button>
					<button @click="showExportDialog = false">关闭</button>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed } from 'vue'
import * as fabric from 'fabric'

// 引用和状态
const canvas = ref(null)
let fabricCanvas = null

// 文本样式状态
const textColor = ref('#000000')
const fontSize = ref('24')
const fontFamily = ref('Arial')
const isBold = ref(false)
const isItalic = ref(false)
const isUnderline = ref(false)
const textAlign = ref('left')

// 对话框状态
const showAddTextDialog = ref(false)
const showExportDialog = ref(false)
const newText = ref('')
const exportedHtml = ref('')

// 计算属性
const fontWeight = computed(() => (isBold.value ? 'bold' : 'normal'))
const fontStyle = computed(() => (isItalic.value ? 'italic' : 'normal'))

// 初始化画布
onMounted(() => {
	// 创建Fabric.js画布
	fabricCanvas = new fabric.Canvas(canvas.value, {
		width: canvas.value.parentElement.clientWidth,
		height: 400,
		backgroundColor: '#ffffff',
	})

	// 监听选择事件，更新控制栏
	fabricCanvas.on('selection:created', updateControlsFromSelection)
	fabricCanvas.on('selection:updated', updateControlsFromSelection)
	fabricCanvas.on('selection:cleared', resetControls)

	// 监听窗口大小变化
	window.addEventListener('resize', handleResize)
})

// 清理事件监听
onUnmounted(() => {
	if (fabricCanvas) {
		fabricCanvas.dispose()
	}
	window.removeEventListener('resize', handleResize)
})

// 处理窗口大小变化
const handleResize = () => {
	if (fabricCanvas) {
		fabricCanvas.setWidth(canvas.value.parentElement.clientWidth)
		fabricCanvas.renderAll()
	}
}

// 从选中对象更新控制栏
const updateControlsFromSelection = () => {
	const activeObject = fabricCanvas.getActiveObject()
	if (activeObject && activeObject.type === 'textbox') {
		textColor.value = activeObject.fill
		fontSize.value = activeObject.fontSize.toString()
		fontFamily.value = activeObject.fontFamily
		isBold.value = activeObject.fontWeight === 'bold'
		isItalic.value = activeObject.fontStyle === 'italic'
		isUnderline.value = activeObject.underline || false
		textAlign.value = activeObject.textAlign
	}
}

// 重置控制栏
const resetControls = () => {
	textColor.value = '#000000'
	fontSize.value = '24'
	fontFamily.value = 'Arial'
	isBold.value = false
	isItalic.value = false
	isUnderline.value = false
	textAlign.value = 'left'
}

// 更新选中对象的样式
const updateActiveStyle = (property) => {
	const activeObject = fabricCanvas.getActiveObject()
	if (activeObject && activeObject.type === 'textbox') {
		switch (property) {
			case 'fill':
				activeObject.set('fill', textColor.value)
				break
			case 'fontSize':
				activeObject.set('fontSize', parseInt(fontSize.value))
				break
			case 'fontFamily':
				activeObject.set('fontFamily', fontFamily.value)
				break
		}
		fabricCanvas.renderAll()
	}
}

// 切换粗体
const toggleBold = () => {
	isBold.value = !isBold.value
	const activeObject = fabricCanvas.getActiveObject()
	if (activeObject && activeObject.type === 'textbox') {
		activeObject.set('fontWeight', isBold.value ? 'bold' : 'normal')
		fabricCanvas.renderAll()
	}
}

// 切换斜体
const toggleItalic = () => {
	isItalic.value = !isItalic.value
	const activeObject = fabricCanvas.getActiveObject()
	if (activeObject && activeObject.type === 'textbox') {
		activeObject.set('fontStyle', isItalic.value ? 'italic' : 'normal')
		fabricCanvas.renderAll()
	}
}

// 切换下划线
const toggleUnderline = () => {
	isUnderline.value = !isUnderline.value
	const activeObject = fabricCanvas.getActiveObject()
	if (activeObject && activeObject.type === 'textbox') {
		activeObject.set('underline', isUnderline.value)
		fabricCanvas.renderAll()
	}
}

// 设置文本对齐
const setTextAlign = (align) => {
	textAlign.value = align
	const activeObject = fabricCanvas.getActiveObject()
	if (activeObject && activeObject.type === 'textbox') {
		activeObject.set('textAlign', align)
		fabricCanvas.renderAll()
	}
}

// 添加文本
const addText = () => {
	newText.value = ''
	showAddTextDialog.value = true
}

// 确认添加文本
const confirmAddText = () => {
	if (newText.value.trim()) {
		const text = new fabric.Textbox(newText.value, {
			left: fabricCanvas.width / 2,
			top: fabricCanvas.height / 2,
			fontSize: parseInt(fontSize.value),
			fontFamily: fontFamily.value,
			fontWeight: isBold.value ? 'bold' : 'normal',
			fontStyle: isItalic.value ? 'italic' : 'normal',
			underline: isUnderline.value,
			fill: textColor.value,
			textAlign: textAlign.value,
			width: 300,
			editable: true,
		})

		fabricCanvas.add(text)
		fabricCanvas.setActiveObject(text)
		fabricCanvas.renderAll()
	}

	showAddTextDialog.value = false
}

// 删除选中对象
const deleteSelected = () => {
	const activeObject = fabricCanvas.getActiveObject()
	if (activeObject) {
		fabricCanvas.remove(activeObject)
		fabricCanvas.renderAll()
	}
}

// 清空画布
const clearCanvas = () => {
	if (confirm('确定要清空画布吗？')) {
		fabricCanvas.clear()
		fabricCanvas.setBackgroundColor('#ffffff', fabricCanvas.renderAll.bind(fabricCanvas))
	}
}

// 导出为HTML
const exportToHtml = () => {
	const objects = fabricCanvas.getObjects()
	const canvasWidth = fabricCanvas.width
	const canvasHeight = fabricCanvas.height

	let html = '<div style="position: relative; width: 100%; height: 400px; background-color: #ffffff;">\n'

	objects.forEach((obj) => {
		if (obj.type === 'textbox') {
			const percentX = ((obj.left / canvasWidth) * 100).toFixed(2)
			const percentY = ((obj.top / canvasHeight) * 100).toFixed(2)

			// 计算变换
			let transformX = '0'
			if (obj.textAlign === 'center') {
				transformX = '-50%'
			} else if (obj.textAlign === 'right') {
				transformX = '-100%'
			}

			html += `  <div style="position: absolute; left: ${percentX}%; top: ${percentY}%; transform: translate(${transformX}, -50%); color: ${obj.fill}; font-size: ${obj.fontSize}px; font-family: ${obj.fontFamily}; font-weight: ${obj.fontWeight}; font-style: ${obj.fontStyle}; text-decoration: ${obj.underline ? 'underline' : 'none'}; text-align: ${obj.textAlign}; width: ${((obj.width / canvasWidth) * 100).toFixed(2)}%;">${obj.text}</div>\n`
		}
	})

	html += '</div>'

	exportedHtml.value = html
	showExportDialog.value = true
}

// 复制到剪贴板
const copyToClipboard = () => {
	navigator.clipboard
		.writeText(exportedHtml.value)
		.then(() => {
			alert('已复制到剪贴板')
		})
		.catch((err) => {
			console.error('复制失败:', err)
			alert('复制失败，请手动复制')
		})
}
</script>

<style scoped>
.rich-text-editor {
	position: relative;
	border: 1px solid #ccc;
	margin: 20px;
	box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
	border-radius: 4px;
	overflow: hidden;
}

.canvas-container {
	width: 100%;
	height: 400px;
	background-color: white;
}

.controls {
	padding: 10px;
	background: #f5f5f5;
	border-bottom: 1px solid #eee;
	display: flex;
	flex-wrap: wrap;
	gap: 10px;
}

.control-group {
	display: flex;
	align-items: center;
	gap: 5px;
	border-right: 1px solid #ddd;
	padding-right: 10px;
}

.control-group:last-child {
	border-right: none;
}

input[type='color'] {
	vertical-align: middle;
	margin-right: 5px;
	width: 30px;
	height: 30px;
	border: none;
	border-radius: 4px;
	cursor: pointer;
}

select {
	padding: 5px;
	border-radius: 4px;
	border: 1px solid #ddd;
}

button {
	padding: 5px 10px;
	background: white;
	border: 1px solid #ddd;
	border-radius: 4px;
	cursor: pointer;
	font-weight: bold;
	min-width: 30px;
	display: flex;
	align-items: center;
	justify-content: center;
}

button:hover {
	background: #f0f0f0;
}

button.active {
	background: #e0e0e0;
	border-color: #aaa;
}

.align-icon {
	font-size: 12px;
}

.dialog-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 1000;
}

.dialog-content {
	background: white;
	padding: 20px;
	border-radius: 8px;
	width: 80%;
	max-width: 600px;
	display: flex;
	flex-direction: column;
	gap: 15px;
}

.dialog-content h3 {
	margin: 0;
	color: #333;
}

.dialog-content textarea {
	width: 100%;
	height: 150px;
	border: 1px solid #ddd;
	padding: 10px;
	font-family: inherit;
	resize: vertical;
}

.dialog-actions {
	display: flex;
	gap: 10px;
	justify-content: flex-end;
}

.dialog-actions button {
	padding: 8px 15px;
}
</style>
