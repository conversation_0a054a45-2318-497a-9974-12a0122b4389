<template>
	<div class="simple-editor-wrapper">
		<!-- 工具栏 -->
		<div v-if="!hideToolbar" class="toolbar">
			<button
				type="button"
				class="toolbar-btn"
				:class="{ active: isActive('bold') }"
				@click="execCommand('bold')"
				title="粗体"
			>
				<strong>B</strong>
			</button>
			<button
				type="button" 
				class="toolbar-btn"
				:class="{ active: isActive('italic') }"
				@click="execCommand('italic')"
				title="斜体"
			>
				<em>I</em>
			</button>
			<button
				type="button"
				class="toolbar-btn"
				:class="{ active: isActive('underline') }"
				@click="execCommand('underline')"
				title="下划线"
			>
				<u>U</u>
			</button>
			<div class="toolbar-divider"></div>
			<button
				type="button"
				class="toolbar-btn"
				@click="insertImage"
				title="插入图片"
			>
				📷
			</button>
			<input
				ref="imageInput"
				type="file"
				accept="image/*"
				style="display: none"
				@change="handleImageUpload"
			>
		</div>

		<!-- 编辑区域 -->
		<div
			ref="editorRef"
			class="editor-content"
			:style="{ minHeight: height + 'px' }"
			contenteditable="true"
			:placeholder="placeholder"
			@input="handleInput"
			@paste="handlePaste"
			@blur="handleBlur"
			@focus="handleFocus"
			@keydown="handleKeydown"
		></div>
	</div>
</template>

<script setup lang="ts">
const props = defineProps({
	modelValue: {
		type: String,
		default: '',
	},
	height: {
		type: [Number, String],
		default: 300,
	},
	placeholder: {
		type: String,
		default: '请输入内容...',
	},
	hideToolbar: {
		type: Boolean,
		default: false,
	},
})

const emit = defineEmits<{
	(event: 'update:modelValue', val: string): void
	(event: 'blur'): void
	(event: 'focus'): void
}>()

const editorRef = ref<HTMLElement>()
const imageInput = ref<HTMLInputElement>()
let isComposing = false
let ignoreNextInput = false

onMounted(() => {
	if (props.modelValue) {
		setContent(props.modelValue)
	}
})

// 设置内容
const setContent = (content: string) => {
	if (!editorRef.value) return
	
	// 处理换行符，将\r\n和\n转换为<br>标签
	const processedContent = content
		.replace(/\r\n/g, '<br>')
		.replace(/\n/g, '<br>')
		// 移除多余的空段落
		.replace(/<p>\s*<\/p>/g, '')
		.replace(/<p>\s*&nbsp;\s*<\/p>/g, '')
	
	editorRef.value.innerHTML = processedContent
}

// 获取内容
const getContent = (): string => {
	if (!editorRef.value) return ''
	
	let content = editorRef.value.innerHTML
	
	// 清理内容
	content = content
		// 移除空的段落标签
		.replace(/<p>\s*<\/p>/g, '')
		.replace(/<p>\s*&nbsp;\s*<\/p>/g, '')
		// 移除多余的<br>标签
		.replace(/<br\s*\/?>\s*<br\s*\/?>/g, '<br>')
		// 清理首尾空白
		.trim()
	
	return content
}

// 处理输入
const handleInput = () => {
	if (ignoreNextInput) {
		ignoreNextInput = false
		return
	}
	
	if (isComposing) return
	
	const content = getContent()
	emit('update:modelValue', content)
}

// 处理粘贴
const handlePaste = (e: ClipboardEvent) => {
	e.preventDefault()
	
	const clipboardData = e.clipboardData
	if (!clipboardData) return
	
	// 获取纯文本
	const text = clipboardData.getData('text/plain')
	if (text) {
		// 处理换行符
		const processedText = text
			.replace(/\r\n/g, '<br>')
			.replace(/\n/g, '<br>')
		
		// 插入处理后的文本
		document.execCommand('insertHTML', false, processedText)
	}
}

// 处理按键
const handleKeydown = (e: KeyboardEvent) => {
	// 处理Enter键
	if (e.key === 'Enter') {
		e.preventDefault()
		
		// 如果按下Shift+Enter，插入<br>标签
		if (e.shiftKey) {
			document.execCommand('insertHTML', false, '<br>')
		} else {
			// 普通Enter，插入<br><br>实现段落效果
			document.execCommand('insertHTML', false, '<br><br>')
		}
		
		// 触发输入事件
		setTimeout(() => {
			handleInput()
		}, 0)
	}
}

// 处理焦点
const handleFocus = () => {
	emit('focus')
}

const handleBlur = () => {
	emit('blur')
}

// 执行编辑命令
const execCommand = (command: string) => {
	document.execCommand(command, false)
	editorRef.value?.focus()
	
	setTimeout(() => {
		handleInput()
	}, 0)
}

// 检查命令状态
const isActive = (command: string): boolean => {
	try {
		return document.queryCommandState(command)
	} catch {
		return false
	}
}

// 插入图片
const insertImage = () => {
	imageInput.value?.click()
}

// 处理图片上传
const handleImageUpload = async (e: Event) => {
	const target = e.target as HTMLInputElement
	const file = target.files?.[0]
	
	if (!file) return
	
	try {
		// 这里假设你有一个上传图片的工具函数
		const uploadResult = await X_FILE_UTILS.upload({ raw: file })
		
		if (uploadResult && typeof uploadResult === 'string') {
			// 插入图片，不会自动添加<p>&nbsp;</p>
			const imgHtml = `<img src="${uploadResult}" alt="uploaded image" style="max-width: 100%; height: auto;">`
			document.execCommand('insertHTML', false, imgHtml)
			
			setTimeout(() => {
				handleInput()
			}, 0)
		}
	} catch (error) {
		console.error('图片上传失败:', error)
	}
	
	// 清空input值
	target.value = ''
}

// 监听外部值变化
watch(
	() => props.modelValue,
	(newValue) => {
		if (!editorRef.value) return
		
		const currentContent = getContent()
		if (newValue !== currentContent) {
			ignoreNextInput = true
			setContent(newValue)
		}
	}
)

// 暴露方法
defineExpose({
	focus: () => editorRef.value?.focus(),
	blur: () => editorRef.value?.blur(),
	getContent,
	setContent,
})
</script>

<style scoped>
.simple-editor-wrapper {
	border: 1px solid #ddd;
	border-radius: 4px;
	overflow: hidden;
	background: white;
}

.toolbar {
	display: flex;
	align-items: center;
	padding: 8px 12px;
	background: #f8f9fa;
	border-bottom: 1px solid #e9ecef;
	gap: 4px;
}

.toolbar-btn {
	padding: 6px 8px;
	border: 1px solid transparent;
	background: transparent;
	border-radius: 3px;
	cursor: pointer;
	font-size: 14px;
	transition: all 0.2s;
}

.toolbar-btn:hover {
	background: #e9ecef;
	border-color: #ced4da;
}

.toolbar-btn.active {
	background: #007bff;
	color: white;
	border-color: #007bff;
}

.toolbar-divider {
	width: 1px;
	height: 20px;
	background: #dee2e6;
	margin: 0 8px;
}

.editor-content {
	padding: 12px;
	line-height: 1.6;
	font-size: 14px;
	outline: none;
	overflow-y: auto;
	word-wrap: break-word;
}

.editor-content:empty::before {
	content: attr(placeholder);
	color: #6c757d;
	pointer-events: none;
}

.editor-content img {
	max-width: 100%;
	height: auto;
	vertical-align: middle;
	margin: 4px 0;
}

.editor-content br {
	line-height: 1.6;
}

/* 移除默认的contenteditable样式 */
.editor-content[contenteditable="true"]:focus {
	outline: none;
}
</style> 