<template>
	<div class="relative w-full overflow-hidden rounded border-inputBorder">
		<div
			v-if="placeholderVisible && !modelValue"
			class="pointer-events-none absolute z-1 hfull wfull flex items-center justify-center bg-white text-inputPlaceholder"
		>
			{{ placeholder }}
		</div>
		<Editor
			:id="tinymceId"
			v-model="tinymceHtml"
			:disabled="params?.disabled"
			:init="init"
			@blur="$emit('blur')"
			@focus="placeholderVisible = false"
			@change="$emit('change')"
		/>
	</div>
</template>
<script setup lang="ts">
import Editor from '@tinymce/tinymce-vue'
const props = defineProps({
	modelValue: {
		type: String,
		default: '',
	},
	height: {
		type: [Number, String],
		default: 300,
	},
	params: {
		type: Object,
		default: () => ({}),
	},
	placeholder: {
		type: String,
		default: '请输入',
	},
})
const placeholderVisible = ref(true)
const isPropsChange = ref(false)
const tinymceHtml = ref<string>(props.modelValue)
const tinymceId = X_COMMON_UTILS.generateUUID()
watch(
	() => props.modelValue,
	async (nv) => {
		if (nv !== tinymceHtml.value && !(tinymceHtml.value.length > 1)) {
			isPropsChange.value = true
			await nextTick()
			tinymceHtml.value = nv || ''
			setTimeout(() => {
				isPropsChange.value = false
			}, 50)
		}
	},
)

const init = {
	//初始化数据
	selector: `#${tinymceId}`,
	skin: 'oxide',
	content_style: 'body {font-size:14px; line-height:1.2;} img {display: inline-block; vertical-align: middle; max-width: 100%; height: auto;}',
	language: 'zh-Hans', // 这里名称根据 zh-Hans.js 里面写的名称而定
	height: props.height, // 限制高度
	statusbar: false, // 显示下方操作栏
	image_dimensions: false, // 禁止操作图片
	plugins: 'code quickbars link lists image media imagetools code table wordcount preview', // 富文本插件
	font_size_formats: '0.5rem 0.8rem 1rem 1.2rem 1.5rem 1.7rem 2rem', // 字体大小文本
	font_family_formats:
		'微软雅黑=Microsoft YaHei,Helvetica Neue,PingFang SC,sans-serif;苹果苹方=PingFang SC,Microsoft YaHei,sans-serif;宋体=simsun,serif', // 字体选择配置
	// toolbar: 'fontsize forecolor undo redo alignleft aligncenter alignright removeformat lineheight code media link preview', // 菜单栏配置
	toolbar: false,
	quickbars_selection_toolbar: 'fontsize | lineheight forecolor bold italic underline strikethrough removeformat',
	branding: false, // //是否禁用"Powered by TinyMCE"
	menubar: false, //顶部菜单栏显示

	// 图片相关配置
	image_caption: false, // 禁用图片标题
	image_title: false, // 禁用图片标题属性
	image_description: false, // 禁用图片描述

	// 修复换行符和空标签问题的配置
	remove_trailing_brs: false, // 允许保留尾部的<br>标签
	remove_linebreaks: false, // 保留换行符
	force_br_newlines: false, // 不强制使用<br>换行
	force_p_newlines: true, // 使用<p>标签换行
	convert_newlines_to_brs: true, // 将\r\n转换为<br>标签
	preformatted: false,
	keep_styles: false,

	// 防止生成空的<p>&nbsp;</p>标签
	forced_root_block: 'p',
	forced_root_block_attrs: {},
	force_html_entities: false,

	// 自定义换行符处理
	formats: {
		// 保留换行格式
		preservelinebreaks: { inline: 'span', styles: { 'white-space': 'pre-wrap' } },
	},

	setup: function (editor) {
		// 处理内容设置时的换行符转换
		editor.on('BeforeSetContent', function (e) {
			if (e.content) {
				// 将\r\n和\n转换为<br>标签，但保留在<p>标签内的结构
				e.content = e.content
					.replace(/\r\n/g, '<br>')
					.replace(/\n/g, '<br>')
					// 避免生成空的<p>&nbsp;</p>
					.replace(/<p>\s*&nbsp;\s*<\/p>/g, '')
					.replace(/<p>\s*<\/p>/g, '')
			}
		})

		// 处理获取内容时保持换行符
		editor.on('GetContent', function (e) {
			if (e.content) {
				// 将<br>标签转换回换行符（如果需要）
				// e.content = e.content.replace(/<br\s*\/?>/gi, '\n')
			}
		})

		// 处理图片插入，确保图片为行内显示
		editor.on('BeforeSetContent', function (e) {
			if (e.content && typeof e.content === 'string' && e.content.includes('<img')) {
				// 确保图片标签包含行内样式
				e.content = e.content.replace(/<img([^>]*)>/gi, function (match, attrs) {
					// 如果已经有style属性，添加到现有样式中
					if (attrs.includes('style=')) {
						return match.replace(/style="([^"]*)"/, function (styleMatch, existingStyle) {
							const inlineStyle = 'display: inline-block; vertical-align: middle; max-width: 100%; height: auto;'
							return `style="${existingStyle}; ${inlineStyle}"`
						})
					} else {
						// 如果没有style属性，添加新的style属性
						return `<img${attrs} style="display: inline-block; vertical-align: middle; max-width: 100%; height: auto;">`
					}
				})
			}
		})

		// 防止在图片前自动添加<p>&nbsp;</p>
		editor.on('BeforeExecCommand', function (e) {
			if (e.command === 'mceInsertContent') {
				const content = e.value
				if (content && typeof content === 'string' && content.includes('<img')) {
					// 如果插入的是图片，确保不会在前面添加空段落
					editor.selection.setContent(content, { merge: false })
					e.preventDefault()
				}
			}
		})

		// 处理粘贴的图片，确保为行内显示
		editor.on('PastePostProcess', function (e) {
			const images = e.node.querySelectorAll('img')
			images.forEach(function (img) {
				img.style.display = 'inline-block'
				img.style.verticalAlign = 'middle'
				img.style.maxWidth = '100%'
				img.style.height = 'auto'
			})
		})
	},
	images_upload_handler: function (blobInfo) {
		return X_FILE_UTILS.upload({ raw: blobInfo.blob() })
	},
	...props.params,
}

const emits = defineEmits<{
	(event: 'update:modelValue', val: string): void // 富文本内容
	(event: 'blur'): void // 失去焦点
}>()

// 双向绑定
watch(
	() => tinymceHtml.value,
	async (nv) => {
		if (isPropsChange.value) return
		if (nv) {
			// 清理内容，移除不必要的空标签和格式化
			let cleanContent = nv
				// 移除空的<p>&nbsp;</p>标签
				.replace(/<p>\s*&nbsp;\s*<\/p>/g, '')
				.replace(/<p>\s*<\/p>/g, '')
				// 清理p标签之间的多余空格，但保留换行符
				.replace(/<\/p>\s*<p>/g, '</p><p>')
			// 保留<br>标签，不进行额外处理

			if (nv !== cleanContent) {
				await nextTick()
				emits('update:modelValue', cleanContent)
			} else {
				emits('update:modelValue', nv)
			}
		} else {
			emits('update:modelValue', nv)
		}
	},
	{ immediate: false },
)
</script>
<style>
#tinymce body {
	margin: 0 !important;
}
p {
	margin: 0 !important;
}
.tox-tinymce {
	border-radius: 0 !important;
	border: none !important;
}
.tox:not(.tox-tinymce-inline) .tox-editor-header {
	box-shadow: none !important;
	border-bottom: none !important;
}

.tox-tinymce img {
	display: inline-block !important;
	vertical-align: middle !important;
	max-width: 100% !important;
	height: auto !important;
}
</style>
