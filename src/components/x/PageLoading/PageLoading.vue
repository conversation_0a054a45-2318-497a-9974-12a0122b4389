<script setup lang="ts">
const state = reactive({
	visible: false,
})
const text = ref('加载中...')

const show = (_text) => {
	if (_text) text.value = _text
	state.visible = true
}

const hide = () => {
	state.visible = false
}

defineExpose({ show, hide })
</script>

<template>
	<Teleport to="body">
		<div v-if="state.visible" class="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
			<div class="flex flex-col items-center gap-4">
				<div class="i-svg-spinners:blocks-scale h-12 w-12 text-white"></div>
				<div class="text-white text-sm">{{ text }}</div>
			</div>
		</div>
	</Teleport>
</template>

<style scoped></style>
