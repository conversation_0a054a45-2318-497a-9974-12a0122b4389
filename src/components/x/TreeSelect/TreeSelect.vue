<template>
	<x-popup ref="popupRef" :only-panel="onlyPanel">
		<div class="min-h-2rem flex items-center rounded bg-white py-xxs border-box border-inputBorder">
			<template v-if="!multiple">
				<x-input
					ref="inputRef"
					v-model="searchKeyword"
					class="!border-none"
					:placeholder="displayValue || placeholder"
					:readonly="!filterable"
					@input="handleSearchInput"
				/>
			</template>
			<template v-else>
				<div class="hfull flex flex-wrap items-center pl-xxs">
					<div v-if="Array.isArray(displayValue) && displayValue.length > 0" class="flex flex-wrap gap-xxs">
						<span v-for="tag in displayValue" :key="tag" class="flex shrink-0 items-center rounded bg-page px-xxs">
							<span class="text-xs">{{ tag.label }}</span>
							<span class="cursor-pointer ml-xxs" @click.stop="removeSelectedNode(tag)">×</span>
						</span>
					</div>
					<x-input
						v-if="filterable"
						ref="inputRef"
						v-model="searchKeyword"
						class="flex-1 !border-none"
						:placeholder="!displayValue.length ? placeholder : ''"
						:readonly="!filterable"
						@input="handleSearchInput"
					/>
				</div>
			</template>
		</div>
		<template #content>
			<div class="max-h-30rem overflow-y-auto rounded bg-white">
				<x-tree
					ref="treeRef"
					v-bind="$attrs"
					:data="data"
					:model-value="modelValue"
					:multiple="multiple"
					:check-strictly="checkStrictly"
					:filterable="filterable"
					:loading="loading"
					:load-data="loadData"
					:accordion="accordion"
					:expand-selected="expandSelected"
					:leaf-only="leafOnly"
					:lazy-load-retries="lazyLoadRetries"
					:lazy-load-retry-delay="lazyLoadRetryDelay"
					:path-node-key="pathNodeKey"
					:label-key="labelKey"
					:value-key="valueKey"
					:include-half-checked="includeHalfChecked"
					@node-click="handleNodeClick"
					@node-check="handleNodeCheck"
					@node-expand="handleNodeExpand"
					@update:model-value="handleUpdateModelValue"
					@lazy-load-error="handleLazyLoadError"
				></x-tree>
			</div>
		</template>
	</x-popup>
</template>
<script setup lang="ts">
const props = defineProps({
	modelValue: {
		type: [String, Number, Object, Array],
		default: () => null,
	},
	data: {
		type: Array,
		default: () => [],
	},
	multiple: {
		type: Boolean,
		default: false,
	},
	checkStrictly: {
		type: Boolean,
		default: false,
	},
	filterable: {
		type: Boolean,
		default: false,
	},
	loading: {
		type: Boolean,
		default: false,
	},
	loadData: {
		type: Function,
		default: null,
	},
	accordion: {
		type: Boolean,
		default: true,
	},
	expandSelected: {
		type: Boolean,
		default: false,
	},
	leafOnly: {
		type: Boolean,
		default: false,
	},
	lazyLoadRetries: {
		type: Number,
		default: 3,
	},
	lazyLoadRetryDelay: {
		type: Number,
		default: 1000,
	},
	pathNodeKey: {
		type: String,
		default: 'path',
	},
	labelKey: {
		type: String,
		default: 'name',
	},
	valueKey: {
		type: String,
		default: 'id',
	},
	placeholder: {
		type: String,
		default: '请选择',
	},
	clearable: {
		type: Boolean,
		default: true,
	},
	// 选择后关闭
	closeOnSelect: {
		type: Boolean,
		default: true,
	},
	onlyPanel: {
		type: Boolean,
		default: false,
	},
	// 是否将半选状态的节点包含在modelValue中
	includeHalfChecked: {
		type: Boolean,
		default: false,
	},
})

const emit = defineEmits(['update:modelValue', 'change', 'node-click', 'node-check', 'node-expand', 'lazy-load-error', 'clear'])

// 组件引用
const popupRef = ref(null)
const inputRef = ref(null)
const treeRef = ref(null)

// 状态
const searchKeyword = ref('')

// 计算属性：显示值
const displayValue = computed(() => {
	if (props.modelValue === null || props.modelValue === undefined) {
		return props.multiple ? [] : ''
	}

	if (props.multiple && Array.isArray(props.modelValue)) {
		// 多选模式，返回标签数组
		return props.modelValue
			.map((value) => {
				const node = treeRef.value?.findNodeByValue(value)
				return node ? { value: node.value, label: node.label } : null
			})
			.filter(Boolean)
	} else {
		// 单选模式，返回标签字符串
		const node = treeRef.value?.findNodeByValue(props.modelValue)
		return node ? node.label : ''
	}
})

// 方法
// 处理搜索输入，直接使用Tree组件的debounceFilter方法
const handleSearchInput = () => {
	if (treeRef.value) {
		// 先更新Tree组件的searchKeyword
		treeRef.value.searchKeyword = searchKeyword.value
		// 然后调用Tree组件的debounceFilter方法
		treeRef.value.debounceFilter()
	}
}

// 处理节点点击
const handleNodeClick = (node) => {
	emit('node-click', node)
}

// 处理节点选中
const handleNodeCheck = (node) => {
	emit('node-check', node)
	/*	if (props.closeOnSelect) {
		// 单选，没有子节点
		if (!props.multiple && !treeRef.value?.hasChildren(node)) {
			close()
		}
	}*/
}

// 处理节点展开
const handleNodeExpand = (node) => {
	emit('node-expand', node)
}

// 处理模型值更新
const handleUpdateModelValue = (value) => {
	emit('update:modelValue', value)
	emit('change', value)
}

// 处理懒加载错误
const handleLazyLoadError = (node, error) => {
	emit('lazy-load-error', node, error)
}

// 移除选中的节点（多选模式）
const removeSelectedNode = (tag) => {
	if (!props.multiple || !Array.isArray(props.modelValue)) return

	const newValue = props.modelValue.filter((value) => value !== tag.value)
	emit('update:modelValue', newValue)
	emit('change', newValue)
}

// 清空选择
const clear = () => {
	const newValue = props.multiple ? [] : null
	emit('update:modelValue', newValue)
	emit('change', newValue)
	emit('clear')
}

// 打开下拉菜单
const open = () => {
	popupRef.value?.open()
}

// 关闭下拉菜单
const close = () => {
	popupRef.value?.close()
}

// 监听搜索关键字变化
watch(searchKeyword, (newVal) => {
	if (newVal === '' && treeRef.value) {
		// 当搜索框清空时，重置树的过滤状态
		treeRef.value.searchKeyword = ''
		// 触发树组件的过滤方法，以清除过滤状态
		treeRef.value.debounceFilter()
	}
})

// 暴露方法
defineExpose({
	open,
	close,
	clear,
	// 暴露树组件的方法
	getChildNodeValues: () => treeRef.value?.getChildNodeValues,
	getParentNodeValues: () => treeRef.value?.getParentNodeValues,
	findNodeByValue: () => treeRef.value?.findNodeByValue,
	findNodeByFullPath: () => treeRef.value?.findNodeByFullPath,
})
</script>

<style scoped></style>
