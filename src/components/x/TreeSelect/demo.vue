<!-- TreeSelectDemo.vue -->
<template>
	<div class="tree-select-demo h-500px overflow-y-auto">
		<h1>树选择组件测试 Demo</h1>

		<div class="demo-container">
			<!-- 基础用法 -->
			<div class="demo-section">
				<h2>11. 基础用法</h2>
				<div class="demo-content">
					{{ basicSelected }}=-=
					<x-tree-select v-model="basicSelected" check-strictly :data="basicData" placeholder="请选择水果或蔬菜"></x-tree-select>
				</div>
				<div class="demo-info">
					<p>当前选中: {{ basicSelected }}</p>
					<p v-if="lastChangeValue">最近一次变更: {{ lastChangeValue }}</p>
				</div>
			</div>

			<!-- 多选模式 -->
			<div class="demo-section">
				<h2>2. 多选模式11</h2>
				<div class="demo-content">
					<x-tree-select v-model="multipleSelected" :data="basicData" multiple placeholder="请选择多个选项"></x-tree-select>
				</div>
				<div class="demo-info">
					<p>当前选中: {{ multipleSelected }}</p>
					<button @click="clearSelection('multipleSelected')">清空选择</button>
					<button @click="selectFruits">选择所有水果</button>
				</div>
			</div>

			<!-- 可清空 -->
			<div class="demo-section">
				<h2>3. 可清空</h2>
				<div class="demo-content">
					<x-tree-select v-model="clearableSelected" :data="basicData" clearable placeholder="可清空的选择器"></x-tree-select>
				</div>
				<div class="demo-info">
					<p>当前选中: {{ clearableSelected }}</p>
					<p>点击选择框右侧的 × 图标可清空选择</p>
				</div>
			</div>

			<!-- 严格模式 -->
			<div class="demo-section">
				<h2>4. 严格模式 (父子节点不关联)</h2>
				<div class="demo-content">
					<x-tree-select v-model="strictlySelected" :data="basicData" multiple check-strictly placeholder="父子节点选择不关联"></x-tree-select>
				</div>
				<div class="demo-info">
					<p>当前选中: {{ strictlySelected }}</p>
				</div>
			</div>

			<!-- 仅叶子节点可选 -->
			<div class="demo-section">
				<h2>5. 仅叶子节点可选</h2>
				<div class="demo-content">
					<x-tree-select v-model="leafOnlySelected" :data="basicData" multiple leaf-only placeholder="只能选择叶子节点"></x-tree-select>
				</div>
				<div class="demo-info">
					<p>当前选中: {{ leafOnlySelected }}</p>
				</div>
			</div>

			<!-- 可搜索 -->
			<div class="demo-section">
				<h2>6. 可搜索</h2>
				<div class="demo-content">
					<x-tree-select v-model="filterableSelected" :data="basicData" filterable placeholder="输入关键字搜索"></x-tree-select>
				</div>
				<div class="demo-info">
					<p>当前选中: {{ filterableSelected }}</p>
				</div>
			</div>

			<!-- 手风琴模式 -->
			<div class="demo-section">
				<h2>7. 手风琴模式</h2>
				<div class="demo-content">
					<x-tree-select v-model="accordionSelected" :data="basicData" accordion placeholder="同级节点只能展开一个"></x-tree-select>
				</div>
				<div class="demo-info">
					<p>当前选中: {{ accordionSelected }}</p>
				</div>
			</div>

			<!-- 懒加载 -->
			<div class="demo-section">
				<h2>8. 懒加载</h2>
				<div class="demo-content">
					<x-tree-select v-model="lazySelected" :data="lazyData" :load-data="loadLazyData" placeholder="点击节点动态加载子节点"></x-tree-select>
				</div>
				<div class="demo-info">
					<p>当前选中: {{ lazySelected }}</p>
					<p v-if="lazyLoadError" class="error-message">加载错误: {{ lazyLoadError }}</p>
					<button @click="toggleLazyLoadSuccess">{{ lazyLoadSuccess ? '模拟加载失败' : '模拟加载成功' }}</button>
				</div>
			</div>

			<!-- 显示完整路径 -->
			<div class="demo-section">
				<h2>9. 显示完整路径</h2>
				<div class="demo-content">
					<x-tree-select v-model="fullPathSelected" :data="basicData" show-full-path placeholder="选择后显示完整路径"></x-tree-select>
				</div>
				<div class="demo-info">
					<p>当前选中: {{ fullPathSelected }}</p>
				</div>
			</div>

			<!-- 选择后关闭 -->
			<div class="demo-section">
				<h2>10. 选择后关闭</h2>
				<div class="demo-content">
					<x-tree-select
						v-model="closeOnSelectSelected"
						:data="basicData"
						multiple
						close-on-select
						placeholder="选择后自动关闭下拉框"
					></x-tree-select>
				</div>
				<div class="demo-info">
					<p>当前选中: {{ closeOnSelectSelected }}</p>
				</div>
			</div>

			<!-- 允许创建新选项 -->
			<div class="demo-section">
				<h2>11. 允许创建新选项</h2>
				<div class="demo-content">
					<x-tree-select
						v-model="allowCreateSelected"
						:data="basicData"
						allow-create
						filterable
						placeholder="输入并回车创建新选项"
						@create-option="handleCreateOption"
					></x-tree-select>
				</div>
				<div class="demo-info">
					<p>当前选中: {{ allowCreateSelected }}</p>
					<p v-if="lastCreatedOption">最近创建: {{ lastCreatedOption.label }}</p>
				</div>
			</div>

			<!-- 自定义下拉框高度 -->
			<div class="demo-section">
				<h2>12. 自定义下拉框高度</h2>
				<div class="demo-content">
					<x-tree-select
						v-model="customHeightSelected"
						:data="basicData"
						:dropdown-max-height="150"
						placeholder="下拉框高度限制为150px"
					></x-tree-select>
				</div>
				<div class="demo-info">
					<p>当前选中: {{ customHeightSelected }}</p>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup>
// 基础数据
const basicData = [
	{
		label: '水果',
		value: 'fruits',
		children: [
			{
				label: '苹果',
				value: 'apple',
			},
			{
				label: '香蕉',
				value: 'banana',
			},
			{
				label: '橙子',
				value: 'orange',
				children: [
					{
						label: '血橙',
						value: 'blood-orange',
					},
					{
						label: '脐橙',
						value: 'navel-orange',
					},
				],
			},
		],
	},
	{
		label: '蔬菜',
		value: 'vegetables',
		children: [
			{
				label: '番茄',
				value: 'tomato',
			},
			{
				label: '土豆',
				value: 'potato',
			},
			{
				label: '胡萝卜',
				value: 'carrot',
			},
		],
	},
	{
		label: '肉类',
		value: 'meat',
		children: [
			{
				label: '牛肉',
				value: 'beef',
			},
			{
				label: '猪肉',
				value: 'pork',
			},
			{
				label: '鸡肉',
				value: 'chicken',
				disabled: true,
			},
		],
	},
]

// 懒加载数据
const lazyData = [
	{
		label: '动态加载1',
		value: 'lazy-1',
		hasChildren: true,
	},
	{
		label: '动态加载2',
		value: 'lazy-2',
		hasChildren: true,
	},
	{
		label: '动态加载3',
		value: 'lazy-3',
		hasChildren: true,
	},
]

// 各个选择器的选中值
const basicSelected = ref('')
const multipleSelected = ref([])
const clearableSelected = ref('')
const strictlySelected = ref([])
const leafOnlySelected = ref([])
const filterableSelected = ref('')
const accordionSelected = ref('')
const lazySelected = ref('')
const fullPathSelected = ref('')
const closeOnSelectSelected = ref([])
const allowCreateSelected = ref('')
const customHeightSelected = ref('')

// 事件处理相关
const lastChangeValue = ref(null)
const lastCreatedOption = ref(null)

// 懒加载相关
const lazyLoadSuccess = ref(true)
const lazyLoadError = ref('')

// 事件处理
const handleChange = (value) => {
	lastChangeValue.value = value
	console.log('选择变更:', value)
}

const handleCreateOption = (option) => {
	lastCreatedOption.value = option
	console.log('创建新选项:', option)
}

// 懒加载数据
const loadLazyData = (node) => {
	return new Promise((resolve, reject) => {
		setTimeout(() => {
			if (lazyLoadSuccess.value) {
				const children = []
				for (let i = 1; i <= 3; i++) {
					children.push({
						label: `${node.label}-子节点${i}`,
						value: `${node.value}-child-${i}`,
						hasChildren: i === 1, // 只有第一个子节点有子节点
					})
				}
				resolve(children)
			} else {
				reject(new Error('模拟加载失败'))
			}
		}, 1000)
	})
}

// 切换懒加载成功/失败
const toggleLazyLoadSuccess = () => {
	lazyLoadSuccess.value = !lazyLoadSuccess.value
	lazyLoadError.value = ''
}

// 清空选择
const clearSelection = (refName) => {
	if (refName === 'multipleSelected') {
		multipleSelected.value = []
	}
}

// 选择所有水果
const selectFruits = () => {
	multipleSelected.value = ['fruits', 'apple', 'banana', 'orange', 'blood-orange', 'navel-orange']
}
</script>

<style scoped>
.tree-select-demo {
	font-family: Arial, sans-serif;
	max-width: 1200px;
	margin: 0 auto;
	padding: 20px;
}

h1 {
	text-align: center;
	color: #409eff;
	margin-bottom: 30px;
}

.demo-container {
	display: grid;
	grid-template-columns: repeat(auto-fill, minmax(500px, 1fr));
	gap: 20px;
}

.demo-section {
	border: 1px solid #e4e7ed;
	border-radius: 4px;
	overflow: hidden;
}

h2 {
	margin: 0;
	padding: 10px 15px;
	background-color: #f5f7fa;
	border-bottom: 1px solid #e4e7ed;
	font-size: 16px;
	color: #303133;
}

.demo-content {
	padding: 15px;
	min-height: 100px;
	border-bottom: 1px solid #e4e7ed;
}

.demo-info {
	padding: 15px;
	background-color: #f8f8f8;
}

button {
	margin-right: 8px;
	margin-bottom: 8px;
	padding: 6px 12px;
	background-color: #409eff;
	color: white;
	border: none;
	border-radius: 4px;
	cursor: pointer;
	transition: background-color 0.3s;
}

button:hover {
	background-color: #66b1ff;
}

.error-message {
	color: #f56c6c;
	margin-top: 10px;
}
</style>
