<script setup lang="ts">
defineProps({
	company: {
		type: String,
		default: '',
	},
})

const text = '提供技术支持'
</script>

<template>
	<div class="copyright-container pointer-events-none">
		<div class="copyright-content">
			<span class="company">
				<span v-for="(char, index) in company" :key="index" class="char" :style="{ animationDelay: `${index * 0.08}s` }">
					{{ char }}
				</span>
			</span>
			<span class="text">
				<span v-for="(char, index) in text" :key="index" class="char" :style="{ animationDelay: `${(index + company.length) * 0.08}s` }">
					{{ char }}
				</span>
			</span>
		</div>
	</div>
</template>

<style scoped>
.copyright-container {
	width: 100%;
	padding: 5px 0;
	display: flex;
	justify-content: center;
	align-items: center;
}

.copyright-content {
	font-size: 11px;
	color: #999;
	display: flex;
	align-items: center;
	gap: 8px;
	animation: fadeIn 1s ease-in-out;

	.company {
		display: flex;
		align-items: center;
		gap: 1px;
		color: #a9b4bc;
		font-weight: bold;
		position: relative;

		.char {
			display: inline-block;
			animation: wave 3s ease-in-out infinite;
		}
	}

	.text {
		display: flex;
		align-items: center;
		gap: 1px;

		.char {
			display: inline-block;
			animation: wave 3s ease-in-out infinite;
		}
	}
}

@keyframes fadeIn {
	from {
		opacity: 0;
		transform: translateY(10px);
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

@keyframes wave {
	0%,
	100% {
		transform: translateY(0);
	}
	25% {
		transform: translateY(-2px);
	}
	50% {
		transform: translateY(0);
	}
	75% {
		transform: translateY(2px);
	}
}
</style>
