<!-- RadioDemo.vue -->
<template>
  <div class="radio-demo p-4">
    <h2 class="text-xl font-bold mb-4">单选组件示例</h2>
    
    <div class="mb-6">
      <h3 class="text-lg font-semibold mb-2">默认样式</h3>
      <XRadioGroup v-model="selectedDefault" :options="options" variant="default" />
      <div class="mt-2">选中值: {{ selectedDefault }}</div>
    </div>
    
    <div class="mb-6">
      <h3 class="text-lg font-semibold mb-2">卡片样式</h3>
      <XRadioGroup v-model="selectedCard" :options="options" variant="card" />
      <div class="mt-2">选中值: {{ selectedCard }}</div>
    </div>
    
    <div class="mb-6">
      <h3 class="text-lg font-semibold mb-2">滑块样式</h3>
      <XRadioGroup v-model="selectedSlider" :options="options" variant="slider" />
      <div class="mt-2">选中值: {{ selectedSlider }}</div>
    </div>
    
    <div class="mb-6">
      <h3 class="text-lg font-semibold mb-2">独立使用的单选按钮</h3>
      <div class="flex gap-4">
        <XRadio value="1" label="选项1" variant="default" v-model="singleRadio" />
        <XRadio value="2" label="选项2" variant="default" v-model="singleRadio" />
        <XRadio value="3" label="选项3" variant="default" v-model="singleRadio" />
      </div>
      <div class="mt-2">选中值: {{ singleRadio }}</div>
    </div>
    
    <div class="mb-6">
      <h3 class="text-lg font-semibold mb-2">自定义内容的卡片样式</h3>
      <XRadioGroup v-model="selectedCustomCard" variant="card">
        <XRadio v-for="(item, index) in customOptions" :key="index" :value="item.value" variant="card">
          <div class="flex flex-col items-center">
            <div class="text-lg font-bold">{{ item.title }}</div>
            <div class="text-sm text-gray-500">{{ item.description }}</div>
            <div class="mt-2 text-primary">{{ item.price }}</div>
          </div>
        </XRadio>
      </XRadioGroup>
      <div class="mt-2">选中值: {{ selectedCustomCard }}</div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import XRadioGroup from '../RadioGroup/RadioGroup.vue'
import XRadio from './Radio.vue'

// 基本选项
const options = [
  { id: '1', name: '选项1' },
  { id: '2', name: '选项2' },
  { id: '3', name: '选项3' },
]

// 自定义内容选项
const customOptions = [
  { 
    value: 'basic', 
    title: '基础版', 
    description: '适合个人使用', 
    price: '¥99/月' 
  },
  { 
    value: 'pro', 
    title: '专业版', 
    description: '适合小型团队', 
    price: '¥199/月' 
  },
  { 
    value: 'enterprise', 
    title: '企业版', 
    description: '适合大型企业', 
    price: '¥499/月' 
  },
]

// 选中值
const selectedDefault = ref('1')
const selectedCard = ref('1')
const selectedSlider = ref('1')
const singleRadio = ref('1')
const selectedCustomCard = ref('basic')
</script>

<style scoped>
.radio-demo {
  max-width: 800px;
  margin: 0 auto;
}
</style> 