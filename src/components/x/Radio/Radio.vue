<!-- Radio.vue -->
<template>
	<label class="relative flex select-none items-center gap-xxs" :class="[disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer']">
		<input
			type="radio"
			class="peer absolute left-0 cursor-inherit op0"
			:checked="isChecked"
			:disabled="disabled"
			:value="value"
			:name="radioName"
			@change="handleChange"
		/>
		<!-- peer-focus-visible:(ring-2 ring-primary-light)-->
		<span
			class="h1rem w1rem flex items-center justify-center rounded-full transition-all duration-200 border-aid after:(h0.5rem w0.5rem scale-0 rounded-full bg-white content-empty) peer-disabled:(border-gray-300 bg-gray-100) peer-checked:(bg-primary border-primary after:scale-100)"
		></span>
		<span class="relative text-content text-sm">
			<slot>{{ label }}</slot>
			<span
				v-if="badge"
				class="absolute h-1.2rem min-w-1.2rem flex items-center justify-center rounded-full bg-primary px-0.3rem text-0.7rem text-white -right-2 -top-2"
			>
				{{ badge }}
			</span>
		</span>
	</label>
</template>

<script setup>
const props = defineProps({
	value: { type: [String, Number, Boolean], required: true },
	label: { type: String, default: '' },
	disabled: { type: Boolean, default: false },
	modelValue: { type: [String, Number, Boolean], default: undefined },
	name: { type: String, default: () => `radio-${Math.random().toString(36).substring(2, 9)}` }, // 默认生成随机name
	badge: { type: [String, Number], default: '' }, // 添加数量角标
})

const emit = defineEmits(['update:modelValue'])

// 获取Group上下文（当在Group中使用时）
const groupContext = inject('radioGroup', null)

// 计算radio的name属性
const radioName = computed(() => {
	if (groupContext && groupContext.name) {
		return groupContext.name
	}
	return props.name
})

// 计算当前是否选中
const isChecked = computed(() => {
	if (groupContext) {
		return unref(groupContext.modelValue) == props.value
	}
	return props.modelValue == props.value
})

// 处理选择变化
const handleChange = (e) => {
	if (props.disabled) return

	const newValue = props.value
	if (groupContext) {
		groupContext.modelValue.value = newValue
		groupContext.onChange?.(newValue)
	} else {
		emit('update:modelValue', newValue)
	}
}
</script>
