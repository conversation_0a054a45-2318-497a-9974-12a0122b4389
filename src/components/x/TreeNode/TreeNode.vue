<!-- TreeNode.vue -->
<template>
	<div
		class="h2.5rem min-w-10rem cursor-pointer transition text-sm pl-lg pr-sm"
		:class="[
			{
				'color-primary-80': isSelected || isExpanded,
				'color-danger': node.loadError,
				'cursor-default color-disabled': node.disabled,
				'color-aid': isLoading,
			},
			isChecked ? 'color-primary' : 'hover:bg-hover',
			{ 'bg-primaryBg border-r-primary !b-r-4px rounded-md overflow-hidden': !multiple && isChecked },
		]"
		@click.stop="handleClick"
	>
		<!-- 节点内容区 -->
		<div :style="{ paddingLeft: `${node.level * 18}px` }" class="hfull flex items-center justify-between">
			<!-- 复选框 - 多选模式下显示 -->
			<div v-if="multiple" class="mr-xs">
				<x-checkbox
					:disabled="node.disabled || (leafOnly && hasChildren)"
					:model-value="isChecked"
					:indeterminate="isIndeterminate"
					@click.stop
					@change="handleCheck"
				/>
			</div>

			<!-- 节点标签内容 -->
			<span class="relative flex flex-1 items-center overflow-hidden truncate whitespace-nowrap text-left transition">
				<!-- 节点图标（如果有） -->
				<div :class="`${node.icon || 'i-carbon:folder'}`" class="shrink-0 text-lg mr-xs"></div>
				<!-- 节点标签 - 根据是否搜索显示高亮或普通文本 -->
				<span v-if="searchKeyword" v-html="highlightKeyword(node.label)"></span>
				<span v-else>{{ node.label }}</span>

				<!-- 加载错误提示 -->
				<span
					v-if="node.loadError"
					class="w-1.5rem inline-flex items-center justify-center color-danger ml-xxs border-danger"
					title="加载失败，点击重试"
				>
					!
				</span>
			</span>

			<!-- 展开/折叠图标 -->
			<div class="hfull w-1rem inline-flex items-center justify-center">
				<span v-if="hasChildren" class="wfull cursor-pointer">
					<!-- 加载中状态 -->
					<div v-if="isLoading" v-loading="true" class="h80% wfull"></div>
					<!-- 展开/折叠箭头 -->
					<XIconsChevronRight v-else class="transition" :class="[{ 'rotate-90deg': isExpanded }]" />
				</span>
				<span v-else class="wfull"></span>
			</div>
		</div>
	</div>

	<!-- 子节点区域 - 使用v-show代替v-if提高性能 -->
	<div v-show="isExpanded && node.children && node.children.length" class="wfull">
		<x-tree-node
			v-for="child in node.children"
			:key="child.value"
			:node="child"
			:multiple="multiple"
			:check-strictly="checkStrictly"
			:leaf-only="leafOnly"
			:model-value="modelValue"
			:search-keyword="searchKeyword"
			:include-half-checked="includeHalfChecked"
		></x-tree-node>
	</div>
</template>

<script setup>
/**
 * 树节点组件
 * 用于渲染树形结构中的单个节点及其子节点
 */

// 组件属性定义
const props = defineProps({
	// 节点数据
	node: {
		type: Object,
		required: true,
	},
	// 是否多选模式
	multiple: {
		type: Boolean,
		default: false,
	},
	// 在父子节点选中状态不再关联的情况下，勾选节点不会影响父子节点
	checkStrictly: {
		type: Boolean,
		default: false,
	},
	// 当前选中的值
	modelValue: {
		type: [String, Number, Object, Array],
		default: null,
	},
	// 搜索关键词，用于高亮显示
	searchKeyword: {
		type: String,
		default: '',
	},
	// 是否仅叶子节点可选
	leafOnly: {
		type: Boolean,
		default: false,
	},
	// 是否将半选状态的节点包含在modelValue中
	includeHalfChecked: {
		type: Boolean,
		default: false,
	},
})

// 注入树组件提供的方法和状态
const tree = inject('tree', {})

// 计算属性 - 节点状态
const isExpanded = computed(() => tree.isNodeExpanded(props.node))
const isChecked = computed(() => tree.isNodeChecked(props.node))
const isLoading = computed(() => tree.isNodeLoading(props.node))
const isIndeterminate = computed(() => tree.isNodeIndeterminate(props.node))
const isSelected = computed(() => tree.isNodeSelected(props.node))
const hasChildren = computed(() => tree.hasChildren(props.node))

// 节点操作方法
const handleClick = () => tree.handleNodeClick(props.node)
const handleCheck = () => tree.handleNodeCheck(props.node)

/**
 * 高亮搜索关键词
 * 将匹配的文本部分用红色标记
 * @param {string} text - 原始文本
 * @returns {string} 包含高亮HTML的文本
 */
const highlightKeyword = (text) => {
	if (!text || !props.searchKeyword) return text

	try {
		const keyword = props.searchKeyword.toLowerCase()
		const index = text.toLowerCase().indexOf(keyword)

		if (index === -1) return text

		// 将文本分为三部分：匹配前、匹配部分、匹配后
		const before = text.substring(0, index)
		const match = text.substring(index, index + keyword.length)
		const after = text.substring(index + keyword.length)

		// 返回高亮HTML
		return `${before}<span class="color-red">${match}</span>${after}`
	} catch (error) {
		console.error('Tree: 高亮关键词失败', error)
		return text
	}
}
</script>

<style scoped></style>
