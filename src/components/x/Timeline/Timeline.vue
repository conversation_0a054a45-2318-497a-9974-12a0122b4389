<template>
	<div class="timeline">
		<slot></slot>
	</div>
</template>

<script setup>
const props = defineProps({
	// 序号的样式类
	numberClass: {
		type: String,
		default: 'bg-primary text-white text-xs font-medium rounded-full w-6 h-6 flex items-center justify-center',
	},
	// 线条的样式类
	lineClass: {
		type: String,
		default: 'bg-gray-300 w-0.5',
	},
	// 是否从1开始序号
	startFromOne: {
		type: Boolean,
		default: true,
	},
	// 是否倒序显示序号
	reverse: {
		type: Boolean,
		default: false,
	},
	// 左侧区域宽度（包含序号和线条）
	leftWidth: {
		type: String,
		default: '1.4rem',
	},
	// 序号与内容之间的间距
	gap: {
		type: String,
		default: '1rem',
	},
	// 序号与线条之间的间距
	numberLineGap: {
		type: String,
		default: '0.5rem',
	},
})

const items = ref([])

const timelineContext = shallowReactive({
	items,
	props,
	registerItem: (item) => {
		if (!items.value.find((i) => i.id === item.id)) {
			items.value.push(item)
		}
	},
	unregisterItem: (item) => {
		const index = items.value.findIndex((i) => i.id === item.id)
		if (index !== -1) {
			items.value.splice(index, 1)
		}
	},
})

provide('timeline', timelineContext)
</script>

<style scoped>
.timeline {
	position: relative;
}
</style>
