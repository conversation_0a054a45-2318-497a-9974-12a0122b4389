<script setup>
import { ref } from 'vue'

const props = defineProps({
	open: { type: Boolean, default: false },
	disabled: { type: Boolean, default: false },
	minHeight: { type: String, default: '0' },
})

const emit = defineEmits(['toggle'])

const isOpen = ref(props.open)

const toggle = () => {
	if (props.disabled) return
	isOpen.value = !isOpen.value
	emit('toggle', isOpen.value)
}
</script>

<template>
	<div class="w-full">
		<!-- 触发器 -->
		<div class="cursor-pointer select-none" :class="{ 'op50 cursor-not-allowed': disabled }" @click="toggle">
			<slot name="trigger" :is-open="isOpen">
				<div class="flex items-center gap-2">
					<XIconsChevronUp class="transition-transform duration-300" :class="{ 'rotate-90': isOpen }" />
					<span>{{ isOpen ? '收起' : '展开' }}</span>
				</div>
			</slot>
		</div>

		<!-- 折叠内容 -->
		<div class="overflow-hidden transition-all duration-300 ease-in-out" :style="{ maxHeight: isOpen ? '' : minHeight }">
			<div>
				<slot />
			</div>
		</div>
	</div>
</template>
