@import url('./variables.css');
html{
    font-size:16px;
    line-height: 1;
	/*	设置字体  */
}
body{
	font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, 'Helvetica Neue', <PERSON><PERSON>,
	'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol',
	'Noto Color Emoji';
}

#nprogress {
    pointer-events: none;
}

#nprogress .bar {
    background: repeating-linear-gradient(90deg, #00dc82 0, #34cdfe 50%, #0047e1);
    position: fixed;
    z-index: 1031;
    top: 0;
    left: 0;

    width: 100%;
    height: 2px;
}


/*
* 全局滚动条
*/
::-webkit-scrollbar {
    width: 4px;
    height:4px;
}

::-webkit-scrollbar-thumb {
    background-color: #dddddd;
    border-radius: 10px;
}

::-webkit-scrollbar-track {
    background-color: rgba(0,0,0,0.05);
}

/*
横向滚动条
*/
