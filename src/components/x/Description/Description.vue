<template>
	<div :style="gridStyle" class="grid" :class="[border ? 'border-border' : '']">
		<div v-for="(item, index) in list" :key="index" class="grid grid-cols-[minmax(3rem,auto)_minmax(3rem,1fr)]" :class="[item.class && item.class]">
			<XLabel :label="item[labelKey]" :style="{ width: labelWidth }" />
			<div class="flex-1 break-words color-tip text-sm">
				{{ item[valueKey] }}
			</div>
		</div>
	</div>
</template>

<script setup>
const props = defineProps({
	list: {
		type: Array,
		required: true,
	},
	row: {
		type: Number,
		default: 1,
	},
	column: {
		type: Number,
		default: 2,
	},
	labelKey: {
		type: String,
		default: 'label',
	},
	valueKey: {
		type: String,
		default: 'value',
	},
	labelWidth: {
		type: String,
		default: '',
	},
	border: {
		type: Boolean,
		default: false,
	},
})

const gridStyle = computed(() => ({
	gridTemplateColumns: `repeat(${props.column}, 1fr)`,
	gridTemplateRows: `repeat(${props.row}, auto)`,
}))
</script>
