<script setup lang="ts">
const isSpinning = ref(false)

const rotate = () => {
	if (!isSpinning.value) {
		isSpinning.value = true

		// 监听动画结束事件
		const handleAnimationEnd = () => {
			isSpinning.value = false
		}

		// 获取DOM元素并添加监听器
		nextTick(() => {
			const iconElement = document.querySelector('.refresh-icon')
			if (iconElement) {
				iconElement.addEventListener('animationend', handleAnimationEnd, { once: true })
			}
		})
	}
}
</script>

<template>
	<x-button :disabled="isSpinning" class="!h1.5rem !w1.5rem !rounded-full !bg-tip !p-0" @click="rotate">
		<div class="i-ic:round-refresh refresh-icon text-lg" :class="{ 'animate-spin animate-duration-500 animate-iteration-1': isSpinning }"></div>
	</x-button>
</template>

<style scoped></style>
