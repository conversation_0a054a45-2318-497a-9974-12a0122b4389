<!-- CascaderNode.vue -->
<template>
	<div
		class="h2rem min-w-10rem wfull flex cursor-pointer items-center transition px-xs"
		:class="[
			{
				'color-primary-80': isSelected || isExpanded,
				'color-danger': node.loadError,
				'cursor-default color-disabled': node.disabled,
				'color-aid': isLoading,
			},
			isChecked ? 'bg-primary_20 color-primary' : 'hover:bg-hover',
		]"
		@click.stop="handleClick"
	>
		<!-- 复选框 -->
		<div v-if="multiple" class="ml-xxs">
			<x-checkbox
				:disabled="node.disabled || (leafOnly && hasChildren)"
				:model-value="isChecked"
				:indeterminate="isIndeterminate"
				@click.stop
				@change="handleCheck"
			/>
		</div>

		<!-- 节点内容 -->
		<span class="relative flex-1 overflow-hidden truncate whitespace-nowrap transition" :class="{}">
			<!-- 节点标签 -->
			<span v-if="searchKeyword" v-html="highlightKeyword(node.label)"></span>
			<span v-else>{{ node.label }}</span>

			<!-- 路径信息（仅在搜索结果中显示） -->
			<span v-if="node.pathLabel" class="truncate color-aid text-xs" :title="node.fullPath || node.pathLabel">({{ node.pathLabel }})</span>

			<!-- 加载错误提示 -->
			<span v-if="node.loadError" class="w-1.5rem inline-flex items-center justify-center color-danger border-danger" title="加载失败，点击重试">
				!
			</span>
		</span>

		<!-- 展开/折叠图标 -->
		<div v-if="hasChildren" class="hfull inline-flex items-center justify-center">
			<span class="wfull cursor-pointer">
				<div v-if="isLoading" v-loading="true" class="h80% wfull"></div>
				<XIconsChevronRight v-else class="transition" :class="[{ 'rotate-90deg': isExpanded }]" />
			</span>
		</div>
	</div>
</template>

<script setup>
const props = defineProps({
	node: {
		type: Object,
		required: true,
	},
	multiple: {
		type: Boolean,
		default: false,
	},
	checkStrictly: {
		type: Boolean,
		default: false,
	},
	modelValue: {
		type: [String, Number, Object, Array],
		default: null,
	},
	searchKeyword: {
		type: String,
		default: '',
	},
	leafOnly: {
		type: Boolean,
		default: false,
	},
})

// 注入级联组件提供的方法
const cascader = inject('cascader', {})

// 计算属性
const isChecked = computed(() => cascader.isNodeChecked(props.node))
const isLoading = computed(() => cascader.isNodeLoading(props.node))
const isIndeterminate = computed(() => cascader.isNodeIndeterminate(props.node))
const isSelected = computed(() => cascader.isNodeSelected(props.node))
const isExpanded = computed(() => cascader.isNodeExpanded(props.node))

// 使用级联组件提供的方法
const hasChildren = computed(() => cascader.hasChildren(props.node))

// 方法
const handleClick = () => {
	// 如果节点有子节点且正在加载，不处理点击事件
	if (hasChildren.value && isLoading.value) return

	// 如果节点有子节点且还没有加载过子节点
	if (props.node.hasChildren && (!props.node.children || !props.node.children.length)) {
		// 设置加载状态并加载子节点
		cascader.loadNodeChildren(props.node)
	}

	cascader.handleNodeClick(props.node)
}

const handleCheck = () => cascader.handleNodeCheck(props.node)

// 高亮关键词 - 优化搜索结果显示
const highlightKeyword = (text) => {
	if (!text || !props.searchKeyword) return text

	try {
		const keyword = props.searchKeyword.toLowerCase()
		const index = text.toLowerCase().indexOf(keyword)

		if (index === -1) return text

		const before = text.substring(0, index)
		const match = text.substring(index, index + keyword.length)
		const after = text.substring(index + keyword.length)

		return `${before}<span class="color-red">${match}</span>${after}`
	} catch (error) {
		console.error('Cascader: 高亮关键词失败', error)
		return text
	}
}
</script>

<style scoped></style>
