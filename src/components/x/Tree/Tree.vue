<!-- Tree.vue -->
<template>
	<div class="overflow-auto" @click.stop>
		<!-- 树形结构 -->
		<div>
			<div v-if="loading" class="text-center color-aid p-sm">
				<span v-loading="true"></span>
				加载中...
			</div>
			<div v-else-if="!filteredData.length" class="text-center color-aid p-sm">
				<span v-if="searchKeyword">无匹配结果</span>
				<span v-else>无数据</span>
			</div>
			<div v-else class="flex flex-col gap-xxs">
				<x-tree-node
					v-for="node in filteredData"
					:key="node.value"
					:node="node"
					:leaf-only="leafOnly"
					:multiple="multiple"
					:check-strictly="checkStrictly"
					:model-value="modelValue"
					:search-keyword="searchKeyword"
					:include-half-checked="includeHalfChecked"
				></x-tree-node>
			</div>
		</div>
	</div>
</template>

<script setup>
/**
 * 树形组件
 * 支持单选、多选、懒加载、搜索过滤等功能
 */

// 组件属性定义
const props = defineProps({
	// 树形数据源
	data: {
		type: Array,
		default: () => [],
	},
	// 当前选中的值
	modelValue: {
		type: [String, Number, Object, Array],
		default: () => null,
	},
	// 是否多选
	multiple: {
		type: Boolean,
		default: false,
	},
	// 在父子节点选中状态不再关联的情况下，勾选节点不会影响父子节点
	checkStrictly: {
		type: Boolean,
		default: false,
	},
	// 是否可筛选
	filterable: {
		type: Boolean,
		default: false,
	},
	// 是否正在加载数据
	loading: {
		type: Boolean,
		default: false,
	},
	// 加载子节点数据的函数
	loadData: {
		type: Function,
		default: null,
	},
	// 是否手风琴模式（同级节点只能展开一个）
	accordion: {
		type: Boolean,
		default: true,
	},
	// 选中节点时是否自动展开父节点
	expandSelected: {
		type: Boolean,
		default: false,
	},
	// 是否仅叶子节点可选
	leafOnly: {
		type: Boolean,
		default: false,
	},
	// 懒加载重试次数
	lazyLoadRetries: {
		type: Number,
		default: 3,
	},
	// 懒加载重试延迟（毫秒）
	lazyLoadRetryDelay: {
		type: Number,
		default: 1000,
	},
	// 用于构建路径的节点属性
	pathNodeKey: {
		type: String,
		default: 'path',
	},
	// 节点标签属性名
	labelKey: {
		type: String,
		default: 'name',
	},
	// 节点值属性名
	valueKey: {
		type: String,
		default: 'id',
	},
	// 是否将半选状态的节点包含在modelValue中
	includeHalfChecked: {
		type: Boolean,
		default: false,
	},
	isFlatData: {
		type: Boolean,
		default: true,
	},
})

// 事件定义
const emit = defineEmits(['node-click', 'node-check', 'node-expand', 'update:modelValue', 'lazy-load-error'])

// 状态变量
const searchKeyword = ref('') // 搜索关键词
const selectedKeys = ref([]) // 选中的节点路径
const expandedKeys = ref(new Set()) // 展开的节点集合
const indeterminateKeys = ref([]) // 半选状态的节点
const treeData = ref([]) // 处理后的树形数据
const loadingKeys = ref(new Set()) // 加载中的节点集合
let filterTimer = null // 搜索防抖定时器

// 内部选中状态，用于在没有modelValue时维护选中状态
const internalSelectedValue = ref(props.modelValue !== null && props.modelValue !== undefined ? props.modelValue : props.multiple ? [] : null)

// 数据结构映射
const nodeMap = ref(new Map()) // 节点映射表，用于快速查找节点
const pathMap = ref(new Map()) // 路径映射表，只存储节点的value
const customPathMaps = ref({}) // 按不同属性构建的路径映射表

/**
 * 搜索防抖函数
 * 避免频繁触发搜索操作
 */
const debounceFilter = () => {
	if (filterTimer) clearTimeout(filterTimer)
	filterTimer = setTimeout(() => {
		// 搜索时自动展开包含匹配项的节点
		if (searchKeyword.value) {
			autoExpandMatchedNodes(searchKeyword.value.toLowerCase())
		}
	}, 300)
}

/**
 * 自动展开包含匹配项的节点
 * @param {string} keyword - 搜索关键词
 */
const autoExpandMatchedNodes = (keyword) => {
	// 存储需要展开的节点值
	const nodesToExpand = new Set()
	// 限制最大匹配数量，避免大型树中展开过多节点
	const maxMatches = 50
	let matchCount = 0

	// 遍历所有节点查找匹配项
	for (const [_, node] of nodeMap.value.entries()) {
		// 检查当前节点是否匹配
		const isMatch = node.label.toLowerCase().includes(keyword)

		// 如果当前节点匹配，收集其所有父节点路径
		if (isMatch) {
			matchCount++
			// 获取父节点路径并添加到需要展开的集合中
			const parentValues = getParentNodeValues(node.value)
			parentValues.forEach((parentValue) => nodesToExpand.add(parentValue))

			// 如果达到最大匹配数量，停止搜索
			if (matchCount >= maxMatches) {
				console.warn(`Tree: 搜索匹配项过多，已限制展开节点数量为 ${maxMatches}`)
				break
			}
		}
	}

	// 一次性展开所有需要展开的节点
	nodesToExpand.forEach((value) => {
		const node = findNodeByValue(value)
		if (node && hasChildren(node)) {
			expandedKeys.value.add(node.value)
		}
	})
}

/**
 * 过滤后的数据
 * 根据搜索关键词过滤树形数据
 */
const filteredData = computed(() => {
	if (!searchKeyword.value) return treeData.value

	const keyword = searchKeyword.value.toLowerCase()

	/**
	 * 递归过滤节点
	 * @param {Array} nodes - 节点数组
	 * @returns {Array} 过滤后的节点数组
	 */
	const filterNode = (nodes) => {
		return nodes.filter((node) => {
			// 检查当前节点是否匹配
			const isMatch = node.label.toLowerCase().includes(keyword)

			// 如果有子节点，递归过滤
			if (node.children && node.children.length) {
				const filteredChildren = filterNode(node.children)

				// 如果有匹配的子节点，保留父节点并更新子节点
				if (filteredChildren.length) {
					// 创建新对象避免修改原始数据
					return {
						...node,
						children: filteredChildren,
					}
				}
			}

			// 如果当前节点匹配，直接返回
			return isMatch
		})
	}

	// 使用深拷贝避免修改原始数据
	try {
		return filterNode(JSON.parse(JSON.stringify(treeData.value)))
	} catch (error) {
		console.error('Tree: 过滤树数据失败', error)
		return []
	}
})

/**
 * 处理节点数据，构建节点关系和路径
 * @param {Array} nodes - 原始节点数组
 * @param {number|string} parentId - 父节点ID
 * @param {number} level - 当前节点层级
 * @param {Object} parentPaths - 父节点路径信息
 * @param {Set} processedValues - 已处理的节点值集合
 * @param {Set} duplicateValues - 重复的节点值集合
 * @returns {Array} 处理后的节点数组
 */
const processNode = (nodes, parentId = 0, level = 0, parentPaths = {}, processedValues = new Set(), duplicateValues = new Set()) => {
	return nodes
		.filter((node) => node.visible !== false) // 过滤掉不可见节点
		.map((node) => {
			// 提取节点基本信息
			const label = node?.[props.labelKey] || node?.label
			const value = node?.[props.valueKey] || node?.value

			// 检查是否已经存在相同value的节点
			if (processedValues.has(value)) {
				// 记录重复值
				duplicateValues.add(value)
				// 如果已存在相同value的节点，则返回null，后续会被过滤掉
				return null
			}

			// 标记该值已被处理
			processedValues.add(value)

			// 创建处理后的节点对象
			const processedNode = {
				...node,
				parentId,
				level,
				label,
				value,
			}

			// 构建路径信息
			const nodePaths = { ...parentPaths }

			/**
			 * 构建节点路径
			 * @param {string} key - 路径键名
			 * @param {string|number} pathValue - 路径值
			 * @returns {string} 完整路径
			 */
			const buildPath = (key, pathValue) => {
				let path = parentPaths[key] ? `${parentPaths[key]}/${pathValue}` : `${pathValue}`
				// 更新当前节点的路径，供子节点使用
				nodePaths[key] = path

				// 将路径存储到节点上
				processedNode[`_path_${key}`] = path

				return path
			}

			// 构建默认路径（使用props.pathNodeKey）
			const defaultKey = props.pathNodeKey
			const defaultPathValue = node[defaultKey] || node.value
			const defaultPath = buildPath(defaultKey, defaultPathValue)
			// 存储默认路径，只存储节点的value
			pathMap.value.set(defaultPath, processedNode.value)

			// 遍历节点的所有属性，为每个属性构建路径
			for (const key in node) {
				if (typeof node[key] === 'string' || (typeof node[key] === 'number' && key !== defaultKey)) {
					const pathValue = node[key]
					const path = buildPath(key, pathValue)

					// 确保自定义路径映射表存在
					if (!customPathMaps.value[key]) {
						customPathMaps.value[key] = new Map()
					}

					// 将节点的value添加到自定义路径映射表
					customPathMaps.value[key].set(path, processedNode.value)
				}
			}

			// 将节点添加到映射表
			nodeMap.value.set(processedNode.value, processedNode)

			// 处理子节点
			if (node.children && node.children.length) {
				processedNode.children = processNode(node.children, processedNode.value, level + 1, nodePaths, processedValues, duplicateValues)
			}

			return processedNode
		})
		.filter(Boolean) // 过滤掉null值（重复节点）
}

/**
 * 将扁平数据转换为树形结构
 * @param {Array} flatData - 扁平结构的数据数组
 * @returns {Array} 树形结构数据
 */
const convertFlatToTree = (flatData) => {
	// 创建一个Map来存储所有节点
	const nodeMap = new Map()
	const rootNodes = []

	// 第一次遍历：创建所有节点
	flatData.forEach((node) => {
		const processedNode = {
			...node,
			children: [],
			level: 0,
		}
		nodeMap.set(node[props.valueKey] || node.value, processedNode)
	})

	// 第二次遍历：构建树形结构
	flatData.forEach((node) => {
		const currentNode = nodeMap.get(node[props.valueKey] || node.value)
		const parentId = node.parentId

		if (parentId === null || parentId === undefined || parentId === 0) {
			// 根节点
			rootNodes.push(currentNode)
		} else {
			// 子节点
			const parentNode = nodeMap.get(parentId)
			if (parentNode) {
				parentNode.children.push(currentNode)
				currentNode.level = parentNode.level + 1
			} else {
				// 如果找不到父节点，将其作为根节点
				rootNodes.push(currentNode)
			}
		}
	})

	return rootNodes
}

/**
 * 初始化树数据
 * 处理原始数据，构建节点关系和映射
 */
const initTreeData = () => {
	// 清空数据结构
	nodeMap.value.clear()
	pathMap.value.clear()
	customPathMaps.value = {}
	loadingKeys.value.clear()

	try {
		const data = JSON.parse(JSON.stringify(props.data))
		console.log('data----', data)
		// 优化数据结构判断逻辑
		// 只检查前10个节点来判断数据结构类型
		const sampleSize = Math.min(10, data.length)
		let hasParentIdCount = 0
		let hasChildrenCount = 0

		// 检查样本节点的数据结构特征
		for (let i = 0; i < sampleSize; i++) {
			const node = data[i]
			if (node?.hasOwnProperty('parentId') && node.parentId !== null && node.parentId !== undefined) {
				hasParentIdCount++
			}
			if (node?.hasOwnProperty('children') && Array.isArray(node.children)) {
				hasChildrenCount++
			}
		}

		// 如果样本中超过一半的节点使用parentId，且几乎没有节点使用children，则认为是扁平结构
		const isFlatData = hasParentIdCount > sampleSize / 2 && hasChildrenCount === 0

		// 如果是扁平数据，先转换为树形结构
		const processedData = isFlatData && props.isFlatData ? convertFlatToTree(data) : data
		console.log('processedData---', processedData)

		// 创建集合来跟踪已处理的值和重复值
		const processedValues = new Set()
		const duplicateValues = new Set()

		// 处理数据并同时检测重复值
		treeData.value = processNode(processedData, 0, 0, {}, processedValues, duplicateValues)

		// 如果有重复值，输出警告
		if (duplicateValues.size > 0) {
			console.warn(`Tree: 检测到 ${duplicateValues.size} 个重复的节点值，已忽略后续相同值的节点: `, [...duplicateValues])
		}
	} catch (error) {
		console.error('Tree: 初始化树数据失败', error)
		treeData.value = []
	}
}

/**
 * 根据值查找节点
 * @param {string|number} value - 节点值
 * @returns {Object|null} 找到的节点对象，未找到则返回null
 */
const findNodeByValue = (value) => {
	return nodeMap.value.get(value)
}

/**
 * 根据值查找节点的标签路径
 * @param {string|number} value - 节点值
 * @returns {string|undefined} 节点标签路径
 */
const findNodeLabelPathByValue = (value) => {
	return findNodeByValue(value)?.[`_path_${props.labelKey}`]
}

/**
 * 根据完整路径查找节点
 * @param {string} fullPath - 完整路径
 * @returns {Object|null} 找到的节点对象，未找到则返回null
 */
const findNodeByFullPath = (fullPath) => {
	if (!fullPath) return null

	const nodeValue = pathMap.value.get(fullPath)
	if (nodeValue === null || nodeValue === undefined) return null

	return nodeMap.value.get(nodeValue)
}

/**
 * 获取节点的所有子节点值（包括深层子节点）
 * 用于级联选择等场景
 * @param {string|number} nodeValue - 节点值
 * @returns {Array} 所有子节点值的数组
 */
const getChildNodeValues = (nodeValue) => {
	const values = []
	const node = nodeMap.value.get(nodeValue)

	if (!node || !node.children) return values

	// 使用迭代而不是递归，避免大型树结构中的栈溢出风险
	const stack = [...node.children]
	while (stack.length > 0) {
		const currentNode = stack.pop()
		values.push(currentNode.value)

		if (currentNode.children && currentNode.children.length) {
			stack.push(...currentNode.children)
		}
	}

	return values
}

/**
 * 获取节点的所有父节点值
 * 用于确定节点路径等场景
 * @param {string|number} nodeValue - 节点值
 * @returns {Array} 所有父节点值的数组，从近到远排序
 */
const getParentNodeValues = (nodeValue) => {
	const values = []
	let node = nodeMap.value.get(nodeValue)

	// 防止循环引用导致无限循环
	const visitedNodes = new Set()
	const maxDepth = 100 // 最多遍历100层，防止极端情况下的无限循环

	while (node && node.parentId && maxDepth > values.length) {
		// 检查是否已访问过该节点，防止循环引用
		if (visitedNodes.has(node.parentId)) {
			console.warn('Tree: 检测到循环引用', node)
			break
		}

		visitedNodes.add(node.parentId)
		values.push(node.parentId)
		node = nodeMap.value.get(node.parentId)
	}

	// 如果达到最大深度限制，记录警告
	if (values.length >= maxDepth) {
		console.warn('Tree: 节点层级过深，已达到最大深度限制')
	}

	return values
}

/**
 * 判断节点是否有子节点
 * @param {Object} node - 节点对象
 * @returns {boolean} 是否有子节点
 */
const hasChildren = (node) => {
	if (!node) return false
	return (node.children && node.children.length > 0) || node.hasChildren
}

/**
 * 判断节点是否被选中
 * @param {Object} node - 节点对象
 * @returns {boolean} 是否被选中
 */
const isNodeChecked = (node) => {
	if (!node) return false

	// 检查modelValue（外部状态）
	if (!props.multiple) {
		return internalSelectedValue.value === node.value
	}

	return Array.isArray(internalSelectedValue.value) && internalSelectedValue.value.includes(node.value)
}

/**
 * 判断节点是否在选中路径中
 * @param {Object} node - 节点对象
 * @returns {boolean} 是否在选中路径中
 */
const isNodeSelected = (node) => selectedKeys.value.includes(node.value)

/**
 * 判断节点是否正在加载
 * @param {Object} node - 节点对象
 * @returns {boolean} 是否正在加载
 */
const isNodeLoading = (node) => {
	if (!node) return false
	return loadingKeys.value.has(node.value)
}

/**
 * 判断节点是否为半选状态
 * @param {Object} node - 节点对象
 * @returns {boolean} 是否为半选状态
 */
const isNodeIndeterminate = (node) => {
	if (!node || !props.multiple) return false
	// 在 leafOnly 模式下，非叶子节点不显示半选状态
	if (props.leafOnly && hasChildren(node)) {
		return false
	}
	return indeterminateKeys.value.includes(node.value)
}

/**
 * 判断节点是否展开
 * @param {Object} node - 节点对象
 * @returns {boolean} 是否展开
 */
const isNodeExpanded = (node) => {
	if (!node) return false
	return expandedKeys.value.has(node.value)
}

/**
 * 处理节点点击
 * 包括展开/折叠和选中操作
 * @param {Object} node - 节点对象
 * @param {boolean} isEmit - 是否触发事件
 */
const handleNodeClick = (node, isEmit = true) => {
	if (!node || node.disabled) return

	// 处理节点展开/折叠
	handleNodeExpand(node)

	// 如果是单选模式，处理节点选中
	if (!props.multiple) {
		handleNodeCheck(node, isEmit)
	}

	// 触发节点点击事件
	if (isEmit) {
		emit('node-click', node)
	}
}

/**
 * 处理节点选中状态变化
 * @param {Object} node - 节点对象
 * @param {boolean} isEmit - 是否触发事件
 */
const handleNodeCheck = (node, isEmit = true) => {
	if (!node) return
	// 如果是仅叶子节点可选模式，且当前节点有子节点，则不处理
	if (props.leafOnly && hasChildren(node)) return

	// 获取所有选中的节点值
	let checkedValues = props.multiple ? [...(Array.isArray(internalSelectedValue.value) ? internalSelectedValue.value : [])] : null
	// 保留现有的半选状态
	const indeterminateValues = [...indeterminateKeys.value]

	if (props.multiple) {
		// 多选模式
		const isCurrentlyChecked = isNodeChecked(node)

		// 处理选中/取消选中逻辑
		if (isCurrentlyChecked) {
			// 取消选中当前节点
			checkedValues = checkedValues.filter((value) => value !== node.value)
		} else {
			// 选中当前节点
			checkedValues.push(node.value)
		}

		// 如果不是严格模式，需要处理父子节点关联
		if (!props.checkStrictly) {
			// 获取当前节点的所有子节点值
			const childValues = getChildNodeValues(node.value)

			if (isCurrentlyChecked) {
				// 取消选中时，需要同时取消子节点的选中状态
				checkedValues = checkedValues.filter((value) => !childValues.includes(value))
			} else {
				// 选中时，同时选中所有子节点
				updateChildNodeCheckedState(node, checkedValues)
			}

			// 从半选状态中移除当前节点
			const indeterminateIndex = indeterminateValues.indexOf(node.value)
			if (indeterminateIndex !== -1) {
				indeterminateValues.splice(indeterminateIndex, 1)
			}

			// 获取当前节点的所有父节点并更新它们的状态
			const parentNodes = getParentNodeValues(node.value)
				.map((id) => nodeMap.value.get(id))
				.filter(Boolean)

			// 更新每个父节点的状态
			parentNodes.forEach((parent) => {
				updateSingleParentNodeState(parent, checkedValues, indeterminateValues)
			})
		}

		// 更新内部半选状态
		indeterminateKeys.value = indeterminateValues

		// 如果需要包含半选状态的节点，将半选节点添加到选中值中
		if (props.includeHalfChecked && indeterminateValues.length > 0) {
			// 创建一个新的数组，包含选中值和半选值，确保没有重复
			checkedValues = [...new Set([...checkedValues, ...indeterminateValues])]
		}
	} else {
		// 单选模式，直接设置选中值为当前节点值
		checkedValues = node.value
	}

	// 更新selectedKeys - 保持选中节点的路径
	updateSelectedPaths(props.multiple ? checkedValues : [node.value])

	// 更新内部选中状态
	internalSelectedValue.value = checkedValues

	// 触发事件
	if (isEmit) {
		emit('update:modelValue', internalSelectedValue.value)
		emit('node-check', node)
	}
}

/**
 * 更新选中节点的路径
 * @param {Array} checkedValues - 选中的节点值数组
 */
const updateSelectedPaths = (checkedValues) => {
	if (!Array.isArray(checkedValues) || checkedValues.length === 0) {
		selectedKeys.value = []
		return
	}

	// 创建一个Set来存储所有路径，避免重复
	const allPaths = new Set()

	// 遍历所有选中的节点值
	checkedValues.forEach((value) => {
		// 将当前节点值添加到路径中
		allPaths.add(value)
		// 获取当前节点的所有父节点路径并添加到Set中
		getParentNodeValues(value).forEach((path) => allPaths.add(path))
	})

	// 将Set转换为数组并更新selectedKeys
	selectedKeys.value = [...allPaths]
}

/**
 * 更新子节点的选中状态
 * 递归处理所有子节点
 * @param {Object} node - 父节点对象
 * @param {Array} checkedValues - 选中的节点值数组（会被修改）
 */
const updateChildNodeCheckedState = (node, checkedValues) => {
	if (!node) return

	// 如果有子节点，递归更新子节点状态
	if (node.children && node.children.length) {
		node.children.forEach((child) => {
			if (!child.disabled) {
				// 避免重复添加
				if (!checkedValues.includes(child.value)) {
					checkedValues.push(child.value)
				}
				// 递归处理子节点的子节点
				updateChildNodeCheckedState(child, checkedValues)
			}
		})
	}
}

/**
 * 更新单个父节点的状态
 * 根据子节点的选中情况更新父节点的选中和半选状态
 * @param {Object} parent - 父节点对象
 * @param {Array} checkedValues - 选中的节点值数组（会被修改）
 * @param {Array} indeterminateValues - 半选状态的节点值数组（会被修改）
 */
const updateSingleParentNodeState = (parent, checkedValues, indeterminateValues) => {
	if (!parent || !parent.children || !parent.children.length) return

	// 检查子节点状态
	const totalChildren = parent.children.length
	const checkedCount = parent.children.filter((child) => checkedValues.includes(child.value)).length
	const indeterminateCount = parent.children.filter((child) => indeterminateValues.includes(child.value)).length

	// 根据子节点状态更新父节点状态
	if (checkedCount === 0 && indeterminateCount === 0) {
		// 所有子节点都未选中 - 父节点也未选中且不是半选
		removeFromArray(checkedValues, parent.value)
		removeFromArray(indeterminateValues, parent.value)
	} else if (checkedCount === totalChildren) {
		// 所有子节点都选中 - 父节点选中且不是半选
		if (!props.leafOnly) {
			// 如果不是仅叶子节点可选模式，则添加父节点到选中列表
			addToArrayIfNotExists(checkedValues, parent.value)
		}
		removeFromArray(indeterminateValues, parent.value)
	} else {
		// 部分子节点选中或半选 - 父节点是半选状态
		removeFromArray(checkedValues, parent.value)
		addToArrayIfNotExists(indeterminateValues, parent.value)
	}
}

/**
 * 从数组中移除指定元素
 * @param {Array} array - 要操作的数组
 * @param {*} value - 要移除的值
 */
const removeFromArray = (array, value) => {
	const index = array.indexOf(value)
	if (index !== -1) {
		array.splice(index, 1)
	}
}

/**
 * 如果数组中不存在指定元素，则添加到数组中
 * @param {Array} array - 要操作的数组
 * @param {*} value - 要添加的值
 */
const addToArrayIfNotExists = (array, value) => {
	if (!array.includes(value)) {
		array.push(value)
	}
}

/**
 * 处理节点展开/折叠
 * @param {Object} node - 节点对象
 */
const handleNodeExpand = (node) => {
	if (!node) return

	// 触发节点展开事件
	emit('node-expand', node)

	if (expandedKeys.value.has(node.value)) {
		// 如果节点已展开，则折叠
		expandedKeys.value.delete(node.value)
	} else {
		// 如果是手风琴模式，需要折叠同级节点
		if (props.accordion || !props.multiple) {
			// 遍历所有展开的节点，折叠同级节点
			for (const value of expandedKeys.value) {
				const expandNode = nodeMap.value.get(value)
				if (expandNode && expandNode.level === node.level) {
					expandedKeys.value.delete(value)
				}
			}
		}

		// 如果节点有子节点，则展开
		if (hasChildren(node)) {
			expandedKeys.value.add(node.value)
		}

		// 如果有懒加载函数且节点还没有加载过子节点
		if (props.loadData && node.hasChildren && (!node.children || !node.children.length)) {
			// 设置加载状态
			loadingKeys.value.add(node.value)

			// 执行懒加载
			loadNodeChildren(node)
		}
	}
}

/**
 * 懒加载节点子节点
 * 支持失败重试机制
 * @param {Object} node - 要加载子节点的节点对象
 */
const loadNodeChildren = (node) => {
	if (!node || !props.loadData || !loadingKeys.value.has(node.value)) return

	// 记录当前重试次数
	node.retryCount = node.retryCount || 0

	try {
		// 调用加载数据函数
		Promise.resolve(props.loadData(node))
			.then((children) => {
				// 处理成功加载的数据
				if (Array.isArray(children) && children.length > 0) {
					// 构建父节点路径信息
					const parentPaths = {}

					// 从节点中提取路径信息
					for (const key in node) {
						if (key.startsWith('_path_')) {
							const pathKey = key.replace('_path_', '')
							parentPaths[pathKey] = node[key]
						}
					}

					// 创建集合来跟踪已处理的值和重复值
					const processedValues = new Set()
					const duplicateValues = new Set()

					// 处理子节点数据
					const processedChildren = processNode([...children], node.value, node.level + 1, parentPaths, processedValues, duplicateValues)

					// 如果有重复值，输出警告
					if (duplicateValues.size > 0) {
						console.warn(`Tree: 懒加载检测到 ${duplicateValues.size} 个重复的节点值，已忽略后续相同值的节点: `, [...duplicateValues])
					}

					// 更新节点的子节点
					node.children = processedChildren
					node.hasChildren = true

					// 清除加载状态和错误状态
					loadingKeys.value.delete(node.value)
					delete node.retryCount
					delete node.loadError
				} else {
					// 没有子节点
					node.children = []
					node.hasChildren = false

					// 清除加载状态
					loadingKeys.value.delete(node.value)
					delete node.retryCount
					delete node.loadError
				}
			})
			.catch((error) => {
				console.error('Tree: 加载节点子节点失败', error)

				// 增加重试次数
				node.retryCount += 1

				// 如果未超过最大重试次数，则延迟重试
				if (node.retryCount < props.lazyLoadRetries) {
					setTimeout(() => {
						loadNodeChildren(node)
					}, props.lazyLoadRetryDelay)
				} else {
					// 超过最大重试次数，标记为加载失败
					node.loadError = true
					loadingKeys.value.delete(node.value)

					// 发出加载错误事件
					emit('lazy-load-error', node, error)
				}
			})
	} catch (error) {
		console.error('Tree: 执行懒加载函数失败', error)
		node.loadError = true
		loadingKeys.value.delete(node.value)
		emit('lazy-load-error', node, error)
	}
}

/**
 * 初始化半选状态
 * 根据选中节点计算半选状态
 */
const initIndeterminateStates = () => {
	// 如果不是多选模式或者是严格模式，不需要计算半选状态
	if (!props.multiple || props.checkStrictly || !Array.isArray(internalSelectedValue.value)) {
		return
	}

	const indeterminateValues = []
	const checkedValues = [...internalSelectedValue.value]

	// 获取所有选中节点的父节点（去重）
	const parentNodeSet = new Set()
	checkedValues.forEach((value) => {
		const parentValues = getParentNodeValues(value)
		parentValues.forEach((parentValue) => parentNodeSet.add(parentValue))
	})

	// 检查每个父节点的状态
	parentNodeSet.forEach((parentValue) => {
		const parentNode = findNodeByValue(parentValue)
		if (parentNode && parentNode.children && parentNode.children.length) {
			const totalChildren = parentNode.children.length
			const checkedChildren = parentNode.children.filter((child) => checkedValues.includes(child.value)).length

			// 如果部分子节点被选中（不是全部也不是零），则父节点应该是半选状态
			if (checkedChildren > 0 && checkedChildren < totalChildren) {
				indeterminateValues.push(parentValue)
			}
		}
	})

	// 更新半选状态
	indeterminateKeys.value = indeterminateValues

	// 如果需要包含半选状态的节点，但modelValue中不包含这些节点，需要更新modelValue
	if (props.includeHalfChecked && indeterminateValues.length > 0) {
		const missingHalfCheckedValues = indeterminateValues.filter((value) => !checkedValues.includes(value))

		if (missingHalfCheckedValues.length > 0) {
			// 合并选中值和半选值
			const combinedValues = [...new Set([...checkedValues, ...indeterminateValues])]
			internalSelectedValue.value = combinedValues

			// 在下一个事件循环中通知父组件
			nextTick(() => {
				emit('update:modelValue', combinedValues)
			})
		}
	}
}

// 监听数据变化
watch(
	() => props.data,
	() => {
		// 初始化树数据
		initTreeData()

		// 数据初始化后，计算半选状态
		nextTick(() => {
			initIndeterminateStates()
		})
	},
	{ immediate: true, deep: true },
)

// 监听选中值变化，更新节点状态
watch(
	() => props.modelValue,
	(newVal) => {
		// 如果值为空，清空选中状态
		if (newVal === undefined || newVal === null) {
			internalSelectedValue.value = props.multiple ? [] : null
			selectedKeys.value = []
			indeterminateKeys.value = []
			return
		}

		// 更新内部选中值
		internalSelectedValue.value = newVal

		// 如果开启了expandSelected，需要自动展开选中节点的父节点
		if (props.expandSelected) {
			expandSelectedNodes(newVal)
		}

		// 更新选中状态
		if (props.multiple && Array.isArray(newVal)) {
			// 更新选中路径
			updateSelectedPaths(newVal)

			// 使用统一的方法计算半选状态
			nextTick(() => {
				initIndeterminateStates()
			})
		} else {
			// 单选模式
			const node = findNodeByValue(newVal)
			if (node) {
				// 更新选中的父节点路径
				selectedKeys.value = getParentNodeValues(node.value)
			} else {
				// 如果没有找到节点，清空selectedKeys
				selectedKeys.value = []
			}
		}
	},
	{ deep: true, immediate: true },
)

/**
 * 展开选中节点的父节点
 * @param {Array|string|number} selectedValues - 选中的节点值
 */
const expandSelectedNodes = (selectedValues) => {
	// 存储需要展开的节点值
	const nodesToExpand = new Set()

	// 处理单个值或数组值
	const values = Array.isArray(selectedValues) ? selectedValues : [selectedValues]

	// 如果是手风琴模式，先清空所有已展开的节点
	if (props.accordion) {
		expandedKeys.value.clear()
	}

	// 只处理最后一个选中值（如果是手风琴模式）
	const valuesToProcess = props.accordion && values.length > 0 ? [values[values.length - 1]] : values

	// 收集所有需要展开的父节点
	for (const value of valuesToProcess) {
		const node = findNodeByValue(value)
		if (node) {
			// 添加所有父节点
			const parentValues = getParentNodeValues(value)
			parentValues.forEach((parentValue) => nodesToExpand.add(parentValue))
		}
	}

	// 一次性展开所有需要展开的节点
	nodesToExpand.forEach((value) => {
		const node = findNodeByValue(value)
		if (node && hasChildren(node) && !isNodeExpanded(node)) {
			// 不触发事件，只更新内部状态
			expandedKeys.value.add(node.value)
		}
	})
}

// 组件卸载前清理
onBeforeUnmount(() => {
	// 清除定时器
	if (filterTimer) {
		clearTimeout(filterTimer)
		filterTimer = null
	}

	// 清空加载状态
	loadingKeys.value.clear()
})

// 提供给子组件的方法和属性
provide('tree', {
	// 属性
	multiple: computed(() => props.multiple),
	checkStrictly: computed(() => props.checkStrictly),
	leafOnly: computed(() => props.leafOnly),
	modelValue: computed(() => internalSelectedValue.value),
	internalSelectedValue: computed(() => internalSelectedValue.value),
	accordion: computed(() => props.accordion),
	loadingKeys: computed(() => loadingKeys.value),
	includeHalfChecked: computed(() => props.includeHalfChecked),

	// 状态判断方法
	hasChildren,
	isNodeSelected,
	isNodeChecked,
	isNodeLoading,
	isNodeExpanded,
	isNodeIndeterminate,

	// 操作方法
	handleNodeClick,
	handleNodeCheck,
	handleNodeExpand,

	// 辅助方法
	getChildNodeValues,
	getParentNodeValues,
	findNodeByValue,
})

// 导出公共方法供外部使用
const publicMethods = {
	getChildNodeValues,
	getParentNodeValues,
	findNodeByValue,
	findNodeLabelPathByValue,
	findNodeByFullPath,
	debounceFilter,
	hasChildren,
}
defineExpose(publicMethods)
</script>

<style scoped></style>
