<!-- TreeDemo.vue -->
<template>
	<div class="tree-demo h-500px overflow-y-auto">
		<h1>树形组件测试 Demo</h1>

		<div class="demo-container">
			<!-- 基础用法 -->
			<div class="demo-section">
				<h2>1. 基础用法</h2>
				<div class="demo-content">
					<x-tree ref="basicTree" v-model="basicSelected" :data="basicData" @node-click="handleNodeClick"></x-tree>
				</div>
				<div class="demo-info">
					<p>当前选中: {{ basicSelected }}</p>
					<button @click="expandNode('basicTree', 'fruits')">展开水果节点</button>
					<button @click="collapseAll('basicTree')">折叠所有</button>
					<button @click="expandSubtree('basicTree', 'fruits')">展开水果子树</button>
				</div>
			</div>

			<!-- 多选模式 -->
			<div class="demo-section">
				<h2>2. 多选模式</h2>
				<div class="demo-content">
					<x-tree ref="multipleTree" v-model="multipleSelected" :data="basicData" multiple @node-check="handleNodeCheck"></x-tree>
				</div>
				<div class="demo-info">
					<p>当前选中: {{ multipleSelected }}</p>
					<button @click="checkNode('multipleTree', 'fruits')">选中水果节点</button>
					<button @click="uncheckAll('multipleTree')">取消所有选中</button>
					<button @click="getChildValues('multipleTree', 'fruits')">获取水果的所有子节点值</button>
				</div>
			</div>

			<!-- 严格模式 -->
			<div class="demo-section">
				<h2>3. 严格模式 (父子节点不关联)</h2>
				<div class="demo-content">
					<x-tree ref="strictlyTree" v-model="strictlySelected" :data="basicData" multiple check-strictly></x-tree>
				</div>
				<div class="demo-info">
					<p>当前选中: {{ strictlySelected }}</p>
				</div>
			</div>

			<!-- 仅叶子节点可选 -->
			<div class="demo-section">
				<h2>4. 仅叶子节点可选</h2>
				<div class="demo-content">
					<x-tree ref="leafOnlyTree" v-model="leafOnlySelected" :data="basicData" multiple leaf-only></x-tree>
				</div>
				<div class="demo-info">
					<p>当前选中: {{ leafOnlySelected }}</p>
				</div>
			</div>

			<!-- 可搜索 -->
			<div class="demo-section">
				<h2>5. 可搜索</h2>
				<div class="demo-content">
					<x-tree ref="filterableTree" v-model="filterableSelected" :data="basicData" filterable></x-tree>
				</div>
				<div class="demo-info">
					<p>当前选中: {{ filterableSelected }}</p>
					<button @click="searchTree('filterableTree', 'apple')">搜索 "apple"</button>
					<button @click="searchTree('filterableTree', '')">清除搜索</button>
				</div>
			</div>

			<!-- 手风琴模式 -->
			<div class="demo-section">
				<h2>6. 手风琴模式</h2>
				<div class="demo-content">
					<x-tree ref="accordionTree" v-model="accordionSelected" :data="basicData" accordion></x-tree>
				</div>
				<div class="demo-info">
					<p>当前选中: {{ accordionSelected }}</p>
				</div>
			</div>

			<!-- 懒加载 -->
			<div class="demo-section">
				<h2>7. 懒加载</h2>
				<div class="demo-content">
					<x-tree ref="lazyTree" v-model="lazySelected" :data="lazyData" :load-data="loadLazyData" @lazy-load-error="handleLazyLoadError"></x-tree>
				</div>
				<div class="demo-info">
					<p>当前选中: {{ lazySelected }}</p>
					<p v-if="lazyLoadError" class="error-message">加载错误: {{ lazyLoadError }}</p>
					<button @click="toggleLazyLoadSuccess">{{ lazyLoadSuccess ? '模拟加载失败' : '模拟加载成功' }}</button>
				</div>
			</div>

			<!-- 自定义操作 -->
			<div class="demo-section">
				<h2>8. 自定义操作</h2>
				<div class="demo-content">
					<x-tree ref="customTree" v-model="customSelected" :data="basicData"></x-tree>
				</div>
				<div class="demo-info">
					<p>当前选中: {{ customSelected }}</p>
					<button @click="getNodePath">获取当前节点路径</button>
					<p v-if="nodePath.length">
						节点路径:
						<span v-for="(path, index) in nodePath" :key="path">
							{{ getNodeLabel(path) }}
							<span v-if="index < nodePath.length - 1">></span>
						</span>
					</p>
				</div>
			</div>

			<!-- 高级用法 - 级联操作 -->
			<div class="demo-section">
				<h2>9. 高级用法 - 级联操作</h2>
				<div class="demo-content">
					<x-tree ref="advancedTree" v-model="advancedSelected" :data="basicData" multiple></x-tree>
				</div>
				<div class="demo-info">
					<p>当前选中: {{ advancedSelected }}</p>
					<div class="button-group">
						<button @click="selectWithChildren">选中节点及其所有子节点</button>
						<button @click="selectWithParents">选中节点及其所有父节点</button>
						<button @click="selectSiblings">选中节点的所有兄弟节点</button>
					</div>
					<div class="button-group">
						<button @click="expandSubtree('advancedTree', operationNodeValue)">展开子树</button>
						<button @click="expandNode('advancedTree', operationNodeValue)">仅展开父路径</button>
						<button @click="collapseAll('advancedTree')">折叠所有</button>
					</div>
					<div class="input-group">
						<input v-model="operationNodeValue" placeholder="输入节点值 (如: fruits)" />
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup>
// 基础数据
const basicData = [
	{
		label: '水果',
		value: 'fruits',
		children: [
			{
				label: '苹果',
				value: 'apple',
			},
			{
				label: '香蕉',
				value: 'banana',
			},
			{
				label: '橙子',
				value: 'orange',
				children: [
					{
						label: '血橙',
						value: 'blood-orange',
					},
					{
						label: '脐橙',
						value: 'navel-orange',
					},
				],
			},
		],
	},
	{
		label: '蔬菜',
		value: 'vegetables',
		children: [
			{
				label: '番茄',
				value: 'tomato',
			},
			{
				label: '土豆',
				value: 'potato',
			},
			{
				label: '胡萝卜',
				value: 'carrot',
			},
		],
	},
	{
		label: '肉类',
		value: 'meat',
		children: [
			{
				label: '牛肉',
				value: 'beef',
			},
			{
				label: '猪肉',
				value: 'pork',
			},
			{
				label: '鸡肉',
				value: 'chicken',
				disabled: true,
			},
		],
	},
]

// 懒加载数据
const lazyData = [
	{
		label: '动态加载1',
		value: 'lazy-1',
		hasChildren: true,
	},
	{
		label: '动态加载2',
		value: 'lazy-2',
		hasChildren: true,
	},
	{
		label: '动态加载3',
		value: 'lazy-3',
		hasChildren: true,
	},
]

// 各个树的选中值
const basicSelected = ref('')
const multipleSelected = ref([])
const strictlySelected = ref([])
const leafOnlySelected = ref([])
const filterableSelected = ref('')
const accordionSelected = ref('')
const lazySelected = ref('')
const customSelected = ref('')
const advancedSelected = ref([])

// 高级操作相关
const operationNodeValue = ref('fruits')

// 树组件引用
const basicTree = ref(null)
const multipleTree = ref(null)
const filterableTree = ref(null)
const lazyTree = ref(null)
const customTree = ref(null)
const advancedTree = ref(null)

// 懒加载相关
const lazyLoadSuccess = ref(true)
const lazyLoadError = ref('')

// 自定义操作相关
const nodePath = ref([])

// 事件处理
const handleNodeClick = (node) => {
	console.log('节点点击:', node)
}

const handleNodeCheck = (node, checked) => {}

const handleLazyLoadError = (node, error) => {
	console.error('懒加载错误:', node, error)
	lazyLoadError.value = error.message || '加载失败'
}

// 懒加载数据
const loadLazyData = (node) => {
	return new Promise((resolve, reject) => {
		setTimeout(() => {
			if (lazyLoadSuccess.value) {
				const children = []
				for (let i = 1; i <= 3; i++) {
					children.push({
						label: `${node.label}-子节点${i}`,
						value: `${node.value}-child-${i}`,
						hasChildren: i === 1, // 只有第一个子节点有子节点
					})
				}
				resolve(children)
			} else {
				reject(new Error('模拟加载失败'))
			}
		}, 1000)
	})
}

// 切换懒加载成功/失败
const toggleLazyLoadSuccess = () => {
	lazyLoadSuccess.value = !lazyLoadSuccess.value
	lazyLoadError.value = ''
}

// 展开指定节点
const expandNode = (treeRef, nodeValue) => {
	const tree = getTreeRef(treeRef)
	if (tree) {
		tree.expandNodePath(nodeValue)
	}
}

// 展开子树
const expandSubtree = (treeRef, nodeValue) => {
	const tree = getTreeRef(treeRef)
	if (tree) {
		tree.expandNodeAndChildren(nodeValue)
	}
}

// 折叠所有节点
const collapseAll = (treeRef) => {
	const tree = getTreeRef(treeRef)
	if (tree && tree.expandedKeys) {
		tree.expandedKeys.clear()
	}
}

// 选中节点
const checkNode = (treeRef, nodeValue) => {
	const tree = getTreeRef(treeRef)
	if (tree) {
		// 获取节点的所有子节点值
		const childValues = tree.getChildNodeValues(nodeValue)
		// 将当前节点和所有子节点添加到选中值中
		multipleSelected.value = [...new Set([nodeValue, ...childValues])]
	}
}

// 取消所有选中
const uncheckAll = (treeRef) => {
	multipleSelected.value = []
}

// 获取子节点值
const getChildValues = (treeRef, nodeValue) => {
	const tree = getTreeRef(treeRef)
	if (tree) {
		const childValues = tree.getChildNodeValues(nodeValue)
		alert(`${nodeValue} 的子节点值: ${childValues.join(', ')}`)
	}
}

// 搜索树
const searchTree = (treeRef, keyword) => {
	const tree = getTreeRef(treeRef)
	if (tree) {
		tree.searchKeyword = keyword
	}
}

// 获取节点路径
const getNodePath = () => {
	if (!customSelected.value) {
		alert('请先选择一个节点')
		return
	}

	const tree = getTreeRef('customTree')
	if (tree) {
		// 获取所有父节点值
		const parentValues = tree.getParentNodeValues(customSelected.value)
		// 构建完整路径（从根到当前节点）
		nodePath.value = [...parentValues.reverse(), customSelected.value]
	}
}

// 获取节点标签
const getNodeLabel = (nodeValue) => {
	const tree = getTreeRef('customTree')
	if (tree) {
		const node = tree.findNodeByValue(nodeValue)
		return node ? node.label : nodeValue
	}
	return nodeValue
}

// 高级操作：选中节点及其所有子节点
const selectWithChildren = () => {
	if (!operationNodeValue.value) {
		alert('请输入节点值')
		return
	}

	const tree = getTreeRef('advancedTree')
	if (tree) {
		// 获取节点的所有子节点值
		const childValues = tree.getChildNodeValues(operationNodeValue.value)
		// 将当前节点和所有子节点添加到选中值中
		advancedSelected.value = [...new Set([operationNodeValue.value, ...childValues])]

		// 展开节点
		tree.expandNodePath(operationNodeValue.value)
	}
}

// 高级操作：选中节点及其所有父节点
const selectWithParents = () => {
	if (!operationNodeValue.value) {
		alert('请输入节点值')
		return
	}

	const tree = getTreeRef('advancedTree')
	if (tree) {
		// 获取节点的所有父节点值
		const parentValues = tree.getParentNodeValues(operationNodeValue.value)
		// 将当前节点和所有父节点添加到选中值中
		advancedSelected.value = [...new Set([...parentValues, operationNodeValue.value])]

		// 展开到当前节点
		tree.expandNodePath(operationNodeValue.value)
	}
}

// 高级操作：选中节点的所有兄弟节点
const selectSiblings = () => {
	if (!operationNodeValue.value) {
		alert('请输入节点值')
		return
	}

	const tree = getTreeRef('advancedTree')
	if (!tree) return

	const node = tree.findNodeByValue(operationNodeValue.value)
	if (!node) {
		alert('未找到节点')
		return
	}

	// 获取父节点
	const parentId = node.parentId
	if (!parentId) {
		// 根节点，获取所有根节点
		const rootValues = tree.filteredData.map((n) => n.value)
		advancedSelected.value = rootValues
	} else {
		// 非根节点，获取同级节点
		const parent = tree.findNodeByValue(parentId)
		if (parent && parent.children) {
			const siblingValues = parent.children.map((n) => n.value)
			advancedSelected.value = siblingValues

			// 展开父节点
			tree.expandNodePath(parentId)
		}
	}
}

// 获取树组件引用
const getTreeRef = (treeRef) => {
	switch (treeRef) {
		case 'basicTree':
			return basicTree.value
		case 'multipleTree':
			return multipleTree.value
		case 'filterableTree':
			return filterableTree.value
		case 'lazyTree':
			return lazyTree.value
		case 'customTree':
			return customTree.value
		case 'advancedTree':
			return advancedTree.value
		default:
			return null
	}
}
</script>

<style scoped>
.tree-demo {
	font-family: Arial, sans-serif;
	max-width: 1200px;
	margin: 0 auto;
	padding: 20px;
}

h1 {
	text-align: center;
	color: #409eff;
	margin-bottom: 30px;
}

.demo-container {
	display: grid;
	grid-template-columns: repeat(auto-fill, minmax(500px, 1fr));
	gap: 20px;
}

.demo-section {
	border: 1px solid #e4e7ed;
	border-radius: 4px;
	overflow: hidden;
}

h2 {
	margin: 0;
	padding: 10px 15px;
	background-color: #f5f7fa;
	border-bottom: 1px solid #e4e7ed;
	font-size: 16px;
	color: #303133;
}

.demo-content {
	padding: 15px;
	min-height: 300px;
	max-height: 400px;
	overflow: auto;
	border-bottom: 1px solid #e4e7ed;
}

.demo-info {
	padding: 15px;
	background-color: #f8f8f8;
}

button {
	margin-right: 8px;
	margin-bottom: 8px;
	padding: 6px 12px;
	background-color: #409eff;
	color: white;
	border: none;
	border-radius: 4px;
	cursor: pointer;
	transition: background-color 0.3s;
}

button:hover {
	background-color: #66b1ff;
}

.error-message {
	color: #f56c6c;
	margin-top: 10px;
}

.button-group {
	margin-bottom: 10px;
}

.input-group {
	margin-top: 10px;
}

.input-group input {
	width: 100%;
	padding: 6px 10px;
	border: 1px solid #dcdfe6;
	border-radius: 4px;
	box-sizing: border-box;
}
</style>
