<template>
	<!--space-y-2-->
	<div class="flex flex-wrap gap-x-sm" :class="[{ 'flex-col': vertical }]">
		<slot>
			<XCheckbox v-for="option in _options" :key="option.value" :label="option.label" :value="option.value" :disabled="option.disabled" />
		</slot>
	</div>
</template>

<script setup lang="ts">
const props = defineProps({
	modelValue: {
		type: Array,
		default: () => [],
		validator: (value) => {
			// 校验 modelValue 是否为数组，且数组中的值为字符串、数字或布尔值
			return Array.isArray(value) && value.every((item) => typeof item === 'string' || typeof item === 'number' || typeof item === 'boolean')
		},
	},
	options: { type: Array, default: () => [] },
	disabled: Boolean,
	vertical: Boolean,
	labelKey: { type: String, default: 'name' },
	valueKey: { type: String, default: 'id' },
	tagGroupName: {
		type: String,
	},
	tagValueFormat: {
		type: String,
		default: 'STRING',
	},
})
const _options = ref()
watchEffect(() => {
	_options.value = formatOptions(props.options)
})
const {
	run: getTagListRun,
	loading: getTagListLoading,
	data: tagList,
} = xUseRequest(BIZ_OPEN_APIS.getTagListStore, `${props.tagGroupName}&format=${props.tagValueFormat}`)
onMounted(async () => {
	if (props.tagGroupName) {
		await getTagListRun()
	}
	_options.value = formatOptions(props.tagGroupName ? tagList.value?.tagList : props.options)
})
function formatOptions(list: any[]) {
	return list?.map((item) => {
		item.label = item[props.labelKey]
		item.value = item[props.valueKey]
		return item
	})
}
const emit = defineEmits(['update:modelValue', 'change'])

// 处理 modelValue 的边界情况
const normalizedModelValue = computed(() => {
	if (props.modelValue == null) {
		// 处理 null 或 undefined
		return []
	}
	if (!Array.isArray(props.modelValue)) {
		console.warn('CheckboxGroup: modelValue 必须是数组')
		return []
	}
	return props.modelValue
})

const updateValue = (value) => {
	const newValue = [...normalizedModelValue.value]
	const index = newValue.indexOf(value)
	index === -1 ? newValue.push(value) : newValue.splice(index, 1)
	emit('update:modelValue', newValue)
}

const handleChange = (e) => {
	emit('change', normalizedModelValue.value)
}

provide('checkboxGroup', {
	modelValue: normalizedModelValue,
	disabled: computed(() => props.disabled),
	updateValue,
	handleChange,
})
</script>
