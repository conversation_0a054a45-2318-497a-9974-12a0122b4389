<template>
	<div class="fixed inset-0 z-9999 flex items-center justify-center bg-black bg-opacity-90">
		<!-- 关闭按钮 -->
		<XButton
			class="!absolute !right-4 !top-4 !z-10 !h-12 !w-12 !rounded-full !bg-black !bg-opacity-50 !p-0 !text-white !transition-all !border-none hover:!bg-opacity-70"
			@click="close"
		>
			<div class="i-bi:x-lg text-xl"></div>
		</XButton>

		<!-- 底部控制栏 -->
		<div
			class="absolute bottom-6 left-1/2 z-10 flex transform items-center gap-4 rounded-full bg-black bg-opacity-60 px-6 py-3 backdrop-blur-sm -translate-x-1/2"
		>
			<!-- 缩放比例显示 -->
			<div class="min-w-12 text-center text-white font-medium text-sm">{{ Math.round(scale * 100) }}%</div>

			<!-- 分隔线 -->
			<div class="h-6 w-px bg-white bg-opacity-30"></div>

			<!-- 缩放控制按钮 -->
			<div class="flex items-center gap-2">
				<XButton
					class="!h-10 !w-10 !rounded-full !bg-transparent !p-0 !text-white !transition-all !border-none hover:!bg-white hover:!bg-opacity-20"
					title="缩小"
					@click="zoomOut"
				>
					<div class="i-bi:zoom-out text-lg"></div>
				</XButton>

				<XButton
					class="!h-10 !w-10 !rounded-full !bg-transparent !p-0 !text-white !transition-all !border-none hover:!bg-white hover:!bg-opacity-20"
					title="重置"
					@click="resetZoom"
				>
					<div class="i-bi:arrow-clockwise text-lg"></div>
				</XButton>

				<XButton
					class="!h-10 !w-10 !rounded-full !bg-transparent !p-0 !text-white !transition-all !border-none hover:!bg-white hover:!bg-opacity-20"
					title="放大"
					@click="zoomIn"
				>
					<div class="i-bi:zoom-in text-lg"></div>
				</XButton>
			</div>
		</div>

		<!-- 图片容器 -->
		<div
			ref="containerRef"
			class="h-full w-full flex cursor-grab items-center justify-center overflow-hidden"
			:class="{ 'cursor-grabbing': isDragging }"
			@mousedown="startDrag"
			@wheel="handleWheel"
		>
			<img
				ref="imageRef"
				:src="imageUrl"
				class="max-w-none select-none transition-transform duration-200 ease-out"
				:style="{
					transform: `translate(${translateX}px, ${translateY}px) scale(${scale})`,
				}"
				@load="handleImageLoad"
				@error="handleImageError"
				@dragstart.prevent
			/>
		</div>

		<!-- 加载状态 -->
		<div v-if="isLoading" class="absolute inset-0 flex items-center justify-center">
			<div class="text-white text-lg">加载中...</div>
		</div>

		<!-- 错误状态 -->
		<div v-if="isError" class="absolute inset-0 flex items-center justify-center">
			<div class="text-white text-lg">图片加载失败</div>
		</div>
	</div>
</template>

<script setup lang="ts">
const props = defineProps({
	imageUrl: {
		type: String,
		required: true,
	},
	imageName: {
		type: String,
		default: '',
	},
})

const emit = defineEmits(['close'])

// 响应式状态
const containerRef = ref<HTMLElement>()
const imageRef = ref<HTMLImageElement>()
const isLoading = ref(true)
const isError = ref(false)

// 缩放和拖拽状态
const scale = ref(1)
const translateX = ref(0)
const translateY = ref(0)
const isDragging = ref(false)

// 拖拽相关
const dragStartX = ref(0)
const dragStartY = ref(0)
const dragStartTranslateX = ref(0)
const dragStartTranslateY = ref(0)

// 缩放控制
const minScale = 0.1
const maxScale = 5
const zoomStep = 0.2

// 关闭预览
const close = () => {
	emit('close')
}

// 放大
const zoomIn = () => {
	const newScale = Math.min(scale.value + zoomStep, maxScale)
	scale.value = newScale
}

// 缩小
const zoomOut = () => {
	const newScale = Math.max(scale.value - zoomStep, minScale)
	scale.value = newScale
}

// 重置缩放和位置
const resetZoom = () => {
	scale.value = 1
	translateX.value = 0
	translateY.value = 0
}

// 处理滚轮缩放
const handleWheel = (event: WheelEvent) => {
	event.preventDefault()

	const delta = event.deltaY > 0 ? -zoomStep : zoomStep
	const newScale = Math.max(minScale, Math.min(maxScale, scale.value + delta))

	if (newScale !== scale.value) {
		// 计算鼠标位置相对于图片的缩放中心
		const rect = containerRef.value?.getBoundingClientRect()
		if (rect) {
			const mouseX = event.clientX - rect.left - rect.width / 2
			const mouseY = event.clientY - rect.top - rect.height / 2

			// 调整平移以保持鼠标位置为缩放中心
			const scaleRatio = newScale / scale.value
			translateX.value = mouseX - (mouseX - translateX.value) * scaleRatio
			translateY.value = mouseY - (mouseY - translateY.value) * scaleRatio
		}

		scale.value = newScale
	}
}

// 开始拖拽
const startDrag = (event: MouseEvent) => {
	if (scale.value <= 1) return // 只有放大时才允许拖拽

	isDragging.value = true
	dragStartX.value = event.clientX
	dragStartY.value = event.clientY
	dragStartTranslateX.value = translateX.value
	dragStartTranslateY.value = translateY.value

	document.addEventListener('mousemove', handleDrag)
	document.addEventListener('mouseup', endDrag)
}

// 处理拖拽
const handleDrag = (event: MouseEvent) => {
	if (!isDragging.value) return

	const deltaX = event.clientX - dragStartX.value
	const deltaY = event.clientY - dragStartY.value

	translateX.value = dragStartTranslateX.value + deltaX
	translateY.value = dragStartTranslateY.value + deltaY
}

// 结束拖拽
const endDrag = () => {
	isDragging.value = false
	document.removeEventListener('mousemove', handleDrag)
	document.removeEventListener('mouseup', endDrag)
}

// 图片加载完成
const handleImageLoad = () => {
	isLoading.value = false
	isError.value = false
}

// 图片加载错误
const handleImageError = () => {
	isLoading.value = false
	isError.value = true
}

// 键盘事件处理
const handleKeydown = (event: KeyboardEvent) => {
	switch (event.key) {
		case 'Escape':
			close()
			break
		case '+':
		case '=':
			zoomIn()
			break
		case '-':
			zoomOut()
			break
		case '0':
			resetZoom()
			break
	}
}

// 生命周期
onMounted(() => {
	document.addEventListener('keydown', handleKeydown)
	// 阻止页面滚动
	document.body.style.overflow = 'hidden'
})

onBeforeUnmount(() => {
	document.removeEventListener('keydown', handleKeydown)
	document.removeEventListener('mousemove', handleDrag)
	document.removeEventListener('mouseup', endDrag)
	// 恢复页面滚动
	document.body.style.overflow = ''
})
</script>

<style scoped>
/* 确保图片不被选中 */
img {
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
}
</style>
