<script setup lang="ts">
// Define interfaces for menu and tree data
interface MenuItem {
	id: number
	name: string
	path: string
	parentId?: number
	visible?: boolean
	fixed?: boolean
	keepAlive?: boolean
	alwaysShow?: boolean
	children?: MenuItem[]
	componentName?: string // Optional explicit component name
}

interface TreeNode {
	value: number
	label: string
	path: string
	visible?: boolean
	keepAlive?: boolean
	alwaysShow?: boolean
	children?: TreeNode[]
	_path_path?: string
	_path_label?: string
	componentName?: string // Optional explicit component name
}
const homeNode = {
	value: 0,
	icon: 'i-carbon:home',
	label: '首页',
	path: '/',
	parentId: 0,
	visible: true,
	fixed: true,
}

function createNotMenuNode() {
	return {
		value: route.path,
		label: route.params?.name || route.query?.name || route.meta.title || route.name,
		_path_path: route.path,
		path: route.path,
		componentName: route.name,
		parentId: 0,
		visible: true,
		keepAlive: route.meta?.keepAlive,
	}
}
const userStore = useUserStore()
userStore.platform = 'admin'
const route = useRoute()
const router = useRouter()
const menuList = ref<TreeNode[]>([])
async function initMenu() {
	const res = await BIZ_Menu_APIS.getPermissionMenus()
	userStore.setUserinfo(res.user)
	const _list = res?.menus || res || ([] as MenuItem[])
	_list.unshift(homeNode)
	menuList.value = _list
}
const accessedNodeList = ref<TreeNode[]>([])
const selectedNodeValue = ref<number>()
const XTreeRef = ref()

function processMenuNodeByNodePath(nodePath) {
	let node = XTreeRef.value.findNodeByFullPath(nodePath)
	const label = route.params?.name || route.query?.name
	console.log(nodePath, node, label)
	if (!node || label) {
		node = createNotMenuNode()
	}
	selectedNodeValue.value = node?.value
	addToAccessedNodeList(node)
}

onMounted(async () => {
	await initMenu()
	await nextTick()
	handlePopState()
	// 添加浏览器历史导航事件监听
	window.addEventListener('popstate', handlePopState)
})

// 处理浏览器前进/后退事件
function handlePopState() {
	processMenuNodeByNodePath(route.path)
}

// 组件卸载时移除事件监听
onUnmounted(() => {
	window.removeEventListener('popstate', handlePopState)
})

function addToAccessedNodeList(item: TreeNode | undefined) {
	if (item && !accessedNodeList.value.some((node) => item.value == node.value)) {
		accessedNodeList.value.push(item)
	}
}

const computedSelectedNodeFullPath = computed(() => {
	return XTreeRef.value?.findNodeLabelPathByValue(selectedNodeValue.value)
})

// 需要缓存的组件名称列表
const cachedViews = computed(() => {
	// 获取所有需要缓存的节点
	const nodesToCache = accessedNodeList.value.filter((node) => node.keepAlive)

	// 为每个节点生成多种可能的组件名称格式
	const componentNames: Set<string> = new Set()

	nodesToCache.forEach((node) => {
		if (node.componentName) {
			componentNames.add(node.componentName)
		}

		let split = node._path_path.split('/')
		let path = split?.[split.length - 1]
		if (path) componentNames.add(path)

		if (node.path) componentNames.add(node.path)
	})
	return Array.from(componentNames)
})

// 监听路由变化，自动添加新访问的页面到缓存列表
watch(
	() => route.path,
	(newPath) => {
		processMenuNodeByNodePath(newPath)
	},
)

// 开发环境下的调试信息
const appName = import.meta.env.VITE_APP_TITLE

// 刷新当前标签页
const reloadFlag = ref(true)
function refreshCurrentTab() {
	// 找到当前选中的节点
	const currentNode = accessedNodeList.value.find((node) => node.value === selectedNodeValue.value)
	if (currentNode) {
		// 如果节点存在，先禁用keepAlive缓存
		const wasKeptAlive = currentNode.keepAlive
		currentNode.keepAlive = false

		// 强制组件重新渲染
		reloadFlag.value = false
		nextTick(() => {
			// 恢复原来的keepAlive状态
			if (wasKeptAlive) {
				currentNode.keepAlive = true
			}
			reloadFlag.value = true
		})
	}
}

// 刷新指定标签页
function refreshTab(nodeValue: number) {
	// 保存当前选中的标签值
	const currentValue = selectedNodeValue.value

	// 切换到要刷新的标签
	selectedNodeValue.value = nodeValue

	// 刷新当前标签
	refreshCurrentTab()

	// 如果刷新的不是当前标签，刷新完后切回原标签
	if (currentValue !== nodeValue) {
		nextTick(() => {
			selectedNodeValue.value = currentValue
		})
	}
}
</script>

<template>
	<div class="grid grid-cols-[14rem_1fr] h100vh wfull overflow-hidden bg-page color-content">
		<div class="grid grid-rows-[4rem_1fr] overflow-hidden bg-white">
			<!--			#f9fbff-->
			<div class="flex items-center justify-center overflow-hidden color-title font-bold">
				<img class="w-100% flex shrink-0" src="/logo.png" alt="" />
			</div>
			<div class="overflow-y-auto">
				<XTree
					v-if="menuList?.length > 0"
					ref="XTreeRef"
					v-model="selectedNodeValue"
					accordion
					leaf-only
					:is-flat-data="false"
					expand-selected
					:data="menuList"
					@node-check="
						(node: TreeNode) => {
							node._path_path && router.push(node._path_path)
							addToAccessedNodeList(node)
						}
					"
				/>
			</div>
		</div>
		<div class="relative grid grid-rows-[4rem_auto_1fr] overflow-hidden">
			<div class="flex items-center justify-between overflow-hidden bg-white border-b-border">
				<div class="flex items-center pl-base gap-base"></div>
				<div class="flex flex-1 justify-center text-title font-bold tracking-0.2rem text-xl">{{ appName }}</div>
				<div class="flex items-center justify-end overflow-hidden pl-sm pr-xs gap-base">
					<div>
						<x-button class="!h2rem !w2rem !rounded-full !bg-tip !p-0">
							<div class="refresh-icon i-ic:baseline-notifications text-lg !color-content"></div>
						</x-button>
					</div>
					<BizAvatar size="2.3rem"></BizAvatar>
				</div>
			</div>
			<!--menu tab-->
			<div class="h-2.5rem flex items-center overflow-hidden bg-white !rounded-lg">
				<BizMenuTab v-model="selectedNodeValue" :data="accessedNodeList" @refresh="refreshTab" />
				<XIconsRefresh class="cursor-pointer mr-xs !color-content" @click="refreshCurrentTab"></XIconsRefresh>
			</div>
			<div class="relative overflow-hidden">
				<router-view v-if="reloadFlag" v-slot="{ Component }">
					<x-transition immediate class="overflow-hidden !absolute !hfull !wfull" mode="fade" direction="left" :duration="0.65">
						<keep-alive :include="cachedViews">
							<component :is="Component" :key="route.params.id" class="overflow-hidden !absolute !hfull !wfull" />
						</keep-alive>
					</x-transition>
				</router-view>
			</div>
		</div>
	</div>
</template>

<style scoped></style>
