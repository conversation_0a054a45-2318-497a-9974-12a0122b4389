import { defineStore } from 'pinia'

export default defineStore('common-tag', {
	state() {
		return {
			map: new Map(),
		}
	},
	actions: {
		async getListStore(name) {
			if (!this.map?.has) {
				this.map = new Map()
			}
			if (!this.map.has(name)) {
				await BIZ_OPEN_APIS.getTagList(name)
					.then((res) => {
						const tagList = res.tagList?.map((item) => ({ id: item.value, name: item.label })) || []
						this.map.set(name, { tagList: tagList })
					})
					.catch(() => {
						this.map.set(name, { tagList: [] })
					})
			}
			return this.map.get(name)
		},
	},
	persist: true,
})
