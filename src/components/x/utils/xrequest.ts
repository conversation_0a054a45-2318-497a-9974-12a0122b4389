import axios from 'axios'
import qs from 'qs'
export const http = axios.create({
	baseURL: import.meta.env.VITE_API_BASE_URL || `${window.location.protocol}//${window.location.hostname}:48080`,
	paramsSerializer: (params) =>
		qs.stringify(params, {
			arrayFormat: 'brackets',
			encode: true,
			skipNulls: true,
		}),
})

// 添加请求拦截器
http.interceptors.request.use(
	function (config) {
		if (useUserStore().token) {
			config.headers.Authorization = `Bearer ${useUserStore().token}`
		}
		const data = config.data
		if (data) {
			// 过滤掉null值
			Object.keys(data).forEach((key) => {
				if (data[key] === null) {
					delete data[key]
				} else if (key.includes('Tags') && data[key]) {
					if (data[key]?.length > 0) {
						data[key] = data[key]?.join(',')
					} else {
						delete data[key]
					}
				}
			})
		}
		// 过滤掉params中的null值
		if (config.params) {
			Object.keys(config.params).forEach((key) => {
				if (config.params[key] === null) {
					delete config.params[key]
				}
			})
		}
		// 在发送请求之前做些什么
		return config
	},
	function (error) {
		toast.error(error.message ?? '未知请求错误')
		// 对请求错误做些什么
		return Promise.reject(error)
	},
)

// 添加响应拦截器
http.interceptors.response.use(
	function (response) {
		const code = response.data?.code || response?.code
		if (code && code != 0) {
			switch (code) {
				case 401:
					useUserStore().logout(true)
					return Promise.reject(response)
			}
			const msg = response.data?.msg || '未知错误'
			toast.warning(msg)
			return Promise.reject(response)
		}
		if (response.data?.data !== undefined) {
			const data = response.data.data
			if (data?.list?.length > 0) {
				data.list?.forEach((item, index) => processResponseData(item, index, response))
			} else {
				processResponseData(data)
			}
			return response.data.data
		}

		return response.data
	},
	function (error) {
		const msg = error.message || error?.msg || '网络异常'
		toast.warning(msg)
		return Promise.reject(error)
	},
)
const processResponseData = (data: any, index, response) => {
	if (!data) return
	// 分页序号处理
	if (index > -1 && response) {
		const params = response.config?.params
		const pageNo = params.pageNo || 1
		const pageSize = params.pageSize || 10
		data._no = (pageNo - 1) * pageSize + index + 1
	}
	// tags处理
	Object.keys(data).forEach((key) => {
		if (key.includes('Tags') && data[key]) {
			data[key] = data[key].split(',')
		}
	})
}
export const xRequest = {
	get(url: string, params = {}, headers = {}): Promise<any> {
		return http.get(url, {
			params,
			headers,
		})
	},
	delete(url: string, params = {}, headers = {}): Promise<any> {
		return http.delete(url, {
			params,
			headers,
		})
	},
	post(url: string, data = {} as any, headers = {}): Promise<any> {
		return http.post(url, data, {
			headers,
		})
	},
	put(url: string, data = {} as any, headers = {}): Promise<any> {
		return http.put(url, data, {
			headers,
		})
	},
	download(url: string, params = {}, headers = {}): Promise<any> {
		return http.get(url, {
			params,
			headers,
			responseType: 'blob',
		})
	},
}

export function xUploadFile(file: File) {
	const formData = new FormData()
	formData.append('file', file, file.name)
	/*const res = await axios.post(import.meta.env.VITE_UPLOAD_BASE_URL, formData, {
		params,
		headers: {
			'Content-Type': 'multipart/form-data',
		},
	})*/
	return http.post(import.meta.env.VITE_UPLOAD_BASE_URL, formData, {
		'Content-Type': 'multipart/form-data',
	})
}

export async function xLoadJsonData(url) {
	try {
		const response = await fetch(url)
		if (!response.ok) {
			throw new Error('Network response was not ok')
		}
		return response.json() // 等待解析为 JSON 对象
	} catch (error) {
		console.error('There was a problem with the fetch operation:', error)
		throw error // 返回错误
	}
}
