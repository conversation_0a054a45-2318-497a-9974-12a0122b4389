// import 'vue-toastification/dist/index.css'
// import { createToastInterface } from 'vue-toastification'
//
// export default createToastInterface()
import { createApp } from 'vue'
import ToastContainer from '../ToastContainer/ToastContainer.vue'
import ConfirmModal from '../ConfirmModal/ConfirmModal.vue'
import PageLoading from '../PageLoading/PageLoading.vue'

// 定义 service 的类型
interface ToastService {
	// eslint-disable-next-line no-unused-vars
	show: (message: any) => void
	// eslint-disable-next-line no-unused-vars
	info: (message: any) => void
	// eslint-disable-next-line no-unused-vars
	success: (message: any) => void
	// eslint-disable-next-line no-unused-vars
	warning: (message: any) => void
	// eslint-disable-next-line no-unused-vars
	error: (message: any) => void
	// eslint-disable-next-line no-unused-vars
	confirm: (message: any, options: {}) => Promise<boolean>
	// eslint-disable-next-line no-unused-vars
	loading: (message?: any) => void
	closeLoading: () => void
}

// 初始化 service 对象
const service: ToastService = {} as ToastService

export const toastInstall = {
	install() {
		// confirm modal
		const container2 = document.createElement('div')
		document.body.appendChild(container2)
		const confirmApp = createApp(ConfirmModal, {})
		const confirmInstance = confirmApp.mount(container2)
		service.confirm = (content, options = {}) => confirmInstance.open({ content, ...options })

		// page loading
		const container3 = document.createElement('div')
		document.body.appendChild(container3)
		const loadingApp = createApp(PageLoading, {})
		const loadingInstance = loadingApp.mount(container3)
		service.loading = (message?: any) => loadingInstance.show(message)
		service.closeLoading = () => loadingInstance.hide()

		// toast
		const container = document.createElement('div')
		document.body.appendChild(container)

		const toastApp = createApp(ToastContainer, {})
		const instance = toastApp.mount(container)

		// 使用 Object.assign 将方法直接赋值给 service 对象
		Object.assign(service, {
			show: (message: any) => instance.addToast(message, 'info'),
			info: (message: any) => instance.addToast(message, 'info'),
			success: (message: any) => instance.addToast(message, 'success'),
			warning: (message: any) => instance.addToast(message, 'warning'),
			error: (message: any) => instance.addToast(message, 'error'),
		})
	},
}

export const toast = service
