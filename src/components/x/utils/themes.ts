export function xThemeSetCssVar(key: string, value: string) {
	document.documentElement.style.setProperty(`${key}`, value)
}
export const themeColors = {
	primary: '#EC6E00',
	primaryBg: '#fff6ee',
	primaryLight: '#fff6ee',
	success: '#34d19d',
	warning: '#b88302',
	danger: '#b60000',
	error: '#c10e0e',
	info: '#b6b6b6',
	hover: '#f5f5f5',
	page: '#f3f4f6',
	title: '#333333',
	content: 'rgba(21, 39, 77, 1)',
	secondary: '#28395D',
	aid: '#b6b6b6',
	tip: '#F4F4F5',
	border: '#E4E4E7',
	inputBorder: '#E4E4E7',
	inputBg: '#fff',
	inputText: '#323C59',
	inputPlaceholder: '#b6b6b6',
	borderLight: '#e8e8e8',
	bg: '#082662',
	disabled: '#a7a7a7',
	loading: '#bfbfbf',
	link: '#003181',
	test: 'red',

	// ============ Table Theme ============
	tableBg: 'none',
	tableHeaderBg: '#f9fafb',
	tableHeaderText: '#111827',
	tableText: '#1f2937',
	tableBorder: '#e5e7eb',
	tableRowHoverBg: '#f4f5f7',
	tableFixedColBg: '#ffffff',
}
export const fontSizes = {
	xxs: '0.65rem',
	xs: '0.75rem',
	sm: '0.90rem',
	base: '1rem',
	lg: '1.2rem',
	xl: '1.5rem',
	'2xl': '2rem',
	'3xl': '3rem',
}
export const gapSizes = {
	xxs: '0.2rem',
	xs: '0.5rem',
	sm: '0.8rem',
	base: '1rem',
	lg: '1.2rem',
	xl: '1.5rem',
	'2xl': '2rem',
	'3xl': '3rem',
}

export const directions = ['bottom', 'top', 'left', 'right']

// 色阶值（支持1-9和100-900）
export const colorScales = [50, 100, 150, 200, 250, 300, 350, 400, 450, 500, 550, 600, 650, 700, 750, 800, 850, 900, 950]

// 透明度使用 _
export const colorOpacity = [1, 3, 5, 7, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 70, 75, 80, 85, 90, 95, 100]

export const allMenuIcons = [
	'i-solar:camera-bold',
	'i-carbon:folder',
	'i-carbon:document',
	'i-material-symbols:file-copy-outline',
	'i-carbon:action-usage',
	'i-carbon:document-multiple-02',
	'i-carbon:airport-location',
	'i-carbon:settings-services',
	'i-ph:truck-trailer',
	'i-healthicons:truck-driver',
	'i-material-symbols:android-emergency-location-service-outline',
	'i-fluent:vehicle-truck-bag-20-regular',
	'i-material-symbols:route',
	'i-gis:route',
	'i-material-symbols:brightness-alert-outline-rounded',
	'i-tdesign:gps',
	'i-mingcute:announcement-line',
]
