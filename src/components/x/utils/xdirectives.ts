import XLoading from '@/components/x/Loading/Loading.vue'
import { App, createApp, nextTick } from 'vue'

function insertLoading(el) {
	// 获取el的宽高
	const app = createApp(XLoading, { customStyle: 'height:100%;width:100%;' })
	// 设置XLoading的高度
	// 设置app的背景色
	const loadingDom = document.createElement('div')
	loadingDom.style.position = 'absolute'
	loadingDom.style.zIndex = '10'
	loadingDom.style.top = '0'
	loadingDom.style.left = '0'
	loadingDom.style.width = '100%'
	loadingDom.style.height = '100%'
	loadingDom.style.backgroundColor = 'rgba(255,255,255,0.3)'
	loadingDom.setAttribute('data-v', 'xloading')
	// 继承父亲的圆角
	loadingDom.style.borderRadius = 'inherit'
	const instance = app.mount(loadingDom)
	loadingDom.appendChild(instance.$el)
	el.appendChild(loadingDom)
}

function findLoadingElement(el) {
	// 查找所有子节点中带有data-v="xloading"属性的元素
	for (let i = 0; i < el.children.length; i++) {
		if (el.children[i].getAttribute('data-v') === 'xloading') {
			return el.children[i]
		}
	}
	return null
}

const loading = {
	async beforeMount(el, binding) {
		el.style.position = 'relative'
		// 创建一个新的 Vue 应用实例并挂载到当前元素
		if (binding.value === true && !findLoadingElement(el)) {
			insertLoading(el)
		}
	},
	async updated(el, binding) {
		await nextTick()
		const loadingElement = findLoadingElement(el)
		if ((binding.value === false || !binding.value) && loadingElement) {
			el.removeChild(loadingElement)
			return
		}
		//v-loading 的值为true，并且该节点下没有loading节点时，调用函数，挂载loading
		if (binding.value === true && !loadingElement) {
			insertLoading(el)
		}
	},
}

// 点击div外部触发事件
const clickOutside = {
	beforeMount(el, binding) {
		el.clickOutsideEvent = (event) => {
			// 点击的元素是否在el内部
			if (!(el === event.target || el.contains(event.target))) {
				if (typeof binding.value === 'function') {
					binding.value(event)
				}
			}
		}
		document.body.addEventListener('click', el.clickOutsideEvent)
	},
	unmounted(el) {
		document.body.removeEventListener('click', el.clickOutsideEvent)
	},
}

export default {
	install: (app: App) => {
		app.directive('loading', loading)
		app.directive('clickOutside', clickOutside)
	},
}
