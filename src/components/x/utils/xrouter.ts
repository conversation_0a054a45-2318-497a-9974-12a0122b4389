/*
 * 守卫
 * */
import router from '~/plugins/router'
const whitePages = ['login', 'userLogin', 'webHome', 'zsjyQuestionBank', 'zsjyPaperList', '/data2word']

/**
 * 处理URL中的token自动登录
 * @param to 目标路由
 * @param userStore 用户store
 * @returns 是否处理了token登录
 */
function handleTokenAutoLogin(to: any, userStore: any): boolean {
	// 检查URL参数中是否有token
	const urlToken = to.query.token || to.query.accessToken

	if (urlToken && typeof urlToken === 'string') {
		console.log('检测到URL中的token，正在进行自动登录...')

		// 保存token到store
		userStore.token = urlToken

		// 创建新的查询参数对象，移除token相关参数
		const newQuery = { ...to.query }
		delete newQuery.token
		delete newQuery.accessToken

		// 如果没有其他查询参数，则设置为空对象
		const hasOtherParams = Object.keys(newQuery).length > 0

		// 重定向到相同路径但不包含token参数的URL
		router.replace({
			path: to.path,
			query: hasOtherParams ? newQuery : {},
			hash: to.hash,
		})

		console.log('token自动登录完成，已清除URL中的token参数')
		return true
	}

	return false
}

router.beforeEach((to, from, next) => {
	const userStore = useUserStore()
	// 处理URL中的token自动登录
	if (handleTokenAutoLogin(to, userStore)) {
		// 如果处理了token登录，直接返回，等待重定向完成
		return
	}

	if (userStore.token) {
		if (to.name.includes('Login')) {
			goHome()
		}
	} else {
		if (!whitePages.includes(to.name) && !to.name.includes('Login')) {
			if (to.name.includes('web')) {
				userStore.platform = 'web'
			} else {
				userStore.platform = 'admin'
			}
			goLogin()
		}
	}
	next()
})

export function goLogin() {
	const userStore = useUserStore()
	if (userStore.platform === 'admin') {
		router.push({
			name: 'adminLogin',
		})
	}
	if (userStore.platform === 'web') {
		router.replace({
			name: 'webLogin',
		})
	}
}

export function goBack() {
	router.go(-1)
}

export function goHome() {
	const userStore = useUserStore()
	if (userStore.platform === 'admin') {
		router.push({
			name: 'adminHome',
		})
	}
	if (userStore.platform === 'web') {
		router.push({
			name: 'webHome',
		})
	}
}

export function goMy() {
	const userStore = useUserStore()
	if (userStore.platform === 'admin') {
		router.push({
			name: 'adminMy',
		})
	}
	if (userStore.platform === 'web') {
		router.push({
			name: 'webMy',
		})
	}
}

/*
 * biz
 * */
export function goZsjyPaperList(query = {}) {
	router.push({
		name: 'zsjyPaperList',
		query,
	})
}
export function goZsjyExam(query) {
	router.push({
		name: 'examHome',
		query,
	})
}
export function replaceZsjyExam(query) {
	router.push({
		name: 'examHome',
		query,
	})
}
export function goZsjyQuestionBank(query) {
	router.push({
		name: 'zsjyQuestionBank',
		query,
	})
}
export function replaceZsjyExamRules(query) {
	router.push({
		name: 'examRules',
		query,
	})
}
export function goZsjyPaperResult(recordId, userId) {
	router.push({
		name: 'paperResult',
		query: userId > 0 ? { recordId, userId } : { recordId },
	})
}

export function replaceZsjyPaperResult(recordId) {
	router.replace({
		name: 'paperResult',
		query: { recordId, fp: 'paper' },
	})
}
