import ImagePreviewModal from '../ImagePreviewModal/ImagePreviewModal.vue'

// 定义 ImagePreviewService 的类型
interface ImagePreviewService {
	/**
	 * 预览图片
	 * @param imageUrl 图片URL
	 * @param imageName 可选的图片名称
	 */
	preview: (imageUrl: string, imageName?: string) => void

	/**
	 * 关闭预览
	 */
	close: () => void
}

// 初始化 service 对象
const service: ImagePreviewService = {} as ImagePreviewService

// 全局维护的实例
let appInstance: any = null
let containerElement: HTMLElement | null = null

export const imagePreviewInstall = {
	install() {
		// 使用 Object.assign 将方法直接赋值给 service 对象
		Object.assign(service, {
			preview: (imageUrl: string, imageName?: string) => {
				// 如果已有实例，先销毁它
				service.close()

				// 创建DOM容器
				containerElement = document.createElement('div')
				document.body.appendChild(containerElement)

				// 创建应用实例
				const app = createApp({
					setup() {
						const handleClose = () => {
							service.close()
						}

						return {
							imageUrl,
							imageName: imageName || '',
							handleClose,
						}
					},
					render() {
						return h(ImagePreviewModal, {
							imageUrl: this.imageUrl,
							imageName: this.imageName,
							onClose: this.handleClose,
						})
					},
				})

				// 挂载应用
				appInstance = app.mount(containerElement)
			},

			close: () => {
				if (appInstance && containerElement) {
					// 卸载应用
					appInstance.unmount?.()

					// 移除DOM
					containerElement.remove()

					// 清理引用
					appInstance = null
					containerElement = null
				}
			},
		})
	},
}

// 导出服务
export const ximagepreview = service
