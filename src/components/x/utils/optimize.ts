export function xUseThrottle<T extends Function>(fn: T, wait: number = 500, isImmediate: boolean = true): () => void {
	let flag = true
	return function (...args: any[]): void {
		if (flag === true) {
			const context = this
			flag = false
			if (isImmediate) {
				fn.apply(context, args)
			}
			setTimeout(() => {
				if (!isImmediate) {
					fn.apply(context, args)
				}
				flag = true
			}, wait)
		}
	}
}

export const xUseDebounce = <T extends () => void>(fn: T, wait: number = 500, isImmediate: boolean = false): (() => void) => {
	let timerId: ReturnType<typeof setTimeout> | null = null

	return function (this: any, ...args: any[]): void {
		const callNow = isImmediate && !timerId

		if (timerId) {
			clearTimeout(timerId)
		}

		timerId = setTimeout(() => {
			timerId = null
			if (!isImmediate) {
				fn.apply(this, args)
			}
		}, wait)

		if (callNow) {
			fn.apply(this, args)
		}
	}
}
