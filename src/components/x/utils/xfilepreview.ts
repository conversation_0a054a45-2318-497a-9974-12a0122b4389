import { createApp, h, ref, watch } from 'vue'
import FilePreviewModal from '../FilePreviewModal/FilePreviewModal.vue'

// 定义 FilePreviewService 的类型
interface FilePreviewService {
	/**
	 * 预览文件
	 * @param fileUrl 文件URL
	 * @param fileName 可选的文件名
	 */
	preview: (fileUrl: string, fileName?: string) => void

	/**
	 * 关闭预览
	 */
	close: () => void
}

// 初始化 service 对象
const service: FilePreviewService = {} as FilePreviewService

// 全局维护的实例
let appInstance: any = null
let containerElement: HTMLElement | null = null

export const filePreviewInstall = {
	install() {
		// 使用 Object.assign 将方法直接赋值给 service 对象
		Object.assign(service, {
			preview: (fileUrl: string, fileName?: string) => {
				// 如果已有实例，先销毁它
				service.close()

				// 创建DOM容器
				containerElement = document.createElement('div')
				document.body.appendChild(containerElement)
				// 创建应用实例
				const app = createApp({
					setup() {
						const visible = ref(true)

						watch(visible, (newVal) => {
							if (!newVal) {
								service.close()
							}
						})

						return {
							visible,
							fileUrl,
							fileName: fileName || '',
						}
					},
					render() {
						return h(FilePreviewModal, {
							fileUrl: this.fileUrl,
							fileName: this.fileName,
							modelValue: this.visible,
							'onUpdate:modelValue': (val: boolean) => {
								this.visible = val
							},
						})
					},
				})

				// 挂载应用
				appInstance = app.mount(containerElement)
			},

			close: () => {
				if (appInstance && containerElement) {
					// 卸载应用
					appInstance.unmount?.()

					// 移除DOM
					containerElement.remove()

					// 清理引用
					appInstance = null
					containerElement = null
				}
			},
		})
	},
}

// 导出服务
export const xfilepreview = service
