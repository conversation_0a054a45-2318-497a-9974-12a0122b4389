<template>
	<div class="swiper-demo">
		<h2>轮播图组件示例</h2>

		<div class="demo-section">
			<h3>基础用法</h3>
			<x-swiper :slides-count="images.length" height="300px" transition="rebound">
				<template #default="{ index }">
					<div class="slide-content">
						<img :src="images[index]" alt="Slide image" />
					</div>
				</template>
			</x-swiper>
		</div>
		<x-transition mode="rebound"></x-transition>

		<div class="demo-section">
			<h3>使用 slides 数组（简单字符串数组）</h3>
			<x-swiper :slides="images" height="300px" transition="slide" />
		</div>

		<div class="demo-section">
			<h3>使用 slides 数组（复杂对象数组）</h3>
			<x-swiper :slides="complexSlides" height="300px" transition="fade" />
		</div>

		<div class="demo-section">
			<h3>不同过渡效果</h3>
			<div class="transition-controls">
				<div class="control-group">
					<label>过渡效果:</label>
					<select v-model="transitionType">
						<option value="fade">淡入淡出</option>
						<option value="slide">滑动</option>
						<option value="scale">缩放</option>
						<option value="flip">翻转</option>
						<option value="elastic">弹性</option>
						<option value="rebound">回弹</option>
						<option value="bounce">弹跳</option>
					</select>
				</div>

				<div class="control-group">
					<label>过渡方向:</label>
					<select v-model="transitionDirection">
						<option value="left">左</option>
						<option value="right">右</option>
						<option value="up">上</option>
						<option value="down">下</option>
						<option value="center">中心</option>
					</select>
				</div>
			</div>

			<x-swiper :slides-count="images.length" height="300px" :transition="transitionType" :direction="transitionDirection">
				<template #default="{ index }">
					<div class="slide-content">
						<img :src="images[index]" alt="Slide image" />
					</div>
				</template>
			</x-swiper>
		</div>

		<div class="demo-section">
			<h3>自定义导航和分页</h3>
			<x-swiper :slides-count="images.length" height="300px" transition="fade">
				<template #default="{ index }">
					<div class="slide-content">
						<img :src="images[index]" alt="Slide image" />
						<div class="slide-caption">幻灯片 {{ index + 1 }}</div>
					</div>
				</template>

				<template #prev-arrow>
					<div class="custom-arrow custom-arrow--prev">
						<span>上一张</span>
					</div>
				</template>

				<template #next-arrow>
					<div class="custom-arrow custom-arrow--next">
						<span>下一张</span>
					</div>
				</template>

				<template #pagination-bullet="{ index, active }">
					<div class="custom-bullet" :class="{ 'custom-bullet--active': active }">
						{{ index + 1 }}
					</div>
				</template>
			</x-swiper>
		</div>

		<div class="demo-section">
			<h3>手动控制</h3>
			<div class="manual-controls">
				<button @click="swiperRef?.prev()">上一张</button>
				<button @click="swiperRef?.next()">下一张</button>
				<button @click="swiperRef?.goTo(2)">跳转到第3张</button>
				<button @click="swiperRef?.startAutoplay()">开始自动播放</button>
				<button @click="swiperRef?.stopAutoplay()">停止自动播放</button>
			</div>

			<x-swiper ref="swiperRef" :slides-count="images.length" height="300px" :autoplay="false">
				<template #default="{ index }">
					<div class="slide-content">
						<img :src="images[index]" alt="Slide image" />
						<div class="slide-number">{{ index + 1 }} / {{ images.length }}</div>
					</div>
				</template>
			</x-swiper>
		</div>
	</div>
</template>

<script setup lang="ts">
import { ref, h } from 'vue'
import XSwiper from './Swiper.vue'

// 示例图片数组
const images = [
	'https://picsum.photos/id/1018/800/400',
	'https://picsum.photos/id/1015/800/400',
	'https://picsum.photos/id/1019/800/400',
	'https://picsum.photos/id/1016/800/400',
	'https://picsum.photos/id/1020/800/400',
]

// 复杂幻灯片数组示例
const complexSlides = [
	// 图片类型带标题
	{
		type: 'image',
		value: 'https://picsum.photos/id/1018/800/400',
		title: '美丽的山脉',
		alt: '山脉图片',
	},
	// HTML 类型
	{
		type: 'html',
		value:
			'<div style="display: flex; flex-direction: column; align-items: center; justify-content: center; height: 100%; background-color: #f0f8ff;"><h2 style="color: #4a90e2;">HTML 内容</h2><p style="color: #333;">这是一个使用 HTML 的幻灯片</p></div>',
	},
	// 纯文本类型
	'这是一个纯文本幻灯片',
	// 图片 URL 字符串（自动识别为图片类型）
	'https://picsum.photos/id/1015/800/400',
	// 带背景色的文本对象
	{
		type: 'text',
		value: '这是一个带有自定义样式的文本幻灯片',
		props: {
			style: 'background-color: #e6f7ff; color: #0066cc; font-size: 24px; font-weight: bold;',
		},
	},
]

// 过渡效果和方向
const transitionType = ref('fade')
const transitionDirection = ref('left')

// Swiper 引用
const swiperRef = ref(null)
</script>

<style scoped>
.swiper-demo {
	max-width: 800px;
	margin: 0 auto;
	padding: 20px;
}

h2 {
	text-align: center;
	margin-bottom: 30px;
}

.demo-section {
	margin-bottom: 40px;
}

h3 {
	margin-bottom: 15px;
}

.slide-content {
	position: relative;
	width: 100%;
	height: 100%;
	display: flex;
	justify-content: center;
	align-items: center;
}

.slide-content img {
	width: 100%;
	height: 100%;
	object-fit: cover;
}

.slide-caption {
	position: absolute;
	bottom: 20px;
	left: 0;
	width: 100%;
	text-align: center;
	color: white;
	font-size: 18px;
	font-weight: bold;
	background-color: rgba(0, 0, 0, 0.5);
	padding: 10px 0;
}

.slide-number {
	position: absolute;
	top: 10px;
	right: 10px;
	background-color: rgba(0, 0, 0, 0.5);
	color: white;
	padding: 5px 10px;
	border-radius: 4px;
}

.transition-controls {
	display: flex;
	gap: 20px;
	margin-bottom: 15px;
}

.control-group {
	display: flex;
	align-items: center;
	gap: 10px;
}

select {
	padding: 5px 10px;
	border-radius: 4px;
	border: 1px solid #ccc;
}

.manual-controls {
	display: flex;
	gap: 10px;
	margin-bottom: 15px;
	flex-wrap: wrap;
}

.manual-controls button {
	padding: 8px 12px;
	background-color: #4a90e2;
	color: white;
	border: none;
	border-radius: 4px;
	cursor: pointer;
	transition: background-color 0.3s;
}

.manual-controls button:hover {
	background-color: #357abf;
}

.custom-arrow {
	display: flex;
	justify-content: center;
	align-items: center;
	width: 80px;
	height: 40px;
	background-color: rgba(0, 0, 0, 0.6);
	color: white;
	border-radius: 4px;
	cursor: pointer;
	transition: background-color 0.3s;
}

.custom-arrow:hover {
	background-color: rgba(0, 0, 0, 0.8);
}

.custom-bullet {
	display: flex;
	justify-content: center;
	align-items: center;
	width: 24px;
	height: 24px;
	background-color: rgba(0, 0, 0, 0.3);
	color: white;
	border-radius: 50%;
	margin: 0 4px;
	font-size: 12px;
	transition: all 0.3s;
}

.custom-bullet--active {
	background-color: #4a90e2;
	transform: scale(1.2);
}
</style>
