<template>
	<div
		ref="swiperRef"
		class="x-swiper"
		:class="{ 'x-swiper--fullscreen': fullscreen }"
		:style="{ height }"
		tabindex="0"
		role="region"
		@mouseenter="autoplay && stopAutoplay()"
		@mouseleave="autoplay && startAutoplay()"
		@keydown.left="navigation && prev()"
		@keydown.right="navigation && next()"
		@keydown.space.prevent="toggleAutoplay"
	>
		<div ref="containerRef" class="x-swiper__container">
			<div class="x-swiper__wrapper" :style="wrapperStyle" @mousedown="enableDrag && startDrag($event)">
				<!-- 使用插槽方式 -->
				<template v-if="!slides.length">
					<!-- 为每个可能的索引创建一个幻灯片 -->
					<div v-for="index in slidesCount" :key="index - 1" v-memo="[index - 1 === activeIndex]" class="x-swiper__slide-wrapper">
						<x-transition :mode="transition" :direction="direction" :duration="transitionDuration" :immediate="immediate">
							<div v-show="index - 1 === activeIndex" class="x-swiper__slide" role="group">
								<slot :index="index - 1"></slot>
							</div>
						</x-transition>
					</div>
				</template>

				<!-- 使用 slides 数组方式 -->
				<template v-else>
					<div v-for="(slide, index) in slides" :key="index" v-memo="[index === activeIndex]" class="x-swiper__slide-wrapper">
						<x-transition :mode="transition" :direction="direction" :duration="transitionDuration" :immediate="immediate">
							<div v-show="index === activeIndex" class="x-swiper__slide" role="group">
								<div class="x-swiper__slide-content">
									<!-- 图片类型 -->
									<img v-if="slide?.type === 'image'" :src="getSlideValue(slide)" :alt="getSlideAlt(slide)" class="x-swiper__slide-image" />

									<!-- HTML 类型 -->
									<div v-else-if="slide?.type === 'html'" class="x-swiper__slide-html" v-html="getSlideValue(slide)"></div>

									<!-- 默认文本类型 -->
									<div v-else class="x-swiper__slide-text">{{ getSlideValue(slide) }}</div>

									<!-- 标题 -->
									<div v-if="getSlideTitle(slide)" class="x-swiper__slide-title">
										{{ getSlideTitle(slide) }}
									</div>
								</div>
							</div>
						</x-transition>
					</div>
				</template>
			</div>

			<!-- Navigation arrows -->
			<div
				v-if="navigation && actualSlidesCount > 1"
				class="x-swiper__arrow x-swiper__arrow--prev"
				role="button"
				tabindex="0"
				@click="prev"
				@keydown.enter="prev"
			>
				<slot name="prev-arrow">
					<div class="x-swiper__arrow-icon">
						<svg viewBox="0 0 24 24" width="24" height="24">
							<path fill="currentColor" d="M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z" />
						</svg>
					</div>
				</slot>
			</div>

			<div
				v-if="navigation && actualSlidesCount > 1"
				class="x-swiper__arrow x-swiper__arrow--next"
				role="button"
				tabindex="0"
				@click="next"
				@keydown.enter="next"
			>
				<slot name="next-arrow">
					<div class="x-swiper__arrow-icon">
						<svg viewBox="0 0 24 24" width="24" height="24">
							<path fill="currentColor" d="M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z" />
						</svg>
					</div>
				</slot>
			</div>

			<!-- Pagination -->
			<div v-if="pagination && actualSlidesCount > 1" class="x-swiper__pagination" role="tablist">
				<div
					v-for="(_, index) in actualSlidesCount"
					:key="index"
					class="x-swiper__pagination-bullet"
					:class="{ 'x-swiper__pagination-bullet--active': index === activeIndex }"
					role="tab"
					tabindex="0"
					@click="goTo(index)"
					@keydown.enter="goTo(index)"
				>
					<slot name="pagination-bullet" :index="index" :active="index === activeIndex">
						<span></span>
					</slot>
				</div>
			</div>

			<!-- Progress bar -->
			<div v-if="progressBar && actualSlidesCount > 1" class="x-swiper__progress">
				<div class="x-swiper__progress-bar" :style="{ width: progressBarWidth }"></div>
			</div>

			<!-- Play/Pause button -->
			<div
				v-if="showPlayButton && actualSlidesCount > 1"
				class="x-swiper__play-button"
				role="button"
				tabindex="0"
				@click="toggleAutoplay"
				@keydown.enter="toggleAutoplay"
			>
				<slot name="play-button" :playing="isPlaying">
					<div class="x-swiper__play-icon">
						<svg v-if="isPlaying" viewBox="0 0 24 24" width="24" height="24">
							<path fill="currentColor" d="M6 19h4V5H6v14zm8-14v14h4V5h-4z" />
						</svg>
						<svg v-else viewBox="0 0 24 24" width="24" height="24">
							<path fill="currentColor" d="M8 5v14l11-7z" />
						</svg>
					</div>
				</slot>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
import XTransition from '../Transition/Transition.vue'

// 定义幻灯片类型
type SlideValue = string | object
type SlideObject = {
	type?: 'image' | 'html' | 'component' | 'text'
	value: SlideValue
	title?: string
	alt?: string
	props?: Record<string, any>
}
type Slide = SlideValue | SlideObject

const props = defineProps({
	// 轮播图高度
	height: {
		type: String,
		default: '300px',
	},
	// 是否自动播放
	autoplay: {
		type: Boolean,
		default: true,
	},
	// 自动播放间隔时间（毫秒）
	interval: {
		type: Number,
		default: 3000,
	},
	// 是否循环播放
	loop: {
		type: Boolean,
		default: true,
	},
	// 初始激活的索引
	initialIndex: {
		type: Number,
		default: 0,
	},
	// 过渡效果
	transition: {
		type: String,
		default: 'rebound',
		validator: (value: string) => {
			return ['fade', 'slide', 'scale', 'flip', 'elastic', 'rebound', 'bounce'].includes(value)
		},
	},
	// 过渡方向
	direction: {
		type: String,
		default: 'left',
		validator: (value: string) => {
			return ['left', 'right', 'up', 'down', 'center'].includes(value)
		},
	},
	// 过渡持续时间（秒）
	transitionDuration: {
		type: Number,
		default: 0.5,
	},
	// 是否显示导航箭头
	navigation: {
		type: Boolean,
		default: false,
	},
	// 是否显示分页器
	pagination: {
		type: Boolean,
		default: true,
	},
	// 幻灯片总数（使用插槽时需要）
	slidesCount: {
		type: Number,
		default: 0,
	},
	// 是否全屏显示
	fullscreen: {
		type: Boolean,
		default: false,
	},
	// 触摸滑动灵敏度
	touchRatio: {
		type: Number,
		default: 1,
	},
	// 幻灯片数组
	slides: {
		type: Array as () => Slide[],
		default: () => [],
	},
	// 是否显示进度条
	progressBar: {
		type: Boolean,
		default: false,
	},
	// 是否显示播放/暂停按钮
	showPlayButton: {
		type: Boolean,
		default: false,
	},
	// 是否启用拖拽
	enableDrag: {
		type: Boolean,
		default: true,
	},
	// 是否立即过渡（不显示动画）
	immediate: {
		type: Boolean,
		default: false,
	},
})

const emit = defineEmits(['change', 'prev', 'next', 'autoplay-start', 'autoplay-stop'])

// 当前激活的索引
const activeIndex = ref(props.initialIndex)
// 用于过渡动画的当前索引
const currentIndex = ref(props.initialIndex)
// 自动播放定时器
let autoplayTimer: number | null = null
// 容器引用 - 使用shallowRef优化性能
const containerRef = shallowRef<HTMLElement | null>(null)
// 轮播图引用 - 使用shallowRef优化性能
const swiperRef = shallowRef<HTMLElement | null>(null)
// 触摸状态
const touchState = ref({
	startX: 0,
	startY: 0,
	endX: 0,
	endY: 0,
	isTouching: false,
})
// 拖拽状态
const dragging = ref(false)
const dragStartX = ref(0)
const dragStartY = ref(0)
// 是否正在播放
const isPlaying = ref(props.autoplay)

// 实际幻灯片数量
const actualSlidesCount = computed(() => {
	return props.slides.length > 0 ? props.slides.length : props.slidesCount
})

// 包装器样式
const wrapperStyle = computed(() => {
	return {
		position: 'relative',
		width: '100%',
		height: '100%',
		overflow: 'hidden',
		cursor: props.enableDrag ? (dragging.value ? 'grabbing' : 'grab') : 'default',
	}
})

// 获取幻灯片值 - 优化类型检查
const getSlideValue = (slide: Slide): SlideValue => {
	if (slide !== null && typeof slide === 'object' && 'value' in slide) {
		return (slide as SlideObject).value
	}
	return slide
}

// 获取幻灯片标题
const getSlideTitle = (slide: Slide): string | undefined => {
	if (typeof slide === 'object' && slide !== null && 'title' in slide) {
		return (slide as SlideObject).title
	}
	return undefined
}

// 获取幻灯片 alt 文本
const getSlideAlt = (slide: Slide): string => {
	if (typeof slide === 'object' && slide !== null && 'alt' in slide) {
		return (slide as SlideObject).alt || ''
	}
	return ''
}

// 切换到下一张幻灯片
const next = () => {
	if (activeIndex.value >= actualSlidesCount.value - 1) {
		if (props.loop) {
			goTo(0)
		}
	} else {
		goTo(activeIndex.value + 1)
	}
	emit('next')
}

// 切换到上一张幻灯片
const prev = () => {
	if (activeIndex.value <= 0) {
		if (props.loop) {
			goTo(actualSlidesCount.value - 1)
		}
	} else {
		goTo(activeIndex.value - 1)
	}
	emit('prev')
}

// 切换到指定索引的幻灯片
const goTo = (index: number) => {
	if (index === activeIndex.value || index < 0 || index >= actualSlidesCount.value) return

	// 更新当前索引，触发过渡动画
	currentIndex.value = activeIndex.value

	// 在下一个 tick 更新活动索引，确保过渡动画正确触发
	nextTick(() => {
		activeIndex.value = index
		emit('change', index)
	})
}

// 开始自动播放
const startAutoplay = () => {
	if (autoplayTimer) return

	let lastTime = performance.now()
	const animate = (currentTime: number) => {
		if (!isPlaying.value) return

		const deltaTime = currentTime - lastTime
		if (deltaTime >= props.interval) {
			next()
			lastTime = currentTime
		}

		autoplayTimer = window.requestAnimationFrame(animate)
	}

	autoplayTimer = window.requestAnimationFrame(animate)
	isPlaying.value = true
	emit('autoplay-start')
}

// 停止自动播放
const stopAutoplay = () => {
	if (autoplayTimer) {
		window.cancelAnimationFrame(autoplayTimer)
		autoplayTimer = null
	}
	isPlaying.value = false
	emit('autoplay-stop')
}

// 切换自动播放状态
const toggleAutoplay = () => {
	if (isPlaying.value) {
		stopAutoplay()
	} else {
		startAutoplay()
	}
}

// 处理触摸开始事件
const handleTouchStart = (e: TouchEvent) => {
	if (!props.enableDrag) return

	touchState.value.isTouching = true
	touchState.value.startX = e.touches[0].clientX
	touchState.value.startY = e.touches[0].clientY

	// 如果正在自动播放，则暂停
	if (isPlaying.value) {
		stopAutoplay()
	}
}

// 处理触摸移动事件
const handleTouchMove = (e: TouchEvent) => {
	if (!touchState.value.isTouching || !props.enableDrag) return

	// 防止页面滚动
	e.preventDefault()

	touchState.value.endX = e.touches[0].clientX
	touchState.value.endY = e.touches[0].clientY
}

// 处理触摸结束事件
const handleTouchEnd = (e?: TouchEvent) => {
	if (!touchState.value.isTouching || !props.enableDrag) return

	try {
		const diffX = touchState.value.endX - touchState.value.startX
		const diffY = touchState.value.endY - touchState.value.startY

		// 水平滑动距离大于垂直滑动距离，且滑动距离足够大
		if (Math.abs(diffX) > Math.abs(diffY) && Math.abs(diffX) > 50 * props.touchRatio) {
			if (diffX > 0) {
				prev() // 向右滑动，切换到上一张
			} else {
				next() // 向左滑动，切换到下一张
			}
		}
	} catch (error) {
		console.error('Swiper touch end error:', error)
	} finally {
		// 重置触摸状态
		touchState.value.isTouching = false
		touchState.value.startX = 0
		touchState.value.startY = 0
		touchState.value.endX = 0
		touchState.value.endY = 0

		// 如果自动播放被启用，则恢复自动播放
		if (props.autoplay && !isPlaying.value) {
			startAutoplay()
		}
	}
}

// 添加节流函数
const throttle = <T extends (...args: any[]) => any>(fn: T, delay: number): ((...args: Parameters<T>) => void) => {
	let lastCall = 0
	return (...args: Parameters<T>) => {
		const now = Date.now()
		if (now - lastCall >= delay) {
			lastCall = now
			fn(...args)
		}
	}
}

// 开始拖拽
const startDrag = (e: MouseEvent) => {
	// 确保我们只在鼠标左键点击时启动拖拽
	if (e.button !== 0 || !props.enableDrag) return

	dragging.value = true
	dragStartX.value = e.clientX
	dragStartY.value = e.clientY

	// 如果正在自动播放，则暂停
	if (isPlaying.value) {
		stopAutoplay()
	}

	// 添加全局事件监听，确保即使鼠标移出组件也能捕获事件
	document.addEventListener('mousemove', throttledMouseMove)
	document.addEventListener('mouseup', handleGlobalMouseUp)

	// 防止文本选择
	e.preventDefault()
}

// 全局鼠标移动处理 - 使用节流优化性能
const handleGlobalMouseMove = (e: MouseEvent) => {
	if (!dragging.value) return

	const diffX = e.clientX - dragStartX.value

	// 防止选中文本
	e.preventDefault()

	// 添加拖拽视觉反馈
	if (containerRef.value) {
		containerRef.value.style.cursor = 'grabbing'
	}
}

// 节流后的鼠标移动处理函数
const throttledMouseMove = throttle(handleGlobalMouseMove, 16) // 约60fps

// 全局鼠标释放处理
const handleGlobalMouseUp = (e: MouseEvent) => {
	if (!dragging.value) return

	const diffX = e.clientX - dragStartX.value
	const diffY = e.clientY - dragStartY.value

	// 如果拖拽距离足够大且水平拖拽大于垂直拖拽，则切换幻灯片
	if (Math.abs(diffX) > 100 && Math.abs(diffX) > Math.abs(diffY)) {
		if (diffX > 0) {
			// 向右拖拽，切换到上一张
			prev()
		} else {
			// 向左拖拽，切换到下一张
			next()
		}
	}

	// 重置拖拽状态和样式
	dragging.value = false
	if (containerRef.value) {
		containerRef.value.style.cursor = props.enableDrag ? 'grab' : 'default'
	}

	// 移除全局事件监听
	document.removeEventListener('mousemove', throttledMouseMove)
	document.removeEventListener('mouseup', handleGlobalMouseUp)

	// 如果自动播放被启用，则恢复自动播放
	if (props.autoplay && !isPlaying.value) {
		startAutoplay()
	}
}

// 添加触摸事件监听器
const addTouchEvents = () => {
	if (!containerRef.value) return

	try {
		containerRef.value.addEventListener('touchstart', handleTouchStart, { passive: false })
		containerRef.value.addEventListener('touchmove', handleTouchMove, { passive: false })
		containerRef.value.addEventListener('touchend', handleTouchEnd)
	} catch (error) {
		console.error('Failed to add touch events:', error)
	}
}

// 移除触摸事件监听器
const removeTouchEvents = () => {
	if (!containerRef.value) return

	try {
		containerRef.value.removeEventListener('touchstart', handleTouchStart)
		containerRef.value.removeEventListener('touchmove', handleTouchMove)
		containerRef.value.removeEventListener('touchend', handleTouchEnd)
	} catch (error) {
		console.error('Failed to remove touch events:', error)
	}
}

// 添加键盘事件监听器
const addKeyboardEvents = () => {
	if (!swiperRef.value) return

	// 确保轮播图可以获取焦点
	swiperRef.value.setAttribute('tabindex', '0')
}

// 监听 slidesCount 和 slides 变化
watch([() => props.slidesCount, () => props.slides.length], ([newSlidesCount, newSlidesLength]) => {
	const newCount = newSlidesLength > 0 ? newSlidesLength : newSlidesCount
	// 如果当前索引超出新的幻灯片数量，则重置为最后一张
	if (activeIndex.value >= newCount) {
		activeIndex.value = Math.max(0, newCount - 1)
	}
})

// 组件挂载时
onMounted(() => {
	// 如果启用自动播放，则开始自动播放
	if (props.autoplay) {
		startAutoplay()
	}

	// 添加触摸事件监听器
	addTouchEvents()

	// 添加键盘事件监听器
	addKeyboardEvents()
})

// 组件卸载前
onBeforeUnmount(() => {
	// 停止自动播放
	stopAutoplay()

	// 移除触摸事件监听器
	removeTouchEvents()

	// 移除全局鼠标事件监听器
	document.removeEventListener('mousemove', handleGlobalMouseMove)
	document.removeEventListener('mouseup', handleGlobalMouseUp)

	// 清空引用以避免内存泄漏
	containerRef.value = null
	swiperRef.value = null
})

// 暴露方法
defineExpose({
	next,
	prev,
	goTo,
	startAutoplay,
	stopAutoplay,
	toggleAutoplay,
	activeIndex,
})

// 进度条宽度计算 - 防止除零错误
const progressBarWidth = computed(() => {
	const total = actualSlidesCount.value - 1
	if (total <= 0) return '0%'
	return `${(activeIndex.value / total) * 100}%`
})
</script>

<style scoped>
.x-swiper {
	position: relative;
	width: 100%;
	overflow: hidden;
	box-sizing: border-box;
	outline: none;
	-webkit-tap-highlight-color: transparent;
	touch-action: pan-y;
	will-change: transform;
}

.x-swiper--fullscreen {
	position: fixed;
	top: 0;
	left: 0;
	width: 100vw;
	height: 100vh;
	z-index: 9999;
}

.x-swiper__container {
	position: relative;
	width: 100%;
	height: 100%;
}

.x-swiper__wrapper {
	position: relative;
	width: 100%;
	height: 100%;
	overflow: hidden;
	will-change: transform;
}

.x-swiper__slide-wrapper {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	will-change: opacity, transform;
}

.x-swiper__slide {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	display: flex;
	justify-content: center;
	align-items: center;
	backface-visibility: hidden;
	-webkit-backface-visibility: hidden;
}

.x-swiper__slide-content {
	position: relative;
	width: 100%;
	height: 100%;
	display: flex;
	justify-content: center;
	align-items: center;
	overflow: hidden;
}

.x-swiper__slide-image {
	width: 100%;
	height: 100%;
	object-fit: cover;
	-webkit-user-drag: none;
	user-select: none;
	-webkit-user-select: none;
}

.x-swiper__slide-html {
	width: 100%;
	height: 100%;
}

.x-swiper__slide-component {
	width: 100%;
	height: 100%;
}

.x-swiper__slide-text {
	display: flex;
	justify-content: center;
	align-items: center;
	width: 100%;
	height: 100%;
	padding: 20px;
	text-align: center;
	font-size: 18px;
	background-color: #f5f5f5;
}

.x-swiper__slide-title {
	position: absolute;
	bottom: 0;
	left: 0;
	width: 100%;
	padding: 10px;
	background-color: rgba(0, 0, 0, 0.5);
	color: white;
	text-align: center;
}

.x-swiper__arrow {
	position: absolute;
	top: 50%;
	transform: translateY(-50%);
	width: 40px;
	height: 40px;
	display: flex;
	justify-content: center;
	align-items: center;
	background-color: rgba(0, 0, 0, 0.3);
	color: #fff;
	border-radius: 50%;
	cursor: pointer;
	z-index: 10;
	transition: all 0.3s;
	outline: none;
	-webkit-tap-highlight-color: transparent;
}

.x-swiper__arrow:hover,
.x-swiper__arrow:focus {
	background-color: rgba(0, 0, 0, 0.5);
}

.x-swiper__arrow--prev {
	left: 10px;
}

.x-swiper__arrow--next {
	right: 10px;
}

.x-swiper__arrow-icon {
	display: flex;
	justify-content: center;
	align-items: center;
}

.x-swiper__pagination {
	position: absolute;
	bottom: 10px;
	left: 50%;
	transform: translateX(-50%);
	display: flex;
	justify-content: center;
	align-items: center;
	z-index: 10;
}

.x-swiper__pagination-bullet {
	width: 8px;
	height: 8px;
	margin: 0 4px;
	border-radius: 50%;
	background-color: rgba(255, 255, 255, 0.5);
	cursor: pointer;
	transition: all 0.3s;
	outline: none;
	-webkit-tap-highlight-color: transparent;
}

.x-swiper__pagination-bullet:hover,
.x-swiper__pagination-bullet:focus {
	background-color: rgba(255, 255, 255, 0.8);
}

.x-swiper__pagination-bullet--active {
	background-color: #fff;
	width: 12px;
	height: 12px;
}

.x-swiper__progress {
	position: absolute;
	bottom: 0;
	left: 0;
	width: 100%;
	height: 4px;
	background-color: rgba(255, 255, 255, 0.3);
	z-index: 10;
	overflow: hidden;
}

.x-swiper__progress-bar {
	height: 100%;
	background-color: #fff;
	transition: width 0.3s;
	will-change: width;
}

.x-swiper__play-button {
	position: absolute;
	bottom: 10px;
	right: 10px;
	width: 36px;
	height: 36px;
	display: flex;
	justify-content: center;
	align-items: center;
	background-color: rgba(0, 0, 0, 0.3);
	color: #fff;
	border-radius: 50%;
	cursor: pointer;
	z-index: 10;
	transition: all 0.3s;
	outline: none;
	-webkit-tap-highlight-color: transparent;
}

.x-swiper__play-button:hover,
.x-swiper__play-button:focus {
	background-color: rgba(0, 0, 0, 0.5);
}

.x-swiper__play-icon {
	display: flex;
	justify-content: center;
	align-items: center;
}

/* 响应式样式 */
@media (max-width: 768px) {
	.x-swiper__arrow {
		width: 32px;
		height: 32px;
	}

	.x-swiper__pagination-bullet {
		width: 6px;
		height: 6px;
	}

	.x-swiper__pagination-bullet--active {
		width: 10px;
		height: 10px;
	}
}

@media (max-width: 480px) {
	.x-swiper__arrow {
		width: 28px;
		height: 28px;
	}
}

/* 添加打印媒体查询 */
@media print {
	.x-swiper__arrow,
	.x-swiper__pagination,
	.x-swiper__progress,
	.x-swiper__play-button {
		display: none !important;
	}
}
</style>
