<script setup lang="ts">
import { InputTypeHTMLAttribute } from 'vue'

/*defineOptions({
	inheritAttrs: false,
})*/

type InputType = InputTypeHTMLAttribute | 'textarea'

interface InputProps {
	displayValue?: string //只用来展示的值，不做修改，跟placeholder也还不同
	type?: InputType
	customClass?: any
	clearable?: boolean
	showPassword?: boolean
	disabled?: boolean
	readonly?: boolean
	autofocus?: boolean
	maxlength?: number
	min?: string | number
	max?: string | number
	showWordLimit?: boolean
	placeholder?: string
	rows?: number
	step?: number
	autosize?: boolean | { minRows?: number; maxRows?: number }
	skipBlurValidation?: boolean // 是否跳过blur时的表单校验
}

const props = withDefaults(defineProps<InputProps>(), {
	type: 'text',
	customClass: '',
	clearable: false,
	clearableNoValue: false,
	showPassword: false,
	placeholder: '请输入',
	disabled: false,
	readonly: false,
	autofocus: false,
	step: 0.1,
	showWordLimit: false,
	rows: 2,
	autosize: false,
	skipBlurValidation: false,
})

const emit = defineEmits<{
	(e: 'clear'): void
	(e: 'input', value: string | number): void
	(e: 'blur'): void
	(e: 'focus'): void
}>()

const value = defineModel<string | number>()
const inputRef = ref<HTMLInputElement | HTMLTextAreaElement | null>(null)
const showClear = ref(false)
const passwordVisible = ref(false)
const textareaStyle = ref<Record<string, string>>({})

// 表单上下文处理
const formContext = inject<any>('formContext', null)
const { prop: formItemProp, error } = inject<Record<string, any>>('formItemContext', {})

// 计算属性
const inputType = computed(() => {
	if (props.type !== 'password') return props.type
	return passwordVisible.value ? 'text' : 'password'
})

const currentValueLength = computed(() => {
	return String(value.value).length
})

const isTextarea = computed(() => props.type === 'textarea')

// 方法
function handleInput(e: Event) {
	const target = e.target as HTMLInputElement | HTMLTextAreaElement
	if (props.type === 'number') {
		value.value = (target as HTMLInputElement).valueAsNumber
	} else {
		value.value = target.value
	}
	showClear.value = !!target.value
	emit('input', value.value)
	// readonly的输入框不参与表单校验
	if (!props.readonly) {
		formContext?.validateField?.(formItemProp, 'change')
	}

	if (isTextarea.value && props.autosize) {
		updateTextareaHeight()
	}
}

function handleBlur() {
	emit('blur')
	// readonly的输入框不参与表单校验，skipBlurValidation为true时也不参与blur校验
	if (!props.readonly && !props.skipBlurValidation) {
		formContext?.validateField?.(formItemProp, 'blur')
	}
}

function handleFocus() {
	if (props.disabled || props.readonly) {
		return
	}
	emit('focus')
}

function clearInput() {
	value.value = ''
	emit('clear')
	inputRef.value?.focus()
}

function togglePassword() {
	passwordVisible.value = !passwordVisible.value
}

function focus() {
	inputRef.value?.focus()
}

function blur() {
	inputRef.value?.blur()
}

function updateTextareaHeight() {
	if (!isTextarea.value || !props.autosize || !inputRef.value) return

	const textarea = inputRef.value as HTMLTextAreaElement
	const minRows = typeof props.autosize === 'object' ? props.autosize.minRows || 2 : 2
	const maxRows = typeof props.autosize === 'object' ? props.autosize.maxRows || null : null

	// Reset height to calculate the scrollHeight
	textarea.style.height = 'auto'

	// Calculate the new height
	const lineHeight = parseInt(getComputedStyle(textarea).lineHeight) || 20
	const paddingTop = parseInt(getComputedStyle(textarea).paddingTop) || 0
	const paddingBottom = parseInt(getComputedStyle(textarea).paddingBottom) || 0

	const minHeight = minRows * lineHeight + paddingTop + paddingBottom
	let height = textarea.scrollHeight

	if (maxRows) {
		const maxHeight = maxRows * lineHeight + paddingTop + paddingBottom
		height = Math.min(height, maxHeight)
	}

	height = Math.max(height, minHeight)

	textareaStyle.value = {
		height: `${height}px`,
		resize: 'none',
	}
}

// 生命周期
onMounted(() => {
	if (props.autofocus) {
		inputRef.value?.focus()
	}

	if (isTextarea.value && props.autosize) {
		updateTextareaHeight()
	}
})

// 监听值变化，更新textarea高度
watch(
	() => value.value,
	() => {
		if (isTextarea.value && props.autosize) {
			nextTick(() => {
				updateTextareaHeight()
			})
		}
	},
)

// 暴露方法
defineExpose({
	focus,
	blur,
	inputRef,
})
</script>

<template>
	<div
		class="relative wfull flex shrink-0 items-center rounded bg-inputBg px-xxs-1.5 border-inputBorder"
		:class="[$attrs.class, disabled ? 'bg-gray-100 cursor-not-allowed' : '', error ? 'border-danger' : '', isTextarea ? 'py-xxs' : 'h-2.3rem']"
	>
		<div class="flex flex-1 items-center gap-xxs" :class="[]">
			<!-- 前缀插槽 -->
			<slot v-if="!isTextarea" name="prefix">
				<span v-if="$slots.prefix" class="pl-2 text-gray-400">
					<component :is="$slots.prefix" />
				</span>
			</slot>
			<!-- 文本输入框 -->
			<input
				v-if="!isTextarea"
				ref="inputRef"
				v-model="value"
				:type="inputType"
				:disabled="disabled"
				:readonly="readonly"
				:maxlength="maxlength"
				:min="min"
				:max="max"
				:placeholder="displayValue || placeholder"
				:step="type === 'number' ? step : undefined"
				class="wfull bg-transparent text-inputText outline-none text-sm placeholder-text-sm placeholder-inputPlaceholder"
				:class="customClass"
				v-bind="{ ...$attrs, class: undefined }"
				autocomplete="new-password"
				@input="handleInput"
				@blur="handleBlur"
				@focus="handleFocus"
			/>

			<!-- 文本域 -->
			<textarea
				v-else
				ref="inputRef"
				v-model="value"
				:class="customClass"
				:disabled="disabled"
				:readonly="readonly"
				:maxlength="maxlength"
				:placeholder="placeholder"
				:rows="rows"
				class="wfull bg-transparent text-inputText outline-none text-sm placeholder-text-sm placeholder-inputPlaceholder"
				v-bind="$attrs"
				:style="textareaStyle"
				@input="handleInput"
				@blur="handleBlur"
				@focus="handleFocus"
			></textarea>
			<!-- 清除按钮槽位：仅 clearable 才预留，隐藏时仍占位避免宽度抖动 -->
			<div v-if="clearable" class="h-5 w-5 flex items-center justify-center">
				<XIconsCloseCircleFill
					:class="[{ 'invisible pointer-events-none': !(value || displayValue) || disabled }, 'cursor-pointer text-secondary']"
					@click.stop="clearInput"
				/>
			</div>

			<!-- 密码可见切换槽位：仅 showPassword 且非 textarea 才预留，隐藏时仍占位避免宽度抖动 -->
			<div v-if="showPassword && !isTextarea" class="h-5 w-5 flex items-center justify-center">
				<div class="cursor-pointer text-secondary" :class="{ 'invisible pointer-events-none': disabled }" @click="togglePassword">
					<XIconsEyeFill v-if="passwordVisible" />
					<XIconsEyeFillSlash v-else />
				</div>
			</div>

			<!-- 后缀插槽 -->
			<slot v-if="!isTextarea" name="suffix">
				<span v-if="$slots.suffix" class="pr-2 text-gray-400">
					<component :is="$slots.suffix" />
				</span>
			</slot>
		</div>

		<!-- 字数统计 -->
		<div v-if="showWordLimit && maxlength" class="mt-1 text-right text-gray-500 text-sm" :class="{ 'text-danger': currentValueLength > maxlength }">
			{{ currentValueLength }} / {{ maxlength }}
		</div>
	</div>
</template>
<style scoped>
input[type='password']::-ms-reveal {
	display: none;
}

input[type='password']::-ms-clear {
	display: none;
}
input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active,
textarea:-webkit-autofill,
textarea:-webkit-autofill:hover,
textarea:-webkit-autofill:focus,
textarea:-webkit-autofill:active {
	transition-delay: 99999s;
	transition:
		color 99999s ease-out,
		background-color 99999s ease-out;
}
</style>
