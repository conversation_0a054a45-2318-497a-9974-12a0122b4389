<script setup lang="ts">
const props = defineProps({
	color: {
		type: String,
		default: 'var(--color-aid)',
	},
	customStyle: {
		type: [Object, String],
		default: '',
	},
})
const containerRef = ref()
const computedStyle = computed(() => {
	const container = containerRef.value
	if (!container) {
		return
	}
	let width = 50
	let height = 50
	if (container?.clientWidth && container?.clientHeight) {
		width = container.clientWidth
		height = container.clientHeight
	}
	const minDimension = Math.min(width, height, 100) * 0.5
	const conicWidth = `${minDimension * 0.2}px`
	const str1 = `aspect-ratio: 1;border-radius: 50%;
          background:
            radial-gradient(farthest-side, ${props.color} 94%, #0000) top/${conicWidth} ${conicWidth} no-repeat,
            conic-gradient(#0000 25%, ${props.color});
          mask: radial-gradient(farthest-side, #0000 calc(100% - ${conicWidth}), #000 0);`
	const str2 = width > height ? `height:${minDimension}px;` : `width:${minDimension}px;`
	return str1 + str2
})
</script>

<template>
	<!--	animate-[l13] animate-ease-linear animate-duration-1000 animate-iteration-count-infinite-->
	<div ref="containerRef" class="flex items-center justify-center" :style="customStyle">
		<div class="animate-spin" :style="computedStyle"></div>
	</div>
</template>
<style scoped></style>
