<template>
	<teleport to="body">
		<div class="pointer-events-none fixed left-50% top-0 z-10 inline-block translate-x--50% items-center gap-xs">
			<XToast
				v-for="(toast, index) in toasts"
				:key="toast.uuid"
				class="mb-xs"
				:list="toasts"
				:type="toast.type"
				:message="toast.message"
				:direction="toast.direction"
				@close="removeToast(index)"
			/>
		</div>
	</teleport>
</template>

<script setup>
const toasts = ref([])

const addToast = (message, type) => {
	const toast = {
		uuid: X_COMMON_UTILS.generateUUID(),
		message,
		type,
	}
	toasts.value.push(toast)
}

const removeToast = (index) => {
	toasts.value.splice(index, 1)
}

defineExpose({
	addToast,
})
</script>

<style scoped>
.toast-container {
}
</style>
