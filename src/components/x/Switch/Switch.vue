<script setup lang="ts">
const props = defineProps({
	disabled: <PERSON><PERSON><PERSON>,
	loading: <PERSON><PERSON><PERSON>,
	activeColor: {
		type: String,
		default: '#34C759',
	},
	inactiveColor: {
		type: String,
		default: '#E5E5EA',
	},
	beforeChange: {
		type: Function as PropType<() => Promise<boolean>>,
		default: null,
	},
})

const emit = defineEmits(['change'])

const innerValue = defineModel({
	type: Boolean,
	default: false,
})
const isChanging = ref(false)

const isInteractive = computed(() => !props.disabled && !props.loading)

const switchClasses = computed(() => [
	'w-11 h-6', // 固定尺寸
	'relative inline-flex items-center rounded-full transition-colors',
	isInteractive.value ? 'cursor-pointer' : 'cursor-not-allowed opacity-50',
])

const sliderClasses = computed(() => [
	'w-5 h-5', // 固定滑块尺寸
	'bg-white dark:bg-gray-100 rounded-full shadow-md',
	'absolute transform transition-transform duration-200',
	innerValue.value ? 'translate-x-5' : 'translate-x-0.5',
])

watch(
	() => props.modelValue,
	(val) => {
		innerValue.value = val
	},
)

async function toggle() {
	if (!isInteractive.value) return

	try {
		if (props.beforeChange) {
			isChanging.value = true
			const canChange = await props.beforeChange()
			if (!canChange) return
		}

		innerValue.value = !innerValue.value
		emit('change', innerValue.value)
	} finally {
		isChanging.value = false
	}
}
</script>

<template>
	<button
		type="button"
		role="switch"
		:aria-checked="innerValue"
		:disabled="!isInteractive"
		:class="switchClasses"
		:style="{
			backgroundColor: innerValue ? activeColor : inactiveColor,
		}"
		@click="toggle"
		@keydown.space.prevent="toggle"
	>
		<span v-loading="loading || isChanging" :class="sliderClasses" class="flex items-center justify-center">
			<!-- Loading 状态 -->
			<!--<template v-if="loading || isChanging">-->
			<!--	<i class="i-carbon:assembly-reference animate-spin text-gray-400 text-xs" />-->
			<!--</template>-->
		</span>
	</button>
</template>
