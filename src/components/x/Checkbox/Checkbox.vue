<template>
	<label class="flex cursor-pointer select-none items-center gap-xxs" :class="[{ 'opacity-50 cursor-not-allowed': isDisabled }, $attrs.class]">
		<input
			v-bind="{ ...$attrs, class: undefined }"
			ref="checkboxRef"
			v-model="model"
			type="checkbox"
			class="absolute h-0 w-0 opacity-0"
			:disabled="isDisabled"
			@change="handleChange"
		/>
		<span
			class="h1rem min-h-1rem min-w-1rem w1rem flex items-center justify-center rounded transition-all duration-200 border-aid"
			:class="[{ 'border-primary bg-primary': isChecked && !isIndeterminate }, { 'bg-primary border-primary': isIndeterminate }]"
		>
			<slot name="icon" :checked="isChecked" :indeterminate="isIndeterminate">
				<XIconsCheck v-if="isChecked && !isIndeterminate" class="text-white" />
				<XIconsMinus v-if="isIndeterminate" class="text-white" />
			</slot>
		</span>
		<slot :checked="isChecked" :disabled="isDisabled" :indeterminate="isIndeterminate">
			<XLabel :label="label" />
		</slot>
	</label>
</template>

<script setup>
const props = defineProps({
	modelValue: {
		type: [Boolean, Array],
		default: false,
		validator: (value) => {
			// 校验 modelValue 是否为布尔值或数组
			return (
				typeof value === 'boolean' ||
				(Array.isArray(value) && value.every((item) => typeof item === 'string' || typeof item === 'number' || typeof item === 'boolean'))
			)
		},
	},
	label: { type: String, default: '' },
	value: { type: [String, Number, Boolean], default: true },
	disabled: Boolean,
	indeterminate: Boolean,
	// 样式相关props
	iconClass: String,
})

// 修改emit定义，支持不同类型的事件参数
const emit = defineEmits(['update:modelValue', 'change'])

const checkboxRef = ref(null)
const checkboxGroup = inject('checkboxGroup', null)

const isDisabled = computed(() => props.disabled || checkboxGroup?.disabled.value)
const isIndeterminate = computed(() => props.indeterminate || checkboxGroup?.indeterminate?.value)

// 处理 modelValue 的边界情况
const normalizedModelValue = computed(() => {
	if (props.modelValue == null) {
		// 处理 null 或 undefined
		return false
	}
	if (Array.isArray(props.modelValue)) {
		return props.modelValue
	}
	return !!props.modelValue // 处理其他非法值，转换为布尔值
})

const isChecked = computed(() => {
	if (checkboxGroup) {
		return checkboxGroup.modelValue.value.includes(props.value)
	}
	return normalizedModelValue.value
})

// 更新值的统一方法
const updateModelValue = (val, forceValue = null) => {
	const newValue = forceValue !== null ? forceValue : val

	if (checkboxGroup) {
		checkboxGroup.updateValue(props.value, forceValue !== null)
	} else {
		emit('update:modelValue', newValue)
	}
}

const model = computed({
	get() {
		return checkboxGroup ? checkboxGroup.modelValue.value : normalizedModelValue.value
	},
	set(val) {
		updateModelValue(val)
	},
})

const handleChange = (e) => {
	// 当用户点击时，如果是indeterminate状态，应该切换为checked状态
	if (isIndeterminate.value) {
		updateModelValue(null, true)
	}

	// 支持不同类型的事件参数
	if (e && typeof e === 'object' && 'target' in e) {
		// 原生事件对象
		emit('change', e)
	} else {
		// 其他类型的参数，如从父组件传递的值
		emit('change', { target: { checked: isChecked.value } })
	}

	if (checkboxGroup) {
		checkboxGroup.handleChange(e)
	}
}

// 监听indeterminate属性变化，同步到原生checkbox
watch(isIndeterminate, (val) => {
	if (checkboxRef.value) {
		checkboxRef.value.indeterminate = val
	}
})

// 组件挂载时设置indeterminate属性
onMounted(() => {
	if (checkboxRef.value) {
		checkboxRef.value.indeterminate = isIndeterminate.value
	}
})
</script>
