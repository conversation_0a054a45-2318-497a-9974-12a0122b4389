<script setup>
const props = defineProps({
	modelValue: {
		type: Object,
		default: () => ({ hour: 0, minute: 0, second: 0 }),
	},
})

const emit = defineEmits(['update:modelValue'])

const time = useVModel(props, 'modelValue', emit)

// 生成时间选项
const hours = Array.from({ length: 24 }, (_, i) => i)
const minutes = Array.from({ length: 60 }, (_, i) => i)
</script>

<template>
	<div class="flex gap-2">
		<select v-model="time.hour">
			<option v-for="h in hours" :value="h">{{ h.toString().padStart(2, '0') }}</option>
		</select>
		<span>:</span>
		<select v-model="time.minute">
			<option v-for="m in minutes" :value="m">{{ m.toString().padStart(2, '0') }}</option>
		</select>
	</div>
</template>
