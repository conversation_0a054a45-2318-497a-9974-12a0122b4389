/**
 * 处理表格滚动相关的逻辑
 * @param {Object} tableWrapper - 表格包装器的ref引用
 * @param {Object} tableContainer - 表格容器的ref引用
 * @returns {Object} - 滚动相关的响应式变量和方法
 */
export function useTableScroll(tableWrapper, tableContainer) {
	// 滚动状态
	const scrollLeft = ref(0)
	const scrollTop = ref(0)
	const scrollbarWidth = ref(0)
	const showScrollbar = ref(false)

	// 处理滚动事件
	const onScroll = (e) => {
		scrollTop.value = e.target.scrollTop
		scrollLeft.value = e.target.scrollLeft

		checkScrollbarVisibility()
		syncHeaderScroll(e.target.scrollLeft)
	}

	// 同步表头滚动
	const syncHeaderScroll = (scrollLeft) => {
		if (!tableWrapper.value) return

		const headerWrapper = tableWrapper.value.querySelector('.table-header-wrapper')
		if (headerWrapper) {
			requestAnimationFrame(() => {
				headerWrapper.scrollLeft = scrollLeft
				// 确保固定列可见性
				const fixedCols = headerWrapper.querySelectorAll('.fixed-left, .fixed-right')
				fixedCols.forEach((col) => (col.style.visibility = 'visible'))
			})
		}
	}

	// 计算滚动条宽度
	const calculateScrollbarWidth = () => {
		const outer = document.createElement('div')
		const inner = document.createElement('div')

		outer.style.cssText = 'visibility:hidden;overflow:scroll;msOverflowStyle:scrollbar'
		document.body.appendChild(outer)
		outer.appendChild(inner)

		const scrollbarWidth = outer.offsetWidth - inner.offsetWidth
		document.body.removeChild(outer)

		return scrollbarWidth
	}

	// 检查滚动条可见性
	const checkScrollbarVisibility = () => {
		if (!tableContainer.value) return
		showScrollbar.value = tableContainer.value.scrollHeight > tableContainer.value.clientHeight
	}

	// 强制滚动更新
	const forceScrollUpdate = () => {
		if (tableContainer.value) {
			const currentScrollTop = tableContainer.value.scrollTop
			tableContainer.value.scrollTop = currentScrollTop + 1
			setTimeout(() => {
				tableContainer.value.scrollTop = currentScrollTop
			}, 0)
		}
	}

	// 组件挂载初始化
	onMounted(() => {
		scrollTop.value = 0
		scrollLeft.value = 0
		scrollbarWidth.value = calculateScrollbarWidth()
		nextTick(checkScrollbarVisibility)
	})

	return {
		scrollLeft,
		scrollTop,
		showScrollbar,
		scrollbarWidth,
		onScroll,
		checkScrollbarVisibility,
		forceScrollUpdate,
	}
}
