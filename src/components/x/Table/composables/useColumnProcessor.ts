/**
 * 处理表格列相关的逻辑
 * @param {Object} props - 组件props
 * @returns {Object} - 列处理相关的计算属性和方法
 */
export function useColumnProcessor(props: any) {
	// 扁平化列配置
	const flattenedColumns = computed(() => {
		const result: any[] = []

		const flatten = (columns: any[]) => {
			columns.forEach((col: any) => {
				if (col.children?.length) {
					flatten(col.children)
				} else {
					// 确保列有宽度
					if (!col.width) {
						col.width = 100
					}
					// 确保 action 列默认右固定
					if (col[props.colValueKey] === 'action' && !col.fixed) {
						col.fixed = 'right'
					}
					result.push(col)
				}
			})
		}

		flatten(props.columns)
		return result
	})

	// 扁平化列的索引映射
	const flattenedColumnsMap = computed(() => {
		const map = new Map()
		flattenedColumns.value.forEach((col: any, index: number) => {
			if (col[props.colValueKey]) {
				map.set(col[props.colValueKey], index)
			}
		})
		return map
	})

	// 处理多级表头
	const headerRows = computed(() => processColumns(props.columns))

	// 计算列的叶子节点数量（用于计算colspan）
	const getLeafCount = (col: any): number => {
		if (!col.children || col.children.length === 0) {
			return 1
		}
		return col.children.reduce((sum: number, child: any) => sum + getLeafCount(child), 0)
	}

	// 处理多级表头 - 核心逻辑
	const processColumns = (columns: any[]) => {
		const result: any[] = []
		const maxLevel = getMaxLevel(columns)

		// 初始化每一行的数组
		for (let i = 0; i < maxLevel; i++) {
			result[i] = []
		}

		// 递归处理列配置
		const processColumn = (col: any, level = 0) => {
			if (!col) return

			const currentCol = {
				...col,
				colspan: 1,
				rowspan: 1,
			}

			// 如果有子列
			if (col.children?.length) {
				// 确保每个子列都有宽度
				col.children.forEach((child: any) => {
					if (!child.width) {
						child.width = 100 // 默认宽度
					}
				})

				// 计算所有叶子节点的数量作为colspan
				currentCol.colspan = getLeafCount(col)

				// 计算子列的总宽度（递归计算）
				const calculateChildrenWidth = (children: any[]): number => {
					return children.reduce((sum: number, child: any) => {
						if (child.children?.length) {
							return sum + calculateChildrenWidth(child.children)
						}
						return sum + (child.width || 100)
					}, 0)
				}
				currentCol.width = calculateChildrenWidth(col.children)

				// 递归处理子列
				col.children.forEach((child: any) => {
					processColumn(child, level + 1)
				})
			} else if (!currentCol.width) {
				// 如果是叶子节点且没有设置宽度，设置默认宽度
				currentCol.width = 100
			}

			// 将列添加到对应层级
			result[level].push(currentCol)
		}

		// 处理每一列
		columns.forEach((col: any) => {
			processColumn(col)
		})

		// 计算rowspan
		for (let i = 0; i < result.length; i++) {
			for (let j = 0; j < result[i].length; j++) {
				const col = result[i][j]
				if (col.children?.length) {
					col.rowspan = 1
				} else {
					col.rowspan = maxLevel - i
				}
			}
		}

		return result
	}

	// 获取最大层级
	const getMaxLevel = (columns: any[]): number => {
		let maxLevel = 1

		const traverse = (cols: any[], level = 1) => {
			cols.forEach((col: any) => {
				if (col.children?.length) {
					maxLevel = Math.max(maxLevel, level + 1)
					traverse(col.children, level + 1)
				}
			})
		}

		traverse(columns)
		return maxLevel
	}

	return {
		flattenedColumns,
		flattenedColumnsMap,
		headerRows,
	}
}
