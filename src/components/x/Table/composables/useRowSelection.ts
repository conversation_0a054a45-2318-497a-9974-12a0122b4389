/**
 * 处理行选择相关的逻辑
 * @param {Object} props - 组件props
 * @param {Object} visibleRows - 可见行数据
 * @param {Object} nodeMap - 节点映射
 * @param {Function} emit - 事件发射器
 * @returns {Object} - 行选择相关的响应式变量和方法
 */
export function useRowSelection(props, visibleRows, nodeMap, emit) {
	// 选择状态
	const selectedRowMap = ref(new Map())
	const indeterminateRowMap = ref(new Map())

	// 选择状态检查
	const isRowSelected = (row) => selectedRowMap.value.has(row[props.valueKey])
	const isRowIndeterminate = (row) => indeterminateRowMap.value.has(row[props.valueKey])

	// 节流函数工具
	const throttle = (fn, delay) => {
		let lastCall = 0
		let lastArgs = null
		let timeout = null

		return function (...args) {
			const now = Date.now()
			lastArgs = args

			if (now - lastCall >= delay) {
				lastCall = now
				fn.apply(this, args)
				return
			}

			if (timeout) clearTimeout(timeout)
			timeout = setTimeout(() => {
				lastCall = Date.now()
				fn.apply(this, lastArgs)
				timeout = null
			}, delay)
		}
	}

	// 发送选择变化事件（节流）
	const emitSelectionChange = throttle((selectedRows) => {
		emit('update:selected', selectedRows)
	}, 50)

	// 获取所有子节点（递归）
	const getAllChildren = (row) => {
		if (!row?.children?.length) return []

		const children = []
		const stack = [...row.children]

		while (stack.length > 0) {
			const node = stack.pop()
			if (!node) continue

			children.push(node)
			if (node.children?.length) {
				stack.push(...node.children)
			}
		}

		return children
	}

	// 收集需要更新的父节点ID
	const collectParentIds = (rows) => {
		const parentIds = new Set()

		rows.forEach((row) => {
			if (!row?.parentId) return

			let currentNode = row
			while (currentNode.parentId) {
				parentIds.add(currentNode.parentId)
				currentNode = nodeMap.value.get(currentNode.parentId)
				if (!currentNode) break
			}
		})

		return parentIds
	}

	// 更新单个父节点状态
	const updateParentSelectionState = (parentId) => {
		const parent = nodeMap.value.get(parentId)
		if (!parent?.children?.length) return

		const children = parent.children
		const allSelected = children.every((child) => selectedRowMap.value.has(child[props.valueKey]))
		const someSelected = children.some(
			(child) => selectedRowMap.value.has(child[props.valueKey]) || indeterminateRowMap.value.has(child[props.valueKey]),
		)

		const parentKey = parent[props.valueKey]

		if (allSelected) {
			selectedRowMap.value.set(parentKey, true)
			indeterminateRowMap.value.delete(parentKey)
		} else if (someSelected) {
			selectedRowMap.value.delete(parentKey)
			indeterminateRowMap.value.set(parentKey, true)
		} else {
			selectedRowMap.value.delete(parentKey)
			indeterminateRowMap.value.delete(parentKey)
		}
	}

	// 批量更新父节点状态
	const batchUpdateParentState = (rows) => {
		if (props.checkStrictly) return

		const parentIds = collectParentIds(rows)
		parentIds.forEach(updateParentSelectionState)
	}

	// 切换行选择状态
	const toggleRowSelection = (row) => {
		if (!row?.[props.valueKey]) return

		const nodeKey = row[props.valueKey]
		const isSelected = selectedRowMap.value.has(nodeKey)
		const rowsToUpdate = [row]

		// 如果不是严格模式，包含所有子节点
		if (!props.checkStrictly) {
			rowsToUpdate.push(...getAllChildren(row))
		}

		// 批量更新选择状态
		updateSelectionState(rowsToUpdate, !isSelected)

		// 更新父节点状态
		if (!props.checkStrictly) {
			batchUpdateParentState(rowsToUpdate)
		}

		// 发送事件
		emitSelectionChange(getSelectedRows())
		emit('selection-change', {
			selected: !isSelected,
			indeterminate: indeterminateRowMap.value.has(nodeKey),
			node: row,
		})
	}

	// 更新选择状态工具函数
	const updateSelectionState = (rows, selected) => {
		rows.forEach((node) => {
			const nodeKey = node?.[props.valueKey]
			if (!nodeKey) return

			if (selected) {
				selectedRowMap.value.set(nodeKey, true)
				indeterminateRowMap.value.delete(nodeKey)
			} else {
				selectedRowMap.value.delete(nodeKey)
				indeterminateRowMap.value.delete(nodeKey)
			}
		})
	}

	// 切换全选状态
	const toggleAllSelection = () => {
		if (visibleRows.value.length === 0) return

		const isAllSelected = allSelected.value

		if (isAllSelected) {
			// 取消全选
			selectedRowMap.value.clear()
			indeterminateRowMap.value.clear()
		} else {
			// 全选可见行
			visibleRows.value.forEach((row) => {
				if (row?.[props.valueKey]) {
					selectedRowMap.value.set(row[props.valueKey], true)
				}
			})
			indeterminateRowMap.value.clear()
		}

		emitSelectionChange(getSelectedRows())
	}

	// 获取选中的行
	const getSelectedRows = () => {
		return Array.from(selectedRowMap.value.keys())
			.map((id) => nodeMap.value.get(id))
			.filter(Boolean)
	}

	// 清空所有选择
	const clearSelection = () => {
		selectedRowMap.value.clear()
		indeterminateRowMap.value.clear()
		emit('update:selected', [])
	}

	// 设置行选择状态
	const setRowSelection = (rows, selected = true) => {
		if (!Array.isArray(rows)) {
			rows = [rows]
		}

		// 快速清空操作
		if (!selected && rows.length > 5 && rows.length === selectedRowMap.value.size) {
			clearSelection()
			return
		}

		// 收集需要更新的行
		const rowsToUpdate = collectRowsToUpdate(rows)

		// 批量更新选择状态
		updateSelectionState(rowsToUpdate, selected)

		// 更新父节点状态
		if (!props.checkStrictly) {
			batchUpdateParentState(rowsToUpdate)
		}

		// 发送事件
		emit('update:selected', getSelectedRows())

		// 发送详细事件（限制数量）
		rows.slice(0, 5).forEach((row) => {
			if (row?.[props.valueKey]) {
				emit('selection-change', {
					selected,
					indeterminate: indeterminateRowMap.value.has(row[props.valueKey]),
					node: row,
				})
			}
		})
	}

	// 收集需要更新的行
	const collectRowsToUpdate = (rows) => {
		const rowsToUpdate = []
		const uniqueIds = new Set()

		// 收集直接选择的行
		rows.forEach((row) => {
			if (row?.[props.valueKey] && !uniqueIds.has(row[props.valueKey])) {
				uniqueIds.add(row[props.valueKey])
				rowsToUpdate.push(row)
			}
		})

		// 如果启用父子关联，收集子节点
		if (!props.checkStrictly) {
			const childrenToAdd = []
			rowsToUpdate.forEach((row) => {
				if (row?.children?.length) {
					getAllChildren(row).forEach((child) => {
						if (child?.[props.valueKey] && !uniqueIds.has(child[props.valueKey])) {
							uniqueIds.add(child[props.valueKey])
							childrenToAdd.push(child)
						}
					})
				}
			})
			rowsToUpdate.push(...childrenToAdd)
		}

		return rowsToUpdate
	}

	// 选择状态计算
	const allSelected = computed(() => {
		return visibleRows.value.length > 0 && visibleRows.value.every((row) => selectedRowMap.value.has(row[props.valueKey]))
	})

	const hasIndeterminate = computed(() => {
		return visibleRows.value.some((row) => indeterminateRowMap.value.has(row[props.valueKey]))
	})

	return {
		selectedRowMap,
		indeterminateRowMap,
		allSelected,
		hasIndeterminate,
		isRowSelected,
		isRowIndeterminate,
		toggleRowSelection,
		toggleAllSelection,
		setRowSelection,
		clearSelection,
		getSelectedRows,
	}
}
