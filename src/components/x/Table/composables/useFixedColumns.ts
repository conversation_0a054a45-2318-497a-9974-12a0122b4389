/**
 * 处理固定列相关的逻辑
 * @param {Object} props - 组件props
 * @param {Object} flattenedColumns - 扁平化的列配置
 * @param {Object} flattenedColumnsMap - 列映射
 * @returns {Object} - 固定列相关的计算属性和方法
 */
export function useFixedColumns(props, flattenedColumns, flattenedColumnsMap) {
	// 检查是否有左侧固定列
	const hasFixedLeft = computed(() => {
		return flattenedColumns.value.some((col) => col.fixed === 'left')
	})

	// 判断是否是最后一个左侧固定列
	const isLastFixedLeft = (col) => {
		if (col.fixed !== 'left') return false
		const colIndex = flattenedColumnsMap.value.get(col[props.colValueKey])
		if (colIndex === undefined) return false
		return colIndex + 1 >= flattenedColumns.value.length || flattenedColumns.value[colIndex + 1].fixed !== 'left'
	}

	// 判断是否是第一个右侧固定列
	const isFirstFixedRight = (col) => {
		if (col.fixed !== 'right') return false
		const colIndex = flattenedColumnsMap.value.get(col[props.colValueKey])
		if (colIndex === undefined) return false
		return colIndex === 0 || flattenedColumns.value[colIndex - 1].fixed !== 'right'
	}

	// 查找第一个叶子列
	const findFirstLeafColumn = (col) => {
		return col.children?.length ? findFirstLeafColumn(col.children[0]) : col
	}

	// 获取列索引（处理多级表头）
	const getColumnIndex = (col, index) => {
		if (col[props.colValueKey]) {
			const flattenedIndex = flattenedColumnsMap.value.get(col[props.colValueKey])
			if (flattenedIndex !== undefined) return flattenedIndex
		}

		if (col.children?.length) {
			const firstLeaf = findFirstLeafColumn(col)
			if (firstLeaf) {
				const leafIndex = flattenedColumnsMap.value.get(firstLeaf[props.colValueKey])
				if (leafIndex !== undefined) return leafIndex
			}
		}

		return index
	}

	// 计算左侧偏移量
	const calculateLeftOffset = (colIndex) => {
		let leftOffset = 0

		// 如果有选择框列
		if (props.selectable) {
			leftOffset += parseInt(props.selectColWidth) || 40
		}

		// 累加之前所有左侧固定列的宽度
		for (let i = 0; i < colIndex; i++) {
			if (flattenedColumns.value[i]?.fixed === 'left') {
				leftOffset += flattenedColumns.value[i].width || 100
			}
		}

		return leftOffset
	}

	// 计算右侧偏移量
	const calculateRightOffset = (colIndex) => {
		let rightOffset = 0

		// 从当前列之后的所有右侧固定列累加宽度
		for (let i = flattenedColumns.value.length - 1; i > colIndex; i--) {
			if (flattenedColumns.value[i]?.fixed === 'right') {
				rightOffset += flattenedColumns.value[i].width || 100
			}
		}

		return rightOffset
	}

	// 计算固定列的样式
	const getFixedStyle = (col, index) => {
		if (!col.fixed) return {}

		const style = { position: 'sticky', zIndex: 1 }
		let colIndex = getColumnIndex(col, index)

		if (col.fixed === 'left') {
			style.left = `${calculateLeftOffset(colIndex)}px`
		} else if (col.fixed === 'right') {
			style.right = `${calculateRightOffset(colIndex)}px`
		}

		return style
	}

	return {
		hasFixedLeft,
		isLastFixedLeft,
		isFirstFixedRight,
		getFixedStyle,
	}
}
