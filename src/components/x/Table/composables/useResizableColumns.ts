/**
 * 处理列宽调整相关的逻辑
 * @param {Object} props - 组件props
 * @param {Object} flattenedColumns - 扁平化的列配置
 * @param {Object} flattenedColumnsMap - 列映射
 * @param {Function} emit - 事件发射器
 * @returns {Object} - 列宽调整相关的响应式变量和方法
 */
export function useResizableColumns(props, flattenedColumns, flattenedColumnsMap, emit) {
	// 列调整状态
	const isDragging = ref(false)
	const startX = ref(0)
	const startWidth = ref(0)
	const columnIndex = ref(null)
	const currentResizeColumn = ref(null)
	const resizeIndicatorPosition = ref(0)
	const minColumnWidth = ref(80) // 增加最小列宽，提供更好的用户体验
	const updateKey = ref(0) // 强制更新key

	// 节流函数 - 仅更新CSS宽度，避免DOM重新渲染
	let updateTimer: number | null = null
	const throttledCSSUpdate = (newWidth: number) => {
		if (updateTimer) return

		updateTimer = requestAnimationFrame(() => {
			updateColumnCSS(newWidth)
			updateTimer = null
		})
	}

	// 直接更新CSS宽度，避免重新渲染多级表头
	const updateColumnCSS = (newWidth: number) => {
		if (!currentResizeColumn.value) return

		const columnId = currentResizeColumn.value[props.colValueKey]
		if (!columnId) return

		// 更新表头列宽
		const headerCols = document.querySelectorAll(`th[data-col="${columnId}"]`)
		headerCols.forEach((col) => {
			;(col as HTMLElement).style.width = `${newWidth}px`
		})

		// 更新 colgroup 中对应列的宽度
		const flattenedIndex = flattenedColumnsMap.value.get(columnId)
		if (flattenedIndex !== undefined) {
			const selectOffset = props.selectable ? 1 : 0
			const headerColElements = document.querySelectorAll(`.table-header colgroup col:nth-child(${flattenedIndex + 1 + selectOffset})`)
			const bodyColElements = document.querySelectorAll(`.table-body colgroup col:nth-child(${flattenedIndex + 1 + selectOffset})`)

			headerColElements.forEach((col) => {
				;(col as HTMLElement).style.width = `${newWidth}px`
			})
			bodyColElements.forEach((col) => {
				;(col as HTMLElement).style.width = `${newWidth}px`
			})
		}
	}

	// 开始拖拽调整列宽
	const onDragStart = (event, index, column) => {
		event.preventDefault()
		event.stopPropagation()

		const tableHeader = event.target.closest('th')
		if (!tableHeader) return

		// 初始化拖拽状态
		initDragState(event, index, column, tableHeader)

		// 设置指示线位置
		updateResizeIndicator(event)

		// 绑定拖拽事件
		bindDragEvents()
	}

	// 初始化拖拽状态
	const initDragState = (event, index, column, tableHeader) => {
		isDragging.value = true
		columnIndex.value = index
		startX.value = event.clientX
		currentResizeColumn.value = column

		// 获取当前列的实际宽度
		let currentWidth = column.width

		// 如果列没有设置宽度，从DOM获取
		if (!currentWidth) {
			// 尝试从tableHeader获取宽度
			const computedStyle = window.getComputedStyle(tableHeader)
			currentWidth = parseInt(computedStyle.width) || tableHeader.offsetWidth || 100
			// 同步到列配置
			column.width = currentWidth
		}

		// 确保宽度是数字类型
		if (typeof currentWidth === 'string') {
			currentWidth = parseInt(currentWidth.replace('px', '')) || 100
		}

		// 确保有效的宽度值
		currentWidth = Math.max(currentWidth || 100, minColumnWidth.value)
		column.width = currentWidth
		startWidth.value = currentWidth
	}

	// 更新调整指示线位置
	const updateResizeIndicator = (event) => {
		if (!currentResizeColumn.value) return

		const columnId = currentResizeColumn.value[props.colValueKey]
		const currentWidth = currentResizeColumn.value.width

		const columnPosition = calculateColumnPosition(columnId, currentWidth)
		if (columnPosition !== null) {
			resizeIndicatorPosition.value = columnPosition
		}
	}

	// 绑定拖拽事件
	const bindDragEvents = () => {
		document.addEventListener('mousemove', onDragMove)
		document.addEventListener('mouseup', onDragEnd)
		document.body.style.userSelect = 'none'
		document.body.style.cursor = 'col-resize'
	}

	// 拖拽移动处理
	const onDragMove = (event) => {
		if (!isDragging.value || !currentResizeColumn.value) return

		const currentX = event.clientX
		const diff = currentX - startX.value
		const newWidth = Math.max(minColumnWidth.value, startWidth.value + diff)

		// 更新列宽
		currentResizeColumn.value.width = newWidth

		// 更新指示线位置
		updateIndicatorPosition(event)

		// 在拖拽过程中只更新 CSS 列宽，不重新渲染 DOM 结构
		throttledCSSUpdate(newWidth)
	}

	// 更新指示线位置
	const updateIndicatorPosition = (event) => {
		if (!currentResizeColumn.value) return

		const columnId = currentResizeColumn.value[props.colValueKey]
		const newWidth = currentResizeColumn.value.width

		const columnPosition = calculateColumnPosition(columnId, newWidth)
		if (columnPosition !== null) {
			resizeIndicatorPosition.value = columnPosition
		}
	}

	// 计算列的实际位置
	const calculateColumnPosition = (columnId, width) => {
		if (!columnId) return null

		// 找到对应的表头单元格
		const headerCell = document.querySelector(`th[data-col="${columnId}"]`)
		if (!headerCell) return null

		const tableContainer = document.querySelector('.table-container')
		if (!tableContainer) return null

		const tableRect = tableContainer.getBoundingClientRect()
		const cellRect = headerCell.getBoundingClientRect()

		// 最简单的方法：直接使用单元格的右边界位置
		// 这与resize handle的位置逻辑一致（right: 0）
		const indicatorPosition = cellRect.right - tableRect.left

		return indicatorPosition
	}

	// 强制更新表格布局
	const forceTableUpdate = () => {
		// 保存当前滚动位置
		const tableContainer = document.querySelector('.table-body-container')
		const headerWrapper = document.querySelector('.table-header-wrapper')
		const currentScrollLeft = tableContainer?.scrollLeft || 0
		const currentScrollTop = tableContainer?.scrollTop || 0

		// 增加更新计数器，强制Vue重新渲染
		updateKey.value++

		nextTick(() => {
			// 强制重新计算表格布局
			const tables = document.querySelectorAll('.tree-table')
			tables.forEach((table) => {
				// 先设置为auto让浏览器重新计算
				table.style.tableLayout = 'auto'
				// 立即设置回fixed
				table.offsetHeight // 强制重排
				table.style.tableLayout = 'fixed'
			})

			// 恢复滚动位置
			if (tableContainer && (currentScrollLeft > 0 || currentScrollTop > 0)) {
				requestAnimationFrame(() => {
					tableContainer.scrollLeft = currentScrollLeft
					tableContainer.scrollTop = currentScrollTop

					// 同步表头滚动
					if (headerWrapper) {
						headerWrapper.scrollLeft = currentScrollLeft
					}
				})
			}
		})
	}

	// 温和的状态同步，避免拖拽结束后的闪烁
	const gentleUpdate = () => {
		// 由于我们已经通过CSS直接更新了样式，并且同步了数据状态
		// 通常不需要再进行强制的DOM重新渲染
		// 只需要确保表格布局计算正确即可
		nextTick(() => {
			const tables = document.querySelectorAll('.tree-table')
			tables.forEach((table) => {
				// 触发重新计算，但不重新渲染DOM结构
				table.offsetHeight
			})
		})
	}

	// 结束拖拽
	const onDragEnd = (event) => {
		if (!isDragging.value) return

		isDragging.value = false

		// 同步列宽到原始配置
		syncColumnWidth()

		// 延迟进行温和的状态同步，避免闪烁
		gentleUpdate()

		// 清理拖拽状态
		cleanupDragState()
	}

	// 同步列宽到原始配置
	const syncColumnWidth = () => {
		if (!currentResizeColumn.value) return

		const columnId = currentResizeColumn.value[props.colValueKey]
		const finalWidth = currentResizeColumn.value.width

		if (columnId && finalWidth) {
			updateOriginalColumnWidth(columnId, finalWidth)
		}

		// 触发列宽改变事件
		emit('column-resize', {
			column: currentResizeColumn.value,
			width: finalWidth,
		})
	}

	// 更新原始列配置中的宽度
	const updateOriginalColumnWidth = (columnId, width) => {
		const updateColumns = (columns) => {
			columns.forEach((col) => {
				if (col[props.colValueKey] === columnId) {
					col.width = width
				}
				if (col.children?.length) {
					updateColumns(col.children)
				}
			})
		}
		updateColumns(props.columns)
	}

	// 清理拖拽状态
	const cleanupDragState = () => {
		currentResizeColumn.value = null
		columnIndex.value = null
		document.body.style.userSelect = ''
		document.body.style.cursor = ''
		document.removeEventListener('mousemove', onDragMove)
		document.removeEventListener('mouseup', onDragEnd)

		// 清理节流定时器
		if (updateTimer) {
			cancelAnimationFrame(updateTimer)
			updateTimer = null
		}
	}

	// 组件卸载时清理
	onBeforeUnmount(() => {
		cleanupDragState()
	})

	return {
		isDragging,
		resizeIndicatorPosition,
		onDragStart,
		updateKey, // 导出更新key供模板使用
	}
}
