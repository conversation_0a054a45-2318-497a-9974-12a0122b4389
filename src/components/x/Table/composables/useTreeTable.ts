/**
 * 处理树形表格相关的逻辑
 * @param {Object} props - 组件props
 * @param {Function} emit - 事件发射器
 * @param {Function} checkScrollbarVisibility - 检查滚动条可见性的函数
 * @param {Function} forceScrollUpdate - 强制滚动更新的函数
 * @returns {Object} - 树形表格相关的响应式变量和方法
 */
export function useTreeTable(props, emit, checkScrollbarVisibility, forceScrollUpdate) {
	// 数据状态
	const nodeMap = ref(new Map())
	const treeData = ref([])
	const expandedRowKeys = ref(new Set())
	const lazyLoadingNodes = ref(new Map())

	// 扁平化树形数据
	const flattenData = (data) => {
		if (!data?.length) return []

		const result = []
		const stack = [...data]
		nodeMap.value.clear()

		while (stack.length) {
			const node = stack.shift()
			if (!node) continue

			nodeMap.value.set(node[props.valueKey], node)
			result.push(node)

			// 如果节点展开且有子节点，将子节点添加到栈前面
			if (node.isExpanded && node.children?.length) {
				stack.unshift(...node.children)
			}
		}

		return result
	}

	// 处理后的数据（树形或扁平）
	const processedData = computed(() => {
		if (!props.treeable) {
			// 非树形表格：建立节点映射并返回原始数据
			props.data?.forEach((item) => {
				nodeMap.value.set(item[props.valueKey], item)
			})
			return props.data || []
		}
		// 树形表格：返回扁平化数据
		return flattenData(treeData.value)
	})

	// 可见行数据
	const visibleRows = computed(() => {
		if (!props.treeable) {
			return processedData.value
		}

		const result = []
		if (!processedData.value?.length) return result

		// 迭代处理节点可见性
		const processNode = (node, isVisible = true) => {
			if (!node) return

			if (isVisible) {
				result.push(node)
			}

			// 检查子节点是否需要展示
			const isExpanded = expandedRowKeys.value.has(node[props.valueKey])
			if (isExpanded && node.children?.length) {
				node.children.forEach((child) => processNode(child, isVisible))
			}
		}

		// 处理所有根节点
		processedData.value.filter((node) => node?.level === 0).forEach((rootNode) => processNode(rootNode, true))

		return result
	})

	// 初始化展开状态
	const initExpandState = () => {
		expandedRowKeys.value.clear()
		if (props.defaultExpandAll) {
			const collectKeys = (nodes) => {
				nodes?.forEach((node) => {
					if (node[props.valueKey]) {
						expandedRowKeys.value.add(node[props.valueKey])
					}
					if (node.children?.length) {
						collectKeys(node.children)
					}
				})
			}
			collectKeys(props.data)
		}
	}

	// 处理树形数据转换
	const updateTreeData = (data) => {
		if (!props.treeable) return data

		const clonedData = Array.isArray(data) ? [...data] : []

		// 检查是否为扁平结构
		const isFlatList = clonedData.length > 0 && clonedData.every((item) => !item.children)

		// 转换扁平结构为树形结构
		let treeStructure = clonedData
		if (isFlatList) {
			treeStructure = buildTreeFromFlatData(clonedData)
		}

		// 初始化展开状态
		if (props.defaultExpandAll) {
			initExpandState()
		} else {
			expandedRowKeys.value.clear()
		}

		return assignNodeLevels(treeStructure)
	}

	// 从扁平数据构建树形结构
	const buildTreeFromFlatData = (data) => {
		const idMap = new Map()
		const rootItems = []

		// 建立ID映射
		data.forEach((item) => {
			idMap.set(item[props.valueKey], { ...item, children: [] })
		})

		// 建立父子关系
		data.forEach((item) => {
			const node = idMap.get(item[props.valueKey])
			if (item.parentId && idMap.has(item.parentId)) {
				idMap.get(item.parentId).children.push(node)
			} else {
				rootItems.push(node)
			}
		})

		return rootItems
	}

	// 分配节点层级
	const assignNodeLevels = (nodes, level = 0, parentId = null) => {
		return nodes.map((node) => {
			const newNode = {
				...node,
				level,
				parentId,
				id: node[props.valueKey] || `${level}-${Math.random().toString(36).substr(2, 9)}`,
				isExpanded: expandedRowKeys.value.has(node[props.valueKey]),
				hasChildren: !!(node.children?.length || node.hasChildren),
				loading: false,
				loadError: false,
				children: node.children ? assignNodeLevels(node.children, level + 1, node[props.valueKey]) : [],
			}
			return newNode
		})
	}

	// 节点工具函数
	const hasChildren = (row) => row.children?.length > 0 || row.hasChildren
	const findNodeById = (id) => nodeMap.value.get(id)

	// 获取兄弟节点
	const getSiblingNodes = (row) => {
		if (!row?.[props.valueKey]) return []

		if (!row.parentId) {
			// 根节点：查找其他根节点
			return processedData.value.filter((node) => !node.parentId && node[props.valueKey] !== row[props.valueKey])
		}

		// 子节点：查找父节点的其他子节点
		const parentNode = findNodeById(row.parentId)
		return parentNode?.children?.filter((node) => node[props.valueKey] !== row[props.valueKey]) || []
	}

	// 展开/收起切换
	const toggleExpand = (row) => {
		if (!row?.[props.valueKey]) return

		const nodeKey = row[props.valueKey]
		const isExpanded = expandedRowKeys.value.has(nodeKey)

		if (isExpanded) {
			// 收起操作
			handleCollapse(row, nodeKey)
		} else {
			// 展开操作
			handleExpand(row, nodeKey)
		}

		emit('expand-change', row)
	}

	// 处理收起操作
	const handleCollapse = (row, nodeKey) => {
		expandedRowKeys.value.delete(nodeKey)
		row.isExpanded = false
	}

	// 处理展开操作
	const handleExpand = (row, nodeKey) => {
		expandedRowKeys.value.add(nodeKey)

		// 手风琴模式：收起兄弟节点
		if (props.accordion) {
			collapseAllSiblings(row)
		}

		// 懒加载处理
		if (shouldLoadChildren(row)) {
			startLazyLoad(row)
		}

		row.isExpanded = true
	}

	// 收起所有兄弟节点
	const collapseAllSiblings = (row) => {
		const siblings = getSiblingNodes(row)
		siblings.forEach((sibling) => {
			if (sibling[props.valueKey]) {
				expandedRowKeys.value.delete(sibling[props.valueKey])
				sibling.isExpanded = false
			}
		})
	}

	// 判断是否需要懒加载
	const shouldLoadChildren = (row) => {
		return props.loadData && row.hasChildren && (!row.children || row.children.length === 0)
	}

	// 开始懒加载
	const startLazyLoad = (row) => {
		row.loading = true
		lazyLoadingNodes.value.set(row[props.valueKey], { row, retries: 0 })
		loadRowChildren(row)
	}

	// 懒加载子节点
	const loadRowChildren = async (row) => {
		if (!row?.[props.valueKey] || !props.loadData) return

		const nodeKey = row[props.valueKey]
		const loadState = lazyLoadingNodes.value.get(nodeKey)
		if (!loadState) return

		const retryLoad = async (retries = 0) => {
			try {
				const children = await props.loadData(row)

				if (!Array.isArray(children)) {
					throw new Error('Lazy load data must return an array')
				}

				updateNodeWithChildren(row, children)
				lazyLoadingNodes.value.delete(nodeKey)
			} catch (error) {
				console.error('Table: 懒加载失败', error)

				if (retries < props.lazyLoadRetries) {
					// 递增延迟重试
					setTimeout(() => retryLoad(retries + 1), props.lazyLoadRetryDelay * (retries + 1))
				} else {
					handleLoadError(row, error)
				}
			}
		}

		await retryLoad()
	}

	// 更新节点的子节点数据
	const updateNodeWithChildren = (row, children) => {
		const node = findNodeById(row[props.valueKey])
		if (!node) return

		node.children = children.map((child) => ({
			...child,
			id: child[props.valueKey] || `${row[props.valueKey]}-${Math.random().toString(36).substr(2, 9)}`,
			parentId: row[props.valueKey],
			level: row.level + 1,
			isExpanded: false,
			hasChildren: !!child.hasChildren,
			loading: false,
			loadError: false,
		}))

		node.loading = false
		node.hasChildren = children.length > 0
		treeData.value = [...treeData.value] // 触发响应式更新
	}

	// 处理加载错误
	const handleLoadError = (row, error) => {
		const node = findNodeById(row[props.valueKey])
		if (node) {
			node.loading = false
			node.loadError = true
			treeData.value = [...treeData.value]
		}
		lazyLoadingNodes.value.delete(row[props.valueKey])
		emit('lazy-load-error', row, error)
	}

	// 删除节点
	const deleteNode = (nodes) => {
		if (!Array.isArray(nodes)) {
			nodes = [nodes]
		}
		if (!nodes.length) return false

		const nodeIdsToDelete = new Set(nodes.map((node) => node?.[props.valueKey]).filter(Boolean))

		const deleteSuccess = props.treeable ? deleteFromTreeData(nodeIdsToDelete) : deleteFromFlatData(nodeIdsToDelete)

		if (deleteSuccess) {
			handleDeleteSuccess(nodes)
		}

		return deleteSuccess
	}

	// 从树形数据中删除节点
	const deleteFromTreeData = (idsToDelete) => {
		const deleteNodesById = (data, idsToDelete) => {
			if (!data?.length) return false

			let hasDeleted = false
			for (let i = data.length - 1; i >= 0; i--) {
				const node = data[i]
				if (!node) continue

				const nodeId = node[props.valueKey]
				if (nodeId && idsToDelete.has(nodeId)) {
					// 清理节点状态
					cleanupNodeState(nodeId)
					// 删除节点
					data.splice(i, 1)
					hasDeleted = true
					continue
				}

				// 递归处理子节点
				if (node.children?.length) {
					if (deleteNodesById(node.children, idsToDelete)) {
						hasDeleted = true
					}
				}
			}
			return hasDeleted
		}

		return deleteNodesById(treeData.value, idsToDelete)
	}

	// 从扁平数据中删除节点
	const deleteFromFlatData = (idsToDelete) => {
		const originalData = props.data
		if (!originalData?.length) return false

		let hasDeleted = false
		for (let i = originalData.length - 1; i >= 0; i--) {
			const node = originalData[i]
			if (node?.[props.valueKey] && idsToDelete.has(node[props.valueKey])) {
				originalData.splice(i, 1)
				hasDeleted = true
			}
		}
		return hasDeleted
	}

	// 清理节点状态
	const cleanupNodeState = (nodeId) => {
		expandedRowKeys.value.delete(nodeId)
		nodeMap.value.delete(nodeId)
	}

	// 处理删除成功后的操作
	const handleDeleteSuccess = (nodes) => {
		nodeMap.value.clear()

		if (props.treeable) {
			treeData.value = updateTreeData([...treeData.value])
		}

		emit('node-delete', nodes)

		nextTick(() => {
			checkScrollbarVisibility()
			forceScrollUpdate()
		})
	}

	// 保存节点（新增或更新）
	const saveNode = (nodes) => {
		if (!nodes) return false
		if (!Array.isArray(nodes)) {
			nodes = [nodes]
		}
		if (!nodes.length) return false

		// 处理节点数据
		const processedNodes = nodes.map(processNodeData)

		if (props.treeable) {
			handleTreeDataSave(processedNodes)
		} else {
			handleFlatDataSave(processedNodes)
		}

		// 更新界面
		nextTick(() => {
			checkScrollbarVisibility()
			forceScrollUpdate()
		})

		return true
	}

	// 处理节点数据
	const processNodeData = (node) => {
		if (!node) return null

		const nodeId = node[props.valueKey] || `${Math.random().toString(36).substr(2, 9)}`
		return {
			...node,
			[props.valueKey]: nodeId,
			hasChildren: node.children?.length > 0 || !!node.hasChildren,
			loading: false,
			loadError: false,
		}
	}

	// 处理树形数据保存
	const handleTreeDataSave = (processedNodes) => {
		processedNodes.forEach((node) => {
			const nodeId = node[props.valueKey]
			const existingNode = findNodeById(nodeId)

			if (existingNode) {
				updateExistingTreeNode(node, existingNode, nodeId)
			} else {
				addNewTreeNode(node)
			}
		})
		treeData.value = [...treeData.value] // 触发响应式更新
	}

	// 更新现有树形节点
	const updateExistingTreeNode = (node, existingNode, nodeId) => {
		const updatedNode = {
			...node,
			children: existingNode.children || [],
			isExpanded: existingNode.isExpanded,
			level: existingNode.level,
			parentId: existingNode.parentId,
			hasChildren: !!(existingNode.children?.length || node.hasChildren),
			loading: false,
			loadError: false,
		}

		nodeMap.value.set(nodeId, updatedNode)
		updateNodeInTree(treeData.value, nodeId, updatedNode)
	}

	// 在树中更新节点
	const updateNodeInTree = (nodes, nodeId, updatedNode) => {
		for (let i = 0; i < nodes.length; i++) {
			if (nodes[i][props.valueKey] === nodeId) {
				// 保持子节点和状态
				const { children, isExpanded, level, parentId } = nodes[i]
				Object.assign(nodes[i], updatedNode, { children, isExpanded, level, parentId })
				return true
			}
			if (nodes[i].children?.length && updateNodeInTree(nodes[i].children, nodeId, updatedNode)) {
				return true
			}
		}
		return false
	}

	// 添加新的树形节点
	const addNewTreeNode = (node) => {
		const parentId = node.parentId

		if (parentId) {
			const parentNode = findNodeById(parentId)
			if (parentNode) {
				node.level = parentNode.level + 1
				node.parentId = parentId
				parentNode.children = [node, ...(parentNode.children || [])]
				parentNode.hasChildren = true
			} else {
				addAsRootNode(node)
			}
		} else {
			addAsRootNode(node)
		}

		nodeMap.value.set(node[props.valueKey], node)
	}

	// 添加为根节点
	const addAsRootNode = (node) => {
		node.level = 0
		node.parentId = null
		treeData.value = [node, ...treeData.value]
	}

	// 处理扁平数据保存
	const handleFlatDataSave = (processedNodes) => {
		// 直接修改原始数据数组，确保响应式更新
		processedNodes.forEach((node) => {
			const existingIndex = props.data.findIndex((item) => item[props.valueKey] === node[props.valueKey])

			if (existingIndex !== -1) {
				// 更新现有节点 - 直接修改原数组中的对象
				Object.assign(props.data[existingIndex], node)
			} else {
				// 添加新节点 - 直接添加到原数组开头
				props.data.unshift(node)
			}

			nodeMap.value.set(node[props.valueKey], node)
		})

		// 同时发射事件，保持向后兼容
		emit('update:data', props.data)
	}

	return {
		nodeMap,
		treeData,
		expandedRowKeys,
		processedData,
		visibleRows,
		hasChildren,
		toggleExpand,
		findNodeById,
		updateTreeData,
		deleteNode,
		saveNode,
	}
}
