interface Modal {
	close: () => void
}

class ModalManager {
	private static instance: ModalManager
	private activeModals: Modal[] = []
	// eslint-disable-next-line no-unused-vars
	private keydownHandler: ((event: KeyboardEvent) => void) | null = null

	private constructor() {
		this.keydownHandler = (event: KeyboardEvent) => {
			if (event.key === 'Escape') {
				event.preventDefault()
				event.stopPropagation()
				this.closeTop()
			}
		}
	}

	static getInstance(): ModalManager {
		if (!ModalManager.instance) {
			ModalManager.instance = new ModalManager()
		}
		return ModalManager.instance
	}

	add(modal: Modal) {
		this.activeModals.push(modal)
		if (this.activeModals.length === 1 && this.keydownHandler) {
			document.addEventListener('keydown', this.keydownHandler)
		}
	}

	remove(modal: Modal) {
		const index = this.activeModals.indexOf(modal)
		if (index > -1) {
			this.activeModals.splice(index, 1)
			if (this.activeModals.length === 0 && this.keydownHandler) {
				document.removeEventListener('keydown', this.keydownHandler)
			}
		}
	}

	closeTop() {
		if (this.activeModals.length > 0) {
			this.activeModals[this.activeModals.length - 1].close()
		}
	}
}

export const modalManager = ModalManager.getInstance()
