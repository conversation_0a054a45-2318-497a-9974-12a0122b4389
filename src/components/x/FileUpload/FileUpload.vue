<template>
	<div class="">
		<!-- 消息提示 -->

		<!-- 上传区域 -->
		<div
			class="flex flex-col items-center justify-center overflow-hidden b-2 rounded-lg b-dashed transition-colors"
			:class="{
				'b-border bg-tip': !isDragging,
				'b-primary bg-primary-10': isDragging,
				'cursor-not-allowed': disabled,
			}"
			@dragover.prevent="handleDragOver"
			@dragleave="handleDragLeave"
			@drop.prevent="handleDrop"
			@click="triggerFileInput"
		>
			<input ref="fileInput" type="file" class="hidden" :multiple="multiple" :accept="accept" @change="handleFileSelect" />
			<div class="h2rem max-h-2rem wfull mb-xxs">
				<div
					v-show="message.show"
					class="hfull flex items-center justify-between rounded-0.2rem transition-opacity px-xs"
					:style="{ color: themeColors?.[message.status] }"
				>
					<div class="flex items-center space-x-2">
						<div v-if="message.status === 'success'" class="i-heroicons:check-circle text-lg" />
						<div v-if="message.status === 'error'" class="i-heroicons:exclamation-circle text-lg" />
						<span class="text-sm">{{ message.message }}</span>
					</div>
				</div>
			</div>
			<div class="flex flex-col items-center justify-center">
				<XIconsUploadCloud class="text-3xl !text-aid" />
				<p class="text-gray-600">
					{{ uploadStatus === 'uploading' ? '上传中...' : '点击上传或拖放文件到这里' }}
				</p>
				<p class="text-aid text-sm">
					支持格式：
					<span class="color-orange">{{ formattedAccept }}</span>
					，最大 {{ maxSize }}MB
				</p>
			</div>
			<!-- 进度条 -->
			<div class="relative mt-4 h-1rem wfull flex items-center overflow-hidden" :class="[{ 'bg-gray-200': uploadStatus === 'uploading' }]">
				<div
					v-show="uploadStatus === 'uploading'"
					class="absolute h-full bg-primary-50 transition-all duration-300"
					:style="{ width: `${totalProgress}%` }"
				/>
				<p v-show="uploadStatus === 'uploading'" class="wfull text-right text-xxs">已完成 {{ completedCount }}/{{ selectedFiles.length }}</p>
			</div>
		</div>
		<!-- 操作按钮 -->
		<div v-if="selectedFiles.length && !autoUpload" class="h2rem flex items-center justify-end mt-xs gap-base">
			<!--			<XButton v-if="selectedFiles.length && uploadStatus !== 'uploading'" class="!bg-warning !text-xs" @click="clearFiles">清空</XButton>-->
			<XButton
				v-if="!autoUpload"
				class="!text-xs"
				:disabled="!selectedFiles.length || disabled || uploadStatus === 'uploading' || uploadStatus === 'success'"
				@click="startUpload"
			>
				{{ uploadStatus === 'uploading' ? '上传中...' : '开始上传' }}
			</XButton>
		</div>
		<!-- 文件列表 -->
		<div v-if="selectedFiles.length" class="mt-sm">
			<div v-for="(file, index) in selectedFiles" :key="file.id" class="relative flex items-center rounded-lg bg-gray-50 p-xs">
				<!-- 预览图 -->
				<div class="h-2.5rem w-2.5rem flex-shrink-0 cursor-pointer" @click="file.remoteUrl && xfilepreview.preview(file.remoteUrl, file.name)">
					<img v-if="file.preview" :src="file.preview" class="h-full w-full rounded object-contain" />
					<div v-else class="h-full w-full flex items-center justify-center rounded bg-gray-200">
						<div class="i-heroicons:document-text text-gray-400 text-xl" />
					</div>
				</div>

				<!-- 文件信息 -->
				<div class="min-w-0 flex-1 pr-3rem px-xs">
					<!-- 文件名 -->
					<XFileLink :name="file.name" :url="file.remoteUrl" />
					<!-- 文件大小 -->
					<p class="text-gray-500 text-xs">{{ formatFileSize(file.size) }}</p>
				</div>

				<!-- 状态指示 -->
				<div class="absolute bottom-1 right-1 ml-3 flex items-center">
					<div class="flex items-center text-xxs space-x-1">
						<!-- 上传中 -->
						<template v-if="file.status === 'uploading'">
							<div class="spin i-heroicons:arrow-path text-blue-500" />
							<span class="text-blue-600">{{ file.progress }}%</span>
						</template>

						<!-- 上传成功 -->
						<template v-if="file.status === 'success'">
							<div class="i-heroicons:check-circle text-green-500" />
							<span class="text-green-600">上传成功</span>
						</template>

						<!-- 上传失败 -->
						<template v-if="file.status === 'error'">
							<div class="i-heroicons:exclamation-circle text-red-500" />
							<span class="text-red-600">{{ file.message }}</span>
						</template>

						<!-- 等待上传 -->
						<template v-if="file.status === 'pending'">
							<div class="i-heroicons:clock text-gray-400" />
							<span class="text-gray-500">等待上传</span>
						</template>
					</div>
				</div>
				<!-- 删除按钮 -->
				<XButton class="right-1 top-1 !absolute !h-1.5rem !bg-tip !text-aid !px-sm !hover:bg-aid !hover:text-danger" @click.stop="removeFile(index)">
					<div class="i-heroicons:trash cursor-pointer"></div>
				</XButton>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
import { PropType } from 'vue'

const props = defineProps({
	list: { type: Array, default: () => [] },
	multiple: { type: Boolean, default: false },
	accept: { type: String as PropType<Accept>, default: '' },
	uploadUrl: { type: String, default: import.meta.env.VITE_UPLOAD_BASE_URL },
	maxSize: { type: Number, default: 0.3 },
	disabled: { type: Boolean, default: false },
	autoUpload: { type: Boolean, default: true }, // 新增自动上传配置
	modelValue: { type: [String, Array], default: () => null }, // 新增 modelValue 支持
	name: { type: [String, Array], default: () => null }, // 新增 modelValue 支持
})

const emit = defineEmits(['success', 'error', 'progress', 'update:modelValue', 'update:name'])

// 表单上下文处理
const formContext = inject<any>('formContext', null)
const { prop: formItemProp, error } = inject('formItemContext', {})

// 状态管理
const fileInput = ref<HTMLInputElement | null>(null)
const isDragging = ref(false)
const selectedFiles = ref<UploadFile[]>([])
const isInternalUpdate = ref(false)

// 监听 modelValue 变化
watch(
	() => props.modelValue,
	async (newVal) => {
		if (isInternalUpdate.value) return
		if (newVal) {
			// 如果是字符串，转换为数组
			const files = Array.isArray(newVal) ? newVal : [newVal]
			// 处理初始文件列表
			selectedFiles.value = files.map((file) => {
				const fileUrl = typeof file === 'string' ? file : file.remoteUrl || file
				// 尝试从URL中提取文件大小参数
				let fileSize = file.size || 0

				// 处理文件名 - 从URL中提取更友好的文件名
				let fileName = ''

				if (file.name) {
					// 如果提供了名称，直接使用
					fileName = file.name
				} else if (typeof fileUrl === 'string') {
					// 从URL中提取文件名
					try {
						// 移除查询参数部分
						let cleanUrl = fileUrl.split('?')[0]
						// 获取URL的最后一部分作为文件名
						let urlParts = cleanUrl.split('/')
						fileName = urlParts[urlParts.length - 1]
						// 解码URI组件，处理特殊字符
						fileName = decodeURIComponent(fileName)
					} catch (e) {
						// 如果提取失败，使用URL的一部分
						fileName = fileUrl.substring(0, 20) + '...'
					}
				}

				// 如果是URL字符串且包含fz参数，则解析文件大小
				if (typeof fileUrl === 'string') {
					const match = fileUrl.match(/[?&]fz=(\d+)/)
					if (match && match[1]) {
						// fz参数现在直接表示字节大小，不需要单位转换
						fileSize = parseInt(match[1], 10)
					}
				}

				return {
					id: X_COMMON_UTILS.generateUUID(),
					valid: true,
					name: fileName,
					size: fileSize,
					type: file.type || '',
					preview: X_FILE_UTILS.isImageExtension(fileUrl) ? fileUrl : null,
					status: 'success',
					progress: 100,
					message: '',
					remoteUrl: fileUrl,
					originalUrl: fileUrl, // 保存原始URL，以便需要时展示
					maxSize: props.maxSize,
				}
			})

			// 对于远程URL，仅处理没有文件大小的文件
			const filesToFetchSize = selectedFiles.value.filter((file) => file.size === 0 && file.remoteUrl).map((file) => ({ file, url: file.remoteUrl }))

			if (filesToFetchSize.length > 0) {
				// 批量获取文件大小
				const sizes = await fetchFileSizesBatch(
					filesToFetchSize.map((item) => item.url),
					{ maxConcurrent: 3 },
				)

				// 更新文件大小
				filesToFetchSize.forEach((item, index) => {
					item.file.size = sizes[index] || 0
				})
			}
		} else {
			selectedFiles.value = []
		}
	},
	{ immediate: true },
)

watch(
	() => props.name,
	(nv) => {
		selectedFiles.value.map((item, key) => {
			item.name = Array.isArray(nv) ? nv?.[key] : nv
		})
	},
)

async function emitModelValue() {
	isInternalUpdate.value = true
	const files = selectedFiles.value.map((file) => file.remoteUrl)
	emit('update:modelValue', props.multiple ? files : files[0])
	const name = props.multiple ? selectedFiles.value?.map((item) => item.name) : selectedFiles.value[0]?.name
	emit('update:name', name)
	formContext?.validateField?.(formItemProp, 'blur')
	// isInternalUpdate.value = false
}

// 新增消息状态
const message = reactive({
	show: true,
	status: '', // 'info' | 'success' | 'error'
	message: '',
})

// 显示消息的方法
const showMessage = (status, content, duration = 3000) => {
	message.status = status
	message.message = content
	message.show = true
	/*	if (duration > 0) {
		setTimeout(() => {
			message.show = false
		}, duration)
	}*/
}

function closeMessage() {
	message.show = false
}

// 处理拖拽事件
const handleDragOver = () => {
	isDragging.value = true
}
const handleDragLeave = () => {
	isDragging.value = false
}

const handleDrop = (e) => {
	isDragging.value = false
	handleFiles(e.dataTransfer.files)
}

// 处理文件选择
const handleFileSelect = (e) => {
	handleFiles(e.target.files)
	e.target.value = '' // 允许重复选择相同文件
}

const handleFiles = async (files) => {
	if (props.disabled) {
		showMessage('error', '上传功能已禁用', 2000)
		return
	}
	const maxSizeBytes = props.maxSize * 1024 * 1024

	// 数量校验
	if (!props.multiple && files.length > 1) {
		showMessage('error', '单次只能上传一个文件')
		return
	}

	// 等待所有文件处理完成
	const processedResults = await Promise.allSettled(
		Array.from(files).map(async (file) => {
			const truncatedName = X_FILE_UTILS.getTruncatedName(file.name)
			// 大小校验
			if (file.size > maxSizeBytes) {
				return {
					valid: false,
					message: `${truncatedName} 超过大小限制（最大${props.maxSize}MB）`,
					name: file.name, // 添加文件名用于错误展示
				}
			}

			// 文件类型校验
			if (props.accept && !isFileTypeAllowed(file, props.accept)) {
				return {
					valid: false,
					message: `${truncatedName} 类型不支持，请上传${formattedAccept.value}`,
					name: file.name,
				}
			}

			// 构造文件对象
			const _file = {
				id: X_COMMON_UTILS.generateUUID(),
				valid: true,
				raw: file,
				name: file.name,
				size: file.size,
				type: file.type,
				preview: null,
				status: 'pending',
				progress: 0,
				message: '',
				maxSize: props.maxSize,
			}

			// 生成预览
			try {
				if (file.type.startsWith('image/')) {
					_file.preview = await readFileAsDataURL(file)
				}
			} catch (error) {
				console.error('生成预览失败:', error)
			}

			return _file
		}),
	)

	// 处理验证结果
	const invalidFiles = processedResults.filter((result) => result.status === 'fulfilled' && !result.value.valid).map((result) => result.value)
	const validFiles = processedResults.filter((result) => result.status === 'fulfilled' && result.value.valid).map((result) => result.value)
	closeMessage()
	// 显示无效文件提示
	if (invalidFiles.length > 0) {
		const errorMessage = invalidFiles.map((f) => f.message || f.name).join(', ')
		showMessage('error', `文件不符合要求：${errorMessage}`)
	}

	// 更新选中文件列表
	if (props.multiple) {
		selectedFiles.value = [...selectedFiles.value, ...validFiles]
	} else {
		selectedFiles.value = validFiles.slice(0, 1)
	}
	// 自动上传逻辑
	if (props.autoUpload && validFiles.length > 0) {
		startUpload()
	}
}

// 读取文件为DataURL
// 修改后的 readFileAsDataURL 方法，增加错误处理
const readFileAsDataURL = (file) => {
	return new Promise((resolve) => {
		const reader = new FileReader()
		reader.onload = () => resolve(reader.result)
		reader.onerror = () => resolve(null) // 读取失败返回 null
		reader.readAsDataURL(file)
	})
}

// 修改后的 removeFile 方法
const removeFile = (index) => {
	const file = selectedFiles.value[index]
	selectedFiles.value.splice(index, 1)
	emitModelValue()
}

// 修改后的 clearFiles 方法
const clearFiles = () => {
	if (selectedFiles.value.length === 0) return
	showMessage('info', `已清空${selectedFiles.value.length}个文件`, 2000)
	selectedFiles.value = []
	emit('success', [])
}

// 触发文件选择
const triggerFileInput = () => {
	if (!props.disabled) fileInput.value.click()
}

// 格式文件大小
const formatFileSize = X_FILE_UTILS.formatFileSize

// 上传状态计算属性
const uploadStatus = computed(() => {
	if (selectedFiles.value.some((f) => f.status === 'uploading')) return 'uploading'
	if (selectedFiles.value.every((f) => f.status === 'success')) return 'success'
	return 'pending'
})

const totalProgress = computed(() => {
	const total = selectedFiles.value.reduce((sum, f) => sum + f.progress, 0)
	return Math.round(total / selectedFiles.value.length)
})

const completedCount = computed(() => {
	return selectedFiles.value.filter((f) => f.status === 'success').length
})

// 开始上传
async function startUpload() {
	const filesToUpload = selectedFiles.value.filter((file) => file.status !== 'success')

	if (!filesToUpload.length) {
		showMessage('info', '没有需要上传的文件')
		return
	}

	// 重置所有文件状态
	selectedFiles.value.forEach((file) => {
		if (file.status !== 'success') {
			file.status = 'pending'
			file.progress = 0
			file.message = ''
		}
	})

	try {
		// 使用 Promise.allSettled 替代 Promise.all
		const uploadResults = await Promise.allSettled(
			selectedFiles.value.map(async (file) => {
				if (file.status === 'success') return { file, success: true }
				try {
					file.status = 'uploading'
					await uploadFile(file)
					file.status = 'success'
					return { file, success: true }
				} catch (error) {
					file.status = 'error'
					file.message = error.message || '上传失败'
					return { file, success: false, error }
				}
			}),
		)

		// 统计成功和失败的数量
		const successCount = uploadResults.filter((result) => result.value?.success).length
		const errorCount = uploadResults.filter((result) => !result.value?.success).length
		emitModelValue()
		// 根据结果显示提示
		if (errorCount === 0) {
			showMessage('success', `所有文件上传成功！共${successCount}个文件`)
		} else if (successCount === 0) {
			showMessage('error', `所有文件(${errorCount}个)上传失败，请检查后重试`)
		} else {
			showMessage('warning', `上传完成，成功${successCount}个，失败${errorCount}个`)
		}

		// 触发事件
		if (successCount > 0) {
			const successFiles = selectedFiles.value.filter((f) => f.status === 'success')
			console.log('successFiles', successFiles)
			emit('success', successFiles)
		}
		if (errorCount > 0) {
			const errorFiles = selectedFiles.value.filter((f) => f.status === 'error')
			emit('error', errorFiles)
		}
	} catch (error) {
		showMessage('error', '上传过程中发生未知错误')
		emit('error', error)
	}
}

// 模拟文件上传
const uploadFile = X_FILE_UTILS.upload

// 获取远程文件大小的函数
const fetchFileSize = async (url) => {
	try {
		const controller = new AbortController()
		// 设置3秒超时
		const timeoutId = setTimeout(() => controller.abort(), 3000)

		const response = await fetch(url, {
			method: 'HEAD',
			signal: controller.signal,
			// 不发送cookies等凭证，减少请求体积
			credentials: 'omit',
		})

		clearTimeout(timeoutId)

		if (!response.ok) {
			throw new Error('Network response was not ok')
		}
		const contentLength = response.headers.get('content-length')
		if (contentLength) {
			return parseInt(contentLength, 10)
		}
		return 0
	} catch (error) {
		console.error('获取文件大小失败:', error)
		return 0
	}
}

// 文件大小缓存
const fileSizeCache = {}

// 批量获取文件大小，带并发控制
const fetchFileSizesBatch = async (urls, options = { maxConcurrent: 3 }) => {
	const { maxConcurrent } = options
	const results = []
	const pendingUrls = [...urls]

	// 首先检查缓存
	const cachedResults = urls.map((url) => fileSizeCache[url] || null)
	if (cachedResults.every((size) => size !== null)) {
		return cachedResults
	}

	// 创建并发任务组
	const createTask = async (index) => {
		while (pendingUrls.length > 0) {
			const url = pendingUrls.shift()
			// 如果已经有缓存，直接使用缓存
			if (fileSizeCache[url] !== undefined) {
				results[urls.indexOf(url)] = fileSizeCache[url]
				continue
			}

			try {
				const size = await fetchFileSize(url)
				results[urls.indexOf(url)] = size
				// 缓存结果
				fileSizeCache[url] = size
			} catch (e) {
				results[urls.indexOf(url)] = 0
			}
		}
	}

	// 启动并发任务
	const tasks = []
	const concurrentTasks = Math.min(maxConcurrent, urls.length)
	for (let i = 0; i < concurrentTasks; i++) {
		tasks.push(createTask(i))
	}

	await Promise.all(tasks)
	return results
}

// 检查文件类型是否符合 accept 限制
const isFileTypeAllowed = (file, accept) => {
	if (!accept) return true

	// 分割 accept 字符串成数组
	const acceptedTypes = accept.split(',').map((type) => type.trim().toLowerCase())

	// 如果接受所有文件
	if (acceptedTypes.includes('*/*')) return true

	const fileName = file.name.toLowerCase()
	const fileType = file.type.toLowerCase()

	// 获取文件扩展名（带点号和不带点号两种格式）
	const fileExt = '.' + fileName.split('.').pop()
	const fileExtWithoutDot = fileName.split('.').pop()

	return acceptedTypes.some((type) => {
		// 检查文件扩展名 - 直接匹配 .xxx
		if (type.startsWith('.')) {
			return fileExt === type
		}

		// 检查不带点号的扩展名
		if (!type.includes('/')) {
			return fileExtWithoutDot === type
		}

		// 检查 MIME 类型通配符 (例如 image/*)
		if (type.endsWith('/*')) {
			const mainType = type.split('/')[0]
			return fileType.startsWith(mainType + '/')
		}

		// 检查具体 MIME 类型
		return fileType === type
	})
}

// 扩展名和MIME类型映射表
const FILE_EXTENSION_MAP = {
	// 图片
	'.jpg': 'image/jpeg',
	'.jpeg': 'image/jpeg',
	'.png': 'image/png',
	'.gif': 'image/gif',
	'.webp': 'image/webp',
	'.svg': 'image/svg+xml',
	// 文档
	'.pdf': 'application/pdf',
	'.doc': 'application/msword',
	'.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
	'.xls': 'application/vnd.ms-excel',
	'.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
	'.ppt': 'application/vnd.ms-powerpoint',
	'.pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
	// 视频
	'.mp4': 'video/mp4',
	'.webm': 'video/webm',
	'.ogg': 'video/ogg',
	// 音频
	'.mp3': 'audio/mpeg',
	'.wav': 'audio/wav',
	'.flac': 'audio/flac',
	// 压缩文件
	'.zip': 'application/zip',
	'.rar': 'application/x-rar-compressed',
	'.7z': 'application/x-7z-compressed',
	// 文本
	'.txt': 'text/plain',
	'.html': 'text/html',
	'.css': 'text/css',
	'.js': 'text/javascript',
	'.json': 'application/json',
	'.xml': 'application/xml',
}

// 用于显示的更友好的类型名称
const FILE_TYPE_NAMES = {
	'image/*': '图片文件',
	'image/jpeg': 'JPEG图片',
	'image/png': 'PNG图片',
	'image/gif': 'GIF图片',
	'image/webp': 'WebP图片',
	'image/svg+xml': 'SVG图片',
	'video/*': '视频文件',
	'video/mp4': 'MP4视频',
	'video/webm': 'WebM视频',
	'video/ogg': 'OGG视频',
	'audio/*': '音频文件',
	'audio/mpeg': 'MP3音频',
	'audio/wav': 'WAV音频',
	'audio/flac': 'FLAC音频',
	'application/pdf': 'PDF文件',
	'application/msword': 'Word文档',
	'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'Word文档',
	'application/vnd.ms-excel': 'Excel文件',
	'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'Excel文件',
	'application/vnd.ms-powerpoint': 'PowerPoint文件',
	'application/vnd.openxmlformats-officedocument.presentationml.presentation': 'PowerPoint文件',
	'application/zip': 'ZIP压缩文件',
	'application/x-rar-compressed': 'RAR压缩文件',
	'application/x-7z-compressed': '7Z压缩文件',
	'text/plain': '文本文件',
	'text/html': 'HTML文件',
	'text/css': 'CSS文件',
	'text/javascript': 'JavaScript文件',
	'application/json': 'JSON文件',
	'application/xml': 'XML文件',
}

// 格式化接受的文件类型
const formattedAccept = computed(() => {
	if (!props.accept) return '任意类型'

	// 如果完全匹配某个 MIME 类型
	if (FILE_TYPE_NAMES[props.accept]) {
		return FILE_TYPE_NAMES[props.accept]
	}

	// 处理多个 MIME 类型
	const types = props.accept.split(',').map((type) => type.trim())
	const formattedTypes = types.map((type) => {
		// 如果是 MIME 类型
		if (type.includes('/')) {
			return FILE_TYPE_NAMES[type] || type
		}
		// 如果是文件扩展名
		if (type.startsWith('.')) {
			const mimeName = FILE_TYPE_NAMES[FILE_EXTENSION_MAP[type]] || type.replace('.', '').toUpperCase() + '文件'
			return mimeName
		}
		return type
	})

	return formattedTypes.join('、')
})

// 定义 Accept 类型，提供代码提示
type MimeType =
	// 图片类型
	| 'image/*'
	| 'image/jpeg'
	| 'image/png'
	| 'image/gif'
	| 'image/webp'
	| 'image/svg+xml'
	// 视频类型
	| 'video/*'
	| 'video/mp4'
	| 'video/webm'
	| 'video/ogg'
	// 音频类型
	| 'audio/*'
	| 'audio/mpeg'
	| 'audio/ogg'
	| 'audio/wav'
	// 文档类型
	| 'application/pdf'
	| 'application/msword'
	| 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
	| 'application/vnd.ms-excel'
	| 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
	| 'application/vnd.ms-powerpoint'
	| 'application/vnd.openxmlformats-officedocument.presentationml.presentation'
	// 压缩文件
	| 'application/zip'
	| 'application/x-rar-compressed'
	// 文本类型
	| 'text/plain'
	| 'text/html'
	| 'text/css'
	| 'text/javascript'
	| 'application/json'
	| 'application/xml'
	// 通配符
	| '*/*'

type FileExtension =
	// 图片扩展名
	| '.jpg'
	| '.jpeg'
	| '.png'
	| '.gif'
	| '.webp'
	| '.svg'
	// 视频扩展名
	| '.mp4'
	| '.webm'
	| '.ogg'
	| '.avi'
	| '.mov'
	// 音频扩展名
	| '.mp3'
	| '.wav'
	| '.ogg'
	| '.flac'
	// 文档扩展名
	| '.pdf'
	| '.doc'
	| '.docx'
	| '.xls'
	| '.xlsx'
	| '.ppt'
	| '.pptx'
	// 压缩文件扩展名
	| '.zip'
	| '.rar'
	| '.7z'
	// 文本文件扩展名
	| '.txt'
	| '.html'
	| '.css'
	| '.js'
	| '.json'
	| '.xml'

// Accept 可以是单个 MimeType 或 FileExtension，也可以是它们的组合（逗号分隔的字符串）
type Accept = MimeType | FileExtension | string

// 文件状态类型
type FileStatus = 'pending' | 'uploading' | 'success' | 'error'

// 上传文件类型
interface UploadFile {
	id: string
	valid: boolean
	raw?: File
	name: string
	size: number
	type: string
	preview: string | null
	status: FileStatus
	progress: number
	message: string
	remoteUrl?: string
	originalUrl?: string
}
</script>

<style>
/* 加载动画 */
.spin {
	animation: spin 1s linear infinite;
}

@keyframes spin {
	from {
		transform: rotate(0deg);
	}
	to {
		transform: rotate(360deg);
	}
}
</style>
