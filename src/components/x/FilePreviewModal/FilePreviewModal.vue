<script setup lang="ts">
const visible = defineModel({ default: false })
defineProps({
	fileUrl: {
		type: String,
		required: true,
	},
	fileName: {
		type: String,
		default: '',
	},
})
function open() {
	visible.value = true
}

function close() {
	visible.value = false
}

defineExpose({ open, close })

onMounted(() => {})
</script>

<template>
	<x-modal v-model="visible" :title="fileName || '文件预览'">
		<XFilePreview :file-url="fileUrl" :file-name="fileName"></XFilePreview>
	</x-modal>
</template>

<style scoped></style>
