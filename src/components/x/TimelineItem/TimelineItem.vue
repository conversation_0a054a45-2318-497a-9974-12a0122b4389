<template>
	<div class="timeline-item" :class="itemClass" :style="{ marginLeft: `calc(${timeline.props.leftWidth} + ${timeline.props.gap})` }">
		<div class="timeline-item-content">
			<!-- 序号 -->
			<div
				ref="numberRef"
				class="timeline-item-number"
				:class="numberStyle"
				:style="{
					left: `calc(-${timeline.props.leftWidth} - ${timeline.props.gap} + ${timeline.props.leftWidth} / 2)`,
					transform: 'translateX(-50%)',
				}"
			>
				{{ itemNumber }}
			</div>

			<!-- 线条 -->
			<div
				v-if="!isLast"
				ref="lineRef"
				class="timeline-item-line"
				:class="lineStyle"
				:style="{
					left: `calc(-${timeline.props.leftWidth} - ${timeline.props.gap} + ${timeline.props.leftWidth} / 2)`,
					transform: 'translateX(-50%)',
					top: lineTop,
					// 确保线条不会覆盖序号
					zIndex: 1,
				}"
			></div>

			<!-- 内容 -->
			<div ref="bodyRef" class="timeline-item-body">
				<slot></slot>
			</div>
		</div>
	</div>
</template>

<script setup>
const props = defineProps({
	// 单个item的序号样式
	numberClass: {
		type: String,
		default: '',
	},
	// 单个item的线条样式
	lineClass: {
		type: String,
		default: '',
	},
	// 额外的item容器样式
	itemClass: {
		type: String,
		default: '',
	},
})

const timeline = inject('timeline')
const lineRef = ref(null)
const bodyRef = ref(null)
const numberRef = ref(null)
const itemId = ref(Symbol())
const lineTop = ref('1.5rem')

const itemNumber = computed(() => {
	const index = timeline.items.value.findIndex((item) => item.id === itemId.value)
	const totalItems = timeline.items.value.length

	if (timeline.props.reverse) {
		// 倒序：最后一个item显示1（或0），第一个item显示最大值
		return timeline.props.startFromOne ? totalItems - index : totalItems - index - 1
	} else {
		// 正序：第一个item显示1（或0），最后一个item显示最大值
		return timeline.props.startFromOne ? index + 1 : index
	}
})

const isLast = computed(() => {
	const index = timeline.items.value.findIndex((item) => item.id === itemId.value)
	return index === timeline.items.value.length - 1
})

const numberStyle = computed(() => {
	return props.numberClass || timeline.props.numberClass
})

const lineStyle = computed(() => {
	return props.lineClass || timeline.props.lineClass
})

// 自适应线条高度和位置
const updateLineHeight = () => {
	if (lineRef.value && bodyRef.value && numberRef.value) {
		const bodyHeight = bodyRef.value.offsetHeight
		const numberHeight = numberRef.value.offsetHeight
		const numberLineGap = parseFloat(timeline.props.numberLineGap.replace('rem', '')) * 16 // 转换rem为px

		// 线条高度 = 内容高度 - 序号高度 - 序号与线条间距
		// 确保线条不会超出内容区域
		const lineHeight = Math.max(0, bodyHeight - numberHeight - numberLineGap)
		lineRef.value.style.height = `${lineHeight}px`
	}
}

const updateLinePosition = () => {
	if (numberRef.value) {
		const numberHeight = numberRef.value.offsetHeight
		const numberLineGap = timeline.props.numberLineGap
		// 线条从序号底部开始，避免穿过序号
		lineTop.value = `calc(${numberHeight}px + ${numberLineGap})`
	}
}

onMounted(() => {
	timeline.registerItem({
		id: itemId.value,
	})

	nextTick(() => {
		updateLineHeight()
		updateLinePosition()
	})

	// 监听内容变化，更新线条高度和位置
	if (bodyRef.value) {
		const resizeObserver = new ResizeObserver(() => {
			updateLineHeight()
			updateLinePosition()
		})
		resizeObserver.observe(bodyRef.value)

		onBeforeUnmount(() => {
			resizeObserver.disconnect()
		})
	}
})

onBeforeUnmount(() => {
	timeline.unregisterItem({
		id: itemId.value,
	})
})
</script>

<style scoped>
.timeline-item {
	position: relative;
	padding-bottom: 1rem;
	/* marginLeft 通过style动态设置 */
}

.timeline-item-content {
	position: relative;
}

.timeline-item-number {
	position: absolute;
	top: 0;
	z-index: 10;
	/* left 和 transform 通过style动态设置，实现居中 */
}

.timeline-item-line {
	position: absolute;
	z-index: 1;
	/* left、top 和 transform 通过style动态设置，实现居中 */
}

.timeline-item-body {
	position: relative;
	z-index: 2;
}
</style>
