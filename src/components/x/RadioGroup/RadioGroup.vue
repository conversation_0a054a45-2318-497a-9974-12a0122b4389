<!-- RadioGroup.vue -->
<template>
	<div class="radio-group flex flex-wrap gap-x-sm">
		<slot>
			<XRadio
				v-for="option in _options"
				:key="option.value"
				:value="option.value"
				:label="option.label"
				:disabled="option.disabled || disabled"
				:badge="getBadge(option[valueKey])"
			/>
		</slot>
	</div>
</template>

<script setup lang="ts">
const props = defineProps({
	options: { type: Array, default: () => [] },
	disabled: { type: Boolean, default: false },
	labelKey: { type: String, default: 'name' },
	valueKey: { type: String, default: 'id' },
	name: { type: String, default: () => `radio-group-${Math.random().toString(36).substring(2, 9)}` },
	badges: { type: Object, default: () => ({}) },
	tagGroupName: {
		type: String,
	},
	tagValueFormat: {
		type: String,
		default: 'default',
	},
})
const _options = ref([])
watchEffect(() => {
	_options.value = formatOptions(props.options)
})
const {
	run: getTagListRun,
	loading: getTagListLoading,
	data: tagList,
} = xUseRequest(BIZ_OPEN_APIS.getTagListStore, `${props.tagGroupName}&format=${props.tagValueFormat}`)
onMounted(async () => {
	if (props.tagGroupName) {
		await getTagListRun()
	}
	_options.value = formatOptions(props.tagGroupName ? tagList.value?.tagList : props.options)
})
function formatOptions(list: any[]) {
	return list?.map((item) => {
		item.label = item[props.labelKey]
		item.value = item[props.valueKey]
		return item
	})
}
function getBadge(value: string | number) {
	return props.badges[value] || ''
}
const modelValue = defineModel()

const emit = defineEmits(['change'])
// 表单上下文处理
const formContext = inject<any>('formContext', null)
const { prop: formItemProp, error } = inject<Record<string, any>>('formItemContext', {})
provide('radioGroup', {
	modelValue,
	name: props.name,
	onChange: (value) => {
		formContext?.validateField?.(formItemProp, 'change')
		emit('change', value)
	},
})
</script>
