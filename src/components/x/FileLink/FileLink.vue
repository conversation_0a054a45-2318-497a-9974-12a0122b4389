<script setup lang="ts">
const props = defineProps({
	name: {
		type: String,
		default: '',
	},
	url: {
		type: String,
		required: true,
	},
})
// 获取显示用的文件名
const displayFileName = computed(() => {
	if (props.name) return props.name
	// 如果没有文件名，尝试从URL中提取
	const urlWithoutParams = props.url?.includes('?') ? props.url.split('?')[0] : props.url
	return decodeURIComponent(urlWithoutParams)
})
</script>

<template>
	<div class="min-w-0 flex flex-grow cursor-pointer items-center text-link text-xs" :title="url" @click="xfilepreview.preview(url, name)">
		<div class="wfull flex cursor-pointer items-center overflow-hidden">
			<!-- 分离URL主体、文件后缀和查询参数 -->
			<span class="min-w-1rem truncate">{{ X_FILE_UTILS.getUrlWithoutExtension(displayFileName) }}</span>
			<span v-if="X_FILE_UTILS.getUrlExtension(displayFileName)" class="shrink-0 whitespace-nowrap">
				{{ '.' + X_FILE_UTILS.getUrlExtension(displayFileName) }}
			</span>
		</div>
	</div>
</template>

<style scoped></style>
