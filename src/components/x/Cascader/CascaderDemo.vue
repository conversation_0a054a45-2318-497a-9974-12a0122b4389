<template>
	<div class="cascader-demo p-md h900px overflow-y-auto">
		<h1 class="font-bold text-xl mb-lg">级联选择器 (Cascader) 测试 Demo</h1>

		<!-- 基础用法 -->
		<section class="mb-lg">
			<h2 class="font-bold text-lg mb-sm">基础用法</h2>
			<div class="gap-md flex flex-wrap">
				<div class="w-300px">
					<p class="mb-xs">默认单选模式</p>
					<Cascader v-model="singleValue" leaf-only :data="options" placeholder="请选择" />
					<p class="color-gray text-sm mt-xs">选中值: {{ singleValue }}</p>
				</div>

				<div class="w-300px">
					<p class="mb-xs">带初始值的单选</p>
					<Cascader v-model="singleWithInitialValue" expand-selected :data="options" placeholder="请选择" />
					<p class="color-gray text-sm mt-xs">选中值: {{ singleWithInitialValue }}</p>
				</div>

				<div class="w-300px">
					<p class="mb-xs">可清空</p>
					<Cascader v-model="clearableValue" :data="options" placeholder="请选择" clearable />
					<p class="color-gray text-sm mt-xs">选中值: {{ clearableValue }}</p>
				</div>
			</div>
		</section>

		<section class="mb-lg">
			<h2 class="font-bold text-lg mb-sm">禁用状态</h2>
			<div class="gap-md flex flex-wrap">
				<div class="w-300px">
					<p class="mb-xs">整体禁用</p>
					<Cascader v-model="disabledValue" :data="options" placeholder="禁用状态" disabled />
				</div>

				<div class="w-300px">
					<p class="mb-xs">带初始值的禁用</p>
					<Cascader v-model="disabledWithValueValue" :data="options" placeholder="禁用状态" disabled />
					<p class="color-gray text-sm mt-xs">选中值: {{ disabledWithValueValue }}</p>
				</div>

				<div class="w-300px">
					<p class="mb-xs">部分选项禁用</p>
					<Cascader v-model="partialDisabledValue" :data="optionsWithDisabled" placeholder="请选择" />
				</div>
			</div>
		</section>

		<!-- 多选模式 -->
		<section class="mb-lg">
			<h2 class="font-bold text-lg mb-sm">多选模式</h2>
			<div class="gap-md flex flex-wrap">
				<div class="w-300px">
					<p class="mb-xs">基础多选</p>
					<Cascader v-model="multipleValue" :data="options" placeholder="请选择" multiple />
					<p class="color-gray text-sm mt-xs">选中值: {{ multipleValue }}</p>
				</div>

				<div class="w-300px">
					<p class="mb-xs">带初始值的多选</p>
					<Cascader v-model="multipleWithInitialValue" :data="options" placeholder="请选择" multiple />
					<p class="color-gray text-sm mt-xs">选中值: {{ multipleWithInitialValue }}</p>
				</div>

				<div class="w-300px">
					<p class="mb-xs">多选 + 可清空</p>
					<Cascader v-model="multipleClearableValue" :data="options" placeholder="请选择" multiple clearable />
					<p class="color-gray text-sm mt-xs">选中值: {{ multipleClearableValue }}</p>
				</div>
			</div>
		</section>

		<!-- 可搜索 -->
		<section class="mb-lg">
			<h2 class="font-bold text-lg mb-sm">可搜索</h2>
			<div class="gap-md flex flex-wrap">
				<div class="w-300px">
					<p class="mb-xs">单选 + 可搜索</p>
					<Cascader v-model="filterableValue" :data="options" placeholder="请输入关键字搜索" filterable />
					<p class="color-gray text-sm mt-xs">选中值: {{ filterableValue }}</p>
				</div>

				<div class="w-300px">
					<p class="mb-xs">单选 + 可搜索 + 仅叶子节点</p>
					<Cascader v-model="filterableLeafValue" :data="options" placeholder="请输入关键字搜索" filterable leaf-only />
					<p class="color-gray text-sm mt-xs">选中值: {{ filterableLeafValue }}</p>
				</div>

				<div class="w-300px">
					<p class="mb-xs">多选 + 可搜索</p>
					<Cascader v-model="multipleFilterableValue" :data="options" placeholder="请输入关键字搜索" multiple filterable />
					<p class="color-gray text-sm mt-xs">选中值: {{ multipleFilterableValue }}</p>
				</div>
			</div>
		</section>

		<!-- 选择模式 -->
		<section class="mb-lg">
			<h2 class="font-bold text-lg mb-sm">选择模式</h2>
			<div class="gap-md flex flex-wrap">
				<div class="w-300px">
					<p class="mb-xs">仅叶子节点可选</p>
					<Cascader v-model="leafOnlyValue" :data="options" placeholder="请选择" leaf-only />
					<p class="color-gray text-sm mt-xs">选中值: {{ leafOnlyValue }}</p>
				</div>

				<div class="w-300px">
					<p class="mb-xs">带初始值的仅叶子节点</p>
					<Cascader v-model="leafOnlyWithInitialValue" :data="options" placeholder="请选择" leaf-only />
					<p class="color-gray text-sm mt-xs">选中值: {{ leafOnlyWithInitialValue }}</p>
				</div>

				<div class="w-300px">
					<p class="mb-xs">多选 + 仅叶子节点可选</p>
					<Cascader v-model="multipleLeafOnlyValue" :data="options" placeholder="请选择" multiple leaf-only />
					<p class="color-gray text-sm mt-xs">选中值: {{ multipleLeafOnlyValue }}</p>
				</div>
			</div>
		</section>

		<!-- 严格模式 -->
		<section class="mb-lg">
			<h2 class="font-bold text-lg mb-sm">严格模式</h2>
			<div class="gap-md flex flex-wrap">
				<div class="w-300px">
					<p class="mb-xs">多选 + 严格模式 (父子节点选择不关联)</p>
					<Cascader v-model="strictlyValue" :data="options" placeholder="请选择" multiple check-strictly />
					<p class="color-gray text-sm mt-xs">选中值: {{ strictlyValue }}</p>
				</div>

				<div class="w-300px">
					<p class="mb-xs">带初始值的严格模式</p>
					<Cascader v-model="strictlyWithInitialValue" :data="options" placeholder="请选择" multiple check-strictly />
					<p class="color-gray text-sm mt-xs">选中值: {{ strictlyWithInitialValue }}</p>
				</div>

				<div class="w-300px">
					<p class="mb-xs">多选 + 严格模式 + 仅叶子节点</p>
					<Cascader v-model="strictlyLeafValue" :data="options" placeholder="请选择" multiple check-strictly leaf-only />
					<p class="color-gray text-sm mt-xs">选中值: {{ strictlyLeafValue }}</p>
				</div>
			</div>
		</section>

		<!-- 显示模式 -->
		<section class="mb-lg">
			<h2 class="font-bold text-lg mb-sm">显示模式</h2>
			<div class="gap-md flex flex-wrap">
				<div class="w-300px">
					<p class="mb-xs">显示完整路径 (默认)</p>
					<Cascader v-model="pathDisplayValue" :data="options" placeholder="请选择" display-mode="path" />
					<p class="color-gray text-sm mt-xs">选中值: {{ pathDisplayValue }}</p>
				</div>

				<div class="w-300px">
					<p class="mb-xs">带初始值的路径显示</p>
					<Cascader v-model="pathDisplayWithInitialValue" :data="options" placeholder="请选择" display-mode="path" />
					<p class="color-gray text-sm mt-xs">选中值: {{ pathDisplayWithInitialValue }}</p>
				</div>

				<div class="w-300px">
					<p class="mb-xs">仅显示选中节点</p>
					<Cascader v-model="nodeDisplayValue" :data="options" placeholder="请选择" display-mode="node" />
					<p class="color-gray text-sm mt-xs">选中值: {{ nodeDisplayValue }}</p>
				</div>
			</div>
		</section>

		<!-- 默认展开 -->
		<section class="mb-lg">
			<h2 class="font-bold text-lg mb-sm">默认展开</h2>
			<div class="gap-md flex flex-wrap">
				<div class="w-300px">
					<p class="mb-xs">默认展开指定节点</p>
					<Cascader v-model="expandedValue" :data="options" placeholder="请选择" :default-expanded="[['zhinan', 'shejiyuanze']]" expand-on-init />
					<p class="color-gray text-sm mt-xs">选中值: {{ expandedValue }}</p>
				</div>

				<div class="w-300px">
					<p class="mb-xs">默认展开 + 初始值</p>
					<Cascader
						v-model="expandedWithInitialValue"
						:data="options"
						placeholder="请选择"
						:default-expanded="[['zhinan', 'shejiyuanze']]"
						expand-on-init
					/>
					<p class="color-gray text-sm mt-xs">选中值: {{ expandedWithInitialValue }}</p>
				</div>
			</div>
		</section>

		<!-- 动态加载 -->
		<section class="mb-lg">
			<h2 class="font-bold text-lg mb-sm">动态加载</h2>
			<div class="gap-md flex flex-wrap">
				<div class="w-300px">
					<p class="mb-xs">点击节点动态加载子节点</p>
					<Cascader v-model="lazyValue" :data="lazyOptions" placeholder="请选择" :lazy-load="lazyLoadData" />
					<p class="color-gray text-sm mt-xs">选中值: {{ lazyValue }}</p>
				</div>

				<div class="w-300px">
					<p class="mb-xs">懒加载 + 多选</p>
					<Cascader v-model="lazyMultipleValue" :data="lazyOptions" placeholder="请选择" :lazy-load="lazyLoadData" multiple />
					<p class="color-gray text-sm mt-xs">选中值: {{ lazyMultipleValue }}</p>
				</div>
			</div>
		</section>

		<!-- 虚拟滚动 -->
		<section class="mb-lg">
			<h2 class="font-bold text-lg mb-sm">大数据量测试</h2>
			<div class="gap-md flex flex-wrap">
				<div class="w-300px">
					<p class="mb-xs">大数据量</p>
					<Cascader v-model="largeDataValue" :data="largeDataOptions" placeholder="请选择" />
					<p class="color-gray text-sm mt-xs">选中值: {{ largeDataValue }}</p>
				</div>

				<div class="w-300px">
					<p class="mb-xs">大数据量 + 多选</p>
					<Cascader v-model="largeDataMultipleValue" :data="largeDataOptions" placeholder="请选择" multiple />
					<p class="color-gray text-sm mt-xs">选中值: {{ largeDataMultipleValue }}</p>
				</div>

				<div class="w-300px">
					<p class="mb-xs">大数据量 + 可搜索</p>
					<Cascader v-model="largeDataSearchValue" :data="largeDataOptions" placeholder="请选择" filterable />
					<p class="color-gray text-sm mt-xs">选中值: {{ largeDataSearchValue }}</p>
				</div>
			</div>
		</section>

		<!-- 组合功能测试 -->
		<section class="mb-lg">
			<h2 class="font-bold text-lg mb-sm">组合功能测试</h2>
			<div class="gap-md flex flex-wrap">
				<div class="w-300px">
					<p class="mb-xs">多选 + 可搜索 + 仅叶子节点</p>
					<Cascader v-model="combinedValue1" :data="options" placeholder="请选择" multiple filterable leaf-only />
					<p class="color-gray text-sm mt-xs">选中值: {{ combinedValue1 }}</p>
				</div>

				<div class="w-300px">
					<p class="mb-xs">多选 + 严格模式 + 可搜索</p>
					<Cascader v-model="combinedValue2" :data="options" placeholder="请选择" multiple check-strictly filterable />
					<p class="color-gray text-sm mt-xs">选中值: {{ combinedValue2 }}</p>
				</div>

				<div class="w-300px">
					<p class="mb-xs">多选 + 可搜索 + 大数据量</p>
					<Cascader v-model="combinedValue3" :data="largeDataOptions" placeholder="请选择" multiple filterable />
					<p class="color-gray text-sm mt-xs">选中值: {{ combinedValue3 }}</p>
				</div>
			</div>
		</section>
	</div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import Cascader from '@/components/x/Cascader/Cascader.vue'

// 基础数据
const options = [
	{
		value: 'zhinan',
		label: '指南',
		children: [
			{
				value: 'shejiyuanze',
				label: '设计原则',
				children: [
					{
						value: 'yizhi',
						label: '一致',
					},
					{
						value: 'fankui',
						label: '反馈',
					},
					{
						value: 'xiaolv',
						label: '效率',
					},
					{
						value: 'kekong',
						label: '可控',
					},
				],
			},
			{
				value: 'daohang',
				label: '导航',
			},
		],
	},
	{
		value: 'zujian',
		label: '组件',
		children: [
			{
				value: 'basic',
				label: '基础',
				children: [
					{
						value: 'layout',
						label: '布局',
					},
					{
						value: 'color',
						label: '颜色',
					},
					{
						value: 'typography',
						label: '字体',
					},
					{
						value: 'icon',
						label: '图标',
					},
					{
						value: 'button',
						label: '按钮',
					},
				],
			},
			{
				value: 'form',
				label: '表单',
				children: [
					{
						value: 'radio',
						label: '单选框',
					},
					{
						value: 'checkbox',
						label: '多选框',
					},
					{
						value: 'input',
						label: '输入框',
					},
					{
						value: 'select',
						label: '选择器',
					},
					{
						value: 'cascader',
						label: '级联选择器',
					},
					{
						value: 'switch',
						label: '开关',
					},
					{
						value: 'slider',
						label: '滑块',
					},
					{
						value: 'timepicker',
						label: '时间选择器',
					},
					{
						value: 'datepicker',
						label: '日期选择器',
					},
					{
						value: 'datetimepicker',
						label: '日期时间选择器',
					},
					{
						value: 'upload',
						label: '上传',
					},
					{
						value: 'rate',
						label: '评分',
					},
					{
						value: 'form',
						label: '表单',
					},
				],
			},
			{
				value: 'data',
				label: '数据',
				children: [
					{
						value: 'table',
						label: '表格',
					},
					{
						value: 'tag',
						label: '标签',
					},
					{
						value: 'progress',
						label: '进度条',
					},
					{
						value: 'tree',
						label: '树形控件',
					},
					{
						value: 'pagination',
						label: '分页',
					},
					{
						value: 'badge',
						label: '标记',
					},
				],
			},
			{
				value: 'notice',
				label: '通知',
				children: [
					{
						value: 'alert',
						label: '警告',
					},
					{
						value: 'loading',
						label: '加载',
					},
					{
						value: 'message',
						label: '消息提示',
					},
					{
						value: 'messagebox',
						label: '弹框',
					},
					{
						value: 'notification',
						label: '通知',
					},
				],
			},
			{
				value: 'navigation',
				label: '导航',
				children: [
					{
						value: 'menu',
						label: '导航菜单',
					},
					{
						value: 'tabs',
						label: '标签页',
					},
					{
						value: 'breadcrumb',
						label: '面包屑',
					},
					{
						value: 'dropdown',
						label: '下拉菜单',
					},
					{
						value: 'steps',
						label: '步骤条',
					},
				],
			},
		],
	},
	{
		value: 'ziyuan',
		label: '资源',
		children: [
			{
				value: 'axure',
				label: 'Axure 组件',
			},
			{
				value: 'sketch',
				label: 'Sketch 模板',
			},
			{
				value: 'jiaohu',
				label: '组件交互文档',
			},
		],
	},
]

const optionsWithDisabled = ref([...options.map((item) => JSON.parse(JSON.stringify(item)))])
// 禁用部分选项
optionsWithDisabled.value[0].children[0].disabled = true
optionsWithDisabled.value[1].children[1].children[0].disabled = true

// 懒加载数据
const lazyOptions = [
	{
		value: 'province1',
		label: '省份1',
		hasChildren: true,
	},
	{
		value: 'province2',
		label: '省份2',
		hasChildren: true,
	},
	{
		value: 'province3',
		label: '省份3',
		hasChildren: true,
	},
]

// 生成大数据量选项
const generateLargeData = () => {
	const result = []
	for (let i = 0; i < 50; i++) {
		const provinces = {
			value: `province${i}`,
			label: `省份${i}`,
			children: [],
		}

		for (let j = 0; j < 20; j++) {
			const cities = {
				value: `province${i}-city${j}`,
				label: `城市${j}`,
				children: [],
			}

			for (let k = 0; k < 15; k++) {
				cities.children.push({
					value: `province${i}-city${j}-district${k}`,
					label: `区县${k}`,
				})
			}

			provinces.children.push(cities)
		}

		result.push(provinces)
	}

	return result
}

// 大数据量选项
const largeDataOptions = ref([])

// 懒加载函数
const lazyLoadData = (node, resolve) => {
	setTimeout(() => {
		const { value, level } = node

		if (level === 0) {
			// 加载城市
			resolve([
				{
					value: `${value}_city1`,
					label: '城市1',
					hasChildren: true,
				},
				{
					value: `${value}_city2`,
					label: '城市2',
					hasChildren: true,
				},
				{
					value: `${value}_city3`,
					label: '城市3',
					hasChildren: true,
				},
			])
		} else if (level === 1) {
			// 加载区县
			resolve([
				{
					value: `${value}_district1`,
					label: '区县1',
					hasChildren: false,
				},
				{
					value: `${value}_district2`,
					label: '区县2',
					hasChildren: false,
				},
				{
					value: `${value}_district3`,
					label: '区县3',
					hasChildren: false,
				},
			])
		}
	}, 500)
}

// 基础用法
const singleValue = ref('')
const singleWithInitialValue = ref('progress')
const clearableValue = ref('zujian')

// 不同尺寸
const sizeSmall = ref('')
const sizeMedium = ref('')
const sizeLarge = ref('')

// 禁用状态
const disabledValue = ref('')
const disabledWithValueValue = ref('ziyuan')
const partialDisabledValue = ref('')

// 多选模式
const multipleValue = ref([])
const multipleWithInitialValue = ref(['zhinan', 'zujian'])
const multipleClearableValue = ref(['ziyuan'])

// 可搜索
const filterableValue = ref('')
const filterableLeafValue = ref('yizhi')
const multipleFilterableValue = ref(['fankui', 'xiaolv'])

// 选择模式
const leafOnlyValue = ref('')
const leafOnlyWithInitialValue = ref('kekong')
const multipleLeafOnlyValue = ref(['cexiangdaohang'])

// 严格模式
const strictlyValue = ref([])
const strictlyWithInitialValue = ref(['zhinan', 'shejiyuanze'])
const strictlyLeafValue = ref(['yizhi'])

// 显示模式
const pathDisplayValue = ref('')
const pathDisplayWithInitialValue = ref('fankui')
const nodeDisplayValue = ref('ziyuan')

// 默认展开
const expandedValue = ref('')
const expandedWithInitialValue = ref('yizhi')

// 动态加载
const lazyValue = ref('')
const lazyMultipleValue = ref([])

// 大数据量测试
const largeDataValue = ref('')
const largeDataMultipleValue = ref([])
const largeDataSearchValue = ref('')

// 组合功能测试
const combinedValue1 = ref(['yizhi', 'fankui'])
const combinedValue2 = ref(['zhinan', 'shejiyuanze', 'yizhi'])
const combinedValue3 = ref([])

// 初始化大数据
onMounted(() => {
	largeDataOptions.value = generateLargeData()
})
</script>

<style scoped>
.cascader-demo {
	font-family: Arial, sans-serif;
}

.w-300px {
	width: 300px;
}

section {
	border-bottom: 1px solid #eee;
	padding-bottom: 20px;
}

.color-gray {
	color: #666;
}
</style>
