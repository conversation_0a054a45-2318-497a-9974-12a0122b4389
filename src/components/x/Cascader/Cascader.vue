<!-- Cascader.vue -->
<template>
	<x-popup ref="popupRef" :only-panel="onlyPanel">
		<!-- 下拉输入框 -->
		<template #default="{ isOpen }">
			<slot :display-value="displayValue">
				<div
					class="wfull flex flex-wrap cursor-text items-center gap-1 rounded text-sm py-xxs px-xxs-1.5"
					:class="[isOpen ? 'border-primary' : 'border-inputBorder', { '!border-danger': error }, $attrs.class]"
				>
					<!-- 多选已选内容 -->
					<template v-if="multiple && Array.isArray(displayValue) && displayValue.length > 0">
						<span v-for="tag in displayValue" :key="tag" class="flex shrink-0 items-center rounded bg-page px-xxs">
							<span class="text-xs">{{ tag.label }}</span>
							<span class="cursor-pointer ml-xxs" @click.stop="removeSelectedNode(tag)">×</span>
						</span>
					</template>

					<!-- 搜索框/显示 -->
					<x-input
						ref="inputRef"
						v-model="searchKeyword"
						class="flex-1 !bg-transparent !p-0 !outline-none !border-none"
						custom-class="!placeholder-content"
						:placeholder="multiple ? (displayValue.length ? '' : placeholder) : displayValue || placeholder"
						:readonly="!filterable"
						@input="debounceFilter"
						@keydown.backspace="handleBackspace"
					/>

					<!-- 清除按钮 -->
					<div v-if="showClearBtn" class="min-w-1rem">
						<XIconsCloseLine class="ml-auto cursor-pointer text-inputPlaceholder hover:text-inputText" @click.stop="handleClear" />
					</div>
				</div>
			</slot>
		</template>
		<!-- 级联面板结构 -->
		<template #content>
			<div class="max-h-30rem overflow-y-auto scroll-smooth rounded bg-white shadow-lg mt-xxs">
				<div class="flex rounded bg-white">
					<!-- 加载状态 -->
					<div v-if="loading" class="w-full text-center color-aid p-sm">
						<span v-loading="true"></span>
						加载中...
					</div>
					<!-- 无数据状态 -->
					<div v-else-if="!filteredData.length && !searchResults.length" class="w-full text-center color-aid p-sm">
						<span v-if="searchKeyword">无匹配结果</span>
						<span v-else>无数据</span>
					</div>
					<!-- 搜索结果面板 -->
					<div v-else-if="searchKeyword && searchResults.length" class="flex overflow-hidden rounded border-border">
						<!-- 搜索结果面板 -->
						<div class="h-20rem min-w-15rem overflow-y-auto">
							<div class="bg-red color-aid p-xxs p-xxs-xs border-b-border">搜索结果</div>
							<x-cascader-node
								v-for="node in searchResults"
								:key="node.value"
								:node="node"
								:leaf-only="leafOnly"
								:multiple="multiple"
								:check-strictly="checkStrictly"
								:model-value="modelValue"
								:search-keyword="searchKeyword"
							></x-cascader-node>
						</div>

						<!-- 级联面板 -->
						<div v-if="showCascaderPanels" class="flex overflow-hidden">
							<!-- 第一级面板 -->
							<div class="h-20rem min-w-18rem overflow-y-auto border-r-border">
								<x-cascader-node
									v-for="node in filteredData"
									:key="node.value"
									:node="node"
									:leaf-only="leafOnly"
									:multiple="multiple"
									:check-strictly="checkStrictly"
									:model-value="modelValue"
									:search-keyword="searchKeyword"
								></x-cascader-node>
							</div>

							<!-- 动态生成后续级别的面板 -->
							<div v-for="(level, index) in expandedPanels" :key="index" class="border-r-border">
								<x-cascader-node
									v-for="node in level"
									:key="node.value"
									:node="node"
									:leaf-only="leafOnly"
									:multiple="multiple"
									:check-strictly="checkStrictly"
									:model-value="modelValue"
									:search-keyword="searchKeyword"
								></x-cascader-node>
							</div>
						</div>
					</div>
					<!-- 级联面板 -->
					<div v-else class="flex">
						<!-- 第一级面板 -->
						<div class="border-r-border">
							<x-cascader-node
								v-for="node in filteredData"
								:key="node.value"
								:node="node"
								:leaf-only="leafOnly"
								:multiple="multiple"
								:check-strictly="checkStrictly"
								:model-value="modelValue"
								:search-keyword="searchKeyword"
							></x-cascader-node>
						</div>

						<!-- 动态生成后续级别的面板 -->
						<div v-for="(level, index) in expandedPanels" :key="index" class="border-r-border">
							<x-cascader-node
								v-for="node in level"
								:key="node.value"
								:node="node"
								:leaf-only="leafOnly"
								:multiple="multiple"
								:check-strictly="checkStrictly"
								:model-value="modelValue"
								:search-keyword="searchKeyword"
							></x-cascader-node>
						</div>
					</div>
				</div>
			</div>
		</template>
	</x-popup>
</template>

<script setup lang="ts">
const props = defineProps({
	data: {
		type: Array,
		default: () => [],
	},
	dataLoadFunc: {
		type: Function,
		default: null,
	},
	modelValue: {
		type: [String, Number, Object, Array],
		default: () => null,
	},
	placeholder: {
		type: String,
		default: '请选择',
	},
	multiple: {
		type: Boolean,
		default: false,
	},
	checkStrictly: {
		type: Boolean,
		default: false,
	},
	filterable: {
		type: Boolean,
		default: false,
	},
	loading: {
		type: Boolean,
		default: false,
	},
	loadData: {
		type: Function,
		default: null,
	},
	expandSelected: {
		type: Boolean,
		default: false,
	},
	leafOnly: {
		type: Boolean,
		default: false,
	},
	// 新增：懒加载重试次数
	lazyLoadRetries: {
		type: Number,
		default: 3,
	},
	// 新增：懒加载重试延迟（毫秒）
	lazyLoadRetryDelay: {
		type: Number,
		default: 1000,
	},
	// 新增：用于构建路径的节点属性
	pathNodeKey: {
		type: String,
		default: 'path',
	},
	labelKey: {
		type: String,
		default: 'name',
	},
	valueKey: {
		type: String,
		default: 'id',
	},
	// 新增：是否在搜索时显示级联面板
	showCascaderWithSearch: {
		type: Boolean,
		default: true,
	},
	// 选择后关闭
	closeOnSelect: {
		type: Boolean,
		default: true,
	},
	// 新增：是否为扁平数据结构
	treeable: {
		type: Boolean,
		default: true,
	},
	// 新增：扁平数据结构中的父节点ID字段
	parentIdKey: {
		type: String,
		default: 'parentId',
	},
	// 新增：是否可清空
	clearable: {
		type: Boolean,
		default: true,
	},
	onlyPanel: {
		type: Boolean,
		default: false,
	},
})
const emit = defineEmits(['node-click', 'node-check', 'node-expand', 'update:modelValue', 'change', 'lazy-load-error', 'clear'])
// 表单上下文处理
const formContext = inject<any>('formContext', null)
const { prop: formItemProp, error } = inject<Record<string, any>>('formItemContext', {})
const popupRef = ref()
function closePopup() {
	popupRef.value?.close()
}
// 状态
const searchKeyword = ref('')
const selectedKeys = ref([])
const expandedNodes = ref([]) // 当前激活的节点
const expandedPanels = ref([]) // 当前显示的面板数据
const indeterminateKeys = ref([])
const treeData = ref([])
let filterTimer = null

// 加载中的节点集合
const loadingKeys = ref(new Set())

// 内部选中状态，用于在没有modelValue时维护选中状态
const internalSelectedValue = ref(props.modelValue !== null && props.modelValue !== undefined ? props.modelValue : props.multiple ? [] : null)
// 显示值
const displayValue = computed(() => {
	if (props.multiple) {
		// 多选模式
		if (Array.isArray(internalSelectedValue.value)) {
			// 查找所有选中的节点
			return internalSelectedValue.value?.map(findNodeByValue)?.filter(Boolean) // 过滤掉未找到的节点
		}
	} else {
		// 单选模式
		const node = findNodeByValue(internalSelectedValue.value)
		return node?._path_label || node?._path_name || props.placeholder
	}
	return props.placeholder
})
// 节点映射表，用于快速查找节点
const nodeMap = ref(new Map())

// 路径映射表，只存储节点的value
const pathMap = ref(new Map())

// 按不同属性构建的路径映射表，只存储节点的value
const customPathMaps = ref({})

// 是否显示级联面板（搜索时）
const showCascaderPanels = computed(() => {
	return props.showCascaderWithSearch && filteredData.value.length > 0
})

// 是否显示清除按钮
const showClearBtn = computed(() => {
	return (
		props.clearable &&
		((props.multiple && Array.isArray(internalSelectedValue.value) && internalSelectedValue.value.length > 0) ||
			(!props.multiple && internalSelectedValue.value !== null && internalSelectedValue.value !== undefined))
	)
})

// 处理退格键
const handleBackspace = (e) => {
	if (props.multiple && Array.isArray(internalSelectedValue.value) && internalSelectedValue.value.length > 0 && searchKeyword.value === '') {
		// 删除最后一个选项
		const lastIndex = displayValue.value.length - 1
		if (lastIndex >= 0) {
			removeSelectedNode(displayValue.value[lastIndex])
		}
	}
}

// 删除节点
function removeSelectedNode(node) {
	internalSelectedValue.value = internalSelectedValue.value.filter((v) => v !== node.value)
	emit('update:modelValue', internalSelectedValue.value)
	emit('change', internalSelectedValue.value) // 触发change事件
	formContext?.validateField?.(formItemProp, 'change')
}

// 清空选择
function handleClear() {
	const oldValue = JSON.parse(JSON.stringify(internalSelectedValue.value))

	if (props.multiple) {
		internalSelectedValue.value = []
	} else {
		internalSelectedValue.value = null
	}

	selectedKeys.value = []
	emit('update:modelValue', internalSelectedValue.value)
	emit('change', internalSelectedValue.value) // 触发change事件
	emit('clear', oldValue) // 触发clear事件，传递之前的值
	formContext?.validateField?.(formItemProp, 'change')
}

// 搜索防抖
const debounceFilter = () => {
	if (filterTimer) clearTimeout(filterTimer)
	filterTimer = setTimeout(() => {
		// 搜索时自动展开包含匹配项的节点
		if (searchKeyword.value) {
			autoExpandMatchedNodes(searchKeyword.value.toLowerCase())
		}
	}, 300)
}

// 自动展开包含匹配项的节点
const autoExpandMatchedNodes = (keyword) => {
	// 存储需要展开的节点值
	const nodesToExpand = new Set()
	// 存储匹配的节点，用于更新面板
	let matchedNode = null

	// 遍历所有节点
	nodeMap.value.forEach((node) => {
		// 检查当前节点是否匹配
		const isMatch = node.label.toLowerCase().includes(keyword)

		// 如果当前节点匹配，收集其所有父节点路径
		if (isMatch) {
			// 获取父节点路径并添加到需要展开的集合中
			const parentValues = getParentNodeValues(node.value)
			parentValues.forEach((parentValue) => nodesToExpand.add(parentValue))

			// 保存最后一个匹配的节点，用于更新面板
			matchedNode = node
		}
	})

	// 如果找到匹配的节点，更新激活节点
	if (matchedNode) {
		updateExpandedPanels(matchedNode)
	}
}

// 更新激活面板
const updateExpandedPanels = (node) => {
	if (!node) return

	// 清空当前激活节点和面板
	expandedNodes.value = []
	expandedPanels.value = []

	// 获取当前节点的所有父节点
	const parentValues = getParentNodeValues(node.value)
	const parentNodes = parentValues.map((value) => nodeMap.value.get(value)).filter(Boolean)

	// 按层级排序父节点
	parentNodes.sort((a, b) => a.level - b.level)

	// 当前是叶子的话，不用展开状态
	expandedNodes.value = hasChildren(node) ? [...parentNodes, node] : [...parentNodes]

	// 更新面板数据
	updatePanelsData()
}

// 更新面板数据
const updatePanelsData = () => {
	expandedPanels.value = []

	// 为每个激活节点创建一个面板
	expandedNodes.value.forEach((node, index) => {
		if (node.children && node.children.length) {
			expandedPanels.value[index] = node.children
		}
	})
}

// 计算属性
// 过滤后的数据
const filteredData = computed(() => {
	if (!searchKeyword.value) return treeData.value

	const keyword = searchKeyword.value.toLowerCase()

	// 递归过滤函数
	const filterNode = (nodes) => {
		return nodes.filter((node) => {
			// 检查当前节点是否匹配
			const isMatch = node.label.toLowerCase().includes(keyword)

			// 如果有子节点，递归过滤
			if (node.children && node.children.length) {
				const filteredChildren = filterNode(node.children)

				// 如果有匹配的子节点，保留父节点并更新子节点
				if (filteredChildren.length) {
					// 创建新对象避免修改原始数据
					return {
						...node,
						children: filteredChildren,
					}
				}
			}

			// 如果当前节点匹配，直接返回
			return isMatch
		})
	}

	// 回退到使用JSON.parse(JSON.stringify())进行深拷贝
	try {
		return filterNode(JSON.parse(JSON.stringify(treeData.value)))
	} catch (error) {
		console.error('Cascader: 过滤数据失败', error)
		return []
	}
})

// 搜索结果列表（扁平化的匹配节点）
const searchResults = computed(() => {
	if (!searchKeyword.value) return []

	const keyword = searchKeyword.value.toLowerCase()
	const results = []

	// 遍历所有节点，找出匹配的节点
	nodeMap.value.forEach((node) => {
		if (node.label.toLowerCase().includes(keyword)) {
			// 创建节点的副本，避免修改原始数据
			const nodeCopy = { ...node }

			// 添加完整路径信息，方便显示
			if (node.level > 0) {
				const parentNodes = getParentNodeValues(node.value)
					.map((value) => nodeMap.value.get(value))
					.filter(Boolean)
					.sort((a, b) => a.level - b.level)

				// 构建完整路径标签
				const pathLabels = parentNodes.map((parent) => parent.label)
				nodeCopy.pathLabel = pathLabels.join(' / ')

				// 添加完整路径，包括当前节点
				nodeCopy.fullPath = [...pathLabels, node.label].join(' / ')
			} else {
				// 根节点
				nodeCopy.fullPath = node.label
			}

			results.push(nodeCopy)
		}
	})

	// 按路径排序，使相关节点在一起显示
	results.sort((a, b) => a.fullPath.localeCompare(b.fullPath))

	return results
})

// 方法
const initTreeData = (_data = props.data) => {
	// 清空节点映射表
	nodeMap.value.clear()

	// 清空路径映射表
	pathMap.value.clear()

	// 清空自定义路径映射表
	customPathMaps.value = {}

	// 清空加载状态
	loadingKeys.value.clear()

	// 清空激活节点和面板
	expandedNodes.value = []
	expandedPanels.value = []

	try {
		let data = JSON.parse(JSON.stringify(_data))
		console.log('props---', props.treeable)
		// 如果是扁平数据结构，先转换为树形结构
		if (props.treeable && Array.isArray(data)) {
			data = convertFlatToTree(data)
		}

		// 创建一个集合来跟踪已处理的值
		const processedValues = new Set()
		const duplicateValues = new Set()

		// 预处理数据，检查是否有重复值
		const checkDuplicates = (nodes, level = 0) => {
			nodes.forEach((node) => {
				const value = node?.[props.valueKey] || node?.value
				if (processedValues.has(value)) {
					duplicateValues.add(value)
				} else {
					processedValues.add(value)
				}

				if (node.children && node.children.length) {
					checkDuplicates(node.children, level + 1)
				}
			})
		}

		// 检查重复值
		checkDuplicates(data)

		// 如果有重复值，输出警告
		if (duplicateValues.size > 0) {
			console.warn(`Cascader: 检测到 ${duplicateValues.size} 个重复的节点值，后续相同值的节点将被忽略: `, [...duplicateValues])
		}

		// 重置处理值集合，开始实际处理
		processedValues.clear()
		treeData.value = processNode(data, 0, 0, {}, processedValues)
	} catch (error) {
		console.error('Cascader: 初始化数据失败', error)
		treeData.value = []
	}
}

// 新增：将扁平数据转换为树形结构
const convertFlatToTree = (flatData) => {
	// 创建节点映射，用于快速查找
	const nodeMap = new Map()

	// 第一次遍历：创建所有节点的映射
	flatData.forEach((item) => {
		const value = item?.[props.valueKey] || item?.value
		if (value !== undefined) {
			// 创建节点的副本，避免修改原始数据
			nodeMap.set(value, { ...item, children: [] })
		}
	})

	// 根节点数组
	const rootNodes = []

	// 第二次遍历：构建树结构
	flatData.forEach((item) => {
		const value = item?.[props.valueKey] || item?.value
		const parentId = item?.[props.parentIdKey]

		if (value !== undefined) {
			const currentNode = nodeMap.get(value)

			// 如果有父节点，将当前节点添加到父节点的children中
			if (parentId !== undefined && parentId !== null && parentId !== 0 && nodeMap.has(parentId)) {
				const parentNode = nodeMap.get(parentId)
				parentNode.children.push(currentNode)
			} else {
				// 没有父节点或父节点不存在，作为根节点
				rootNodes.push(currentNode)
			}
		}
	})

	return rootNodes
}

// 处理节点数据的辅助函数
const processNode = (nodes, parentId = 0, level = 0, parentPaths = {}, processedValues = new Set()) => {
	return nodes
		.filter((node) => node.visible !== false)
		.map((node) => {
			const label = node?.[props.labelKey] || node?.label
			const value = node?.[props.valueKey] || node?.value

			// 创建处理后的节点
			const processedNode = {
				...node,
				parentId,
				level,
				label,
				value,
			}

			// 构建默认路径（使用props.pathNodeKey）
			const defaultKey = props.pathNodeKey
			const defaultPathValue = node[defaultKey] || node.value
			const defaultPath = parentPaths[defaultKey] ? `${parentPaths[defaultKey]}/${defaultPathValue}` : `${defaultPathValue}`

			// 存储默认路径，只存储节点的value
			pathMap.value.set(defaultPath, processedNode.value)

			// 将默认路径存储到节点上
			processedNode[`_path_${defaultKey}`] = defaultPath

			// 构建所有可能的路径
			const nodePaths = { ...parentPaths }

			// 遍历节点的所有属性，为每个属性构建路径
			for (const key in node) {
				if (typeof node[key] === 'string' || typeof node[key] === 'number') {
					const pathValue = node[key]
					const path = parentPaths[key] ? `${parentPaths[key]}/${pathValue}` : `${pathValue}`

					// 更新当前节点的路径，供子节点使用
					nodePaths[key] = path

					// 确保自定义路径映射表存在
					if (!customPathMaps.value[key]) {
						customPathMaps.value[key] = new Map()
					}

					// 将节点的value添加到自定义路径映射表
					customPathMaps.value[key].set(path, processedNode.value)

					// 将路径存储到节点上
					processedNode[`_path_${key}`] = path
				}
			}

			// 检查是否已经存在相同value的节点
			if (!processedValues.has(processedNode.value)) {
				// 将节点添加到映射表
				nodeMap.value.set(processedNode.value, processedNode)
				// 标记该值已被处理
				processedValues.add(processedNode.value)

				// 处理子节点
				if (node.children && node.children.length) {
					processedNode.children = processNode(node.children, processedNode.value, level + 1, nodePaths, processedValues)
				}

				return processedNode
			} else {
				// 如果已存在相同value的节点，则返回null，后续会被过滤掉
				console.warn(`Cascader: 检测到重复的节点值 "${processedNode.value}"，已忽略后续节点`)
				return null
			}
		})
		.filter(Boolean) // 过滤掉null值
}

// 根据值查找节点 - 简化回原始实现
const findNodeByValue = (value) => {
	return nodeMap.value.get(value)
}

const findNodeByFullPath = (fullPath) => {
	return nodeMap.value.get(pathMap.value.get(fullPath))
}

/**
 * 获取节点的所有子节点值（包括深层子节点）
 * 用于级联选择等场景
 * @param {string|number} nodeValue - 节点值
 * @returns {Array} 所有子节点值的数组
 */
const getChildNodeValues = (nodeValue) => {
	const values = []
	const node = nodeMap.value.get(nodeValue)

	if (!node || !node.children) return values

	const collectValues = (children) => {
		children.forEach((child) => {
			values.push(child.value)
			if (child.children && child.children.length) {
				collectValues(child.children)
			}
		})
	}

	collectValues(node.children)
	return values
}

/**
 * 获取节点的所有父节点值
 * 用于确定节点路径等场景
 * @param {string|number} nodeValue - 节点值
 * @returns {Array} 所有父节点值的数组，从近到远排序
 */
const getParentNodeValues = (nodeValue) => {
	const values = []
	let node = nodeMap.value.get(nodeValue)

	// 防止无限循环
	const visitedNodes = new Set()

	while (node && node.parentId) {
		// 检查是否已访问过该节点，防止循环引用
		if (visitedNodes.has(node.parentId)) {
			console.warn('Cascader: 检测到循环引用', node)
			break
		}

		visitedNodes.add(node.parentId)
		values.push(node.parentId)
		node = nodeMap.value.get(node.parentId)
	}
	return values
}

// 处理节点点击
const handleNodeClick = (node, isEmit = true) => {
	if (!node || node.disabled) return

	// 更新激活节点和面板
	updateExpandedPanels(node)

	if (!props.multiple) {
		handleNodeCheck(node, isEmit)
	}

	isEmit && emit('node-click', node)
}

// 判断节点是否有子节点
const hasChildren = (node) => {
	if (!node) return false
	return (node.children && node.children.length > 0) || node.hasChildren
}

// 判断节点是否被选中
const isNodeChecked = (node) => {
	if (!node) return false

	// 然后检查modelValue（外部状态）
	if (!props.multiple) {
		// 比较值而不是唯一键
		return internalSelectedValue.value === node.value
	}

	// 在多选模式下，检查值是否在选中列表中
	return Array.isArray(internalSelectedValue.value) && internalSelectedValue.value.includes(node.value)
}

const isNodeSelected = (node) => selectedKeys.value.includes(node.value)
const isNodeLoading = (node) => {
	if (!node) return false
	return loadingKeys.value.has(node.value)
}
// 判断节点是否为半选状态
const isNodeIndeterminate = (node) => {
	if (!node || !props.multiple) return false
	// 在 leafOnly 模式下，非叶子节点不显示半选状态
	if (props.leafOnly && hasChildren(node)) {
		return false
	}
	return indeterminateKeys.value.includes(node.value)
}

const isNodeExpanded = (node) => {
	if (!node) return false
	return expandedNodes.value.some((n) => n.value === node.value)
}

// 处理节点选中状态变化（多选模式）
const handleNodeCheck = (node, isEmit = true) => {
	if (!node) return
	if (props.leafOnly && hasChildren(node)) return

	// 保存旧值，用于触发change事件
	const oldValue = JSON.parse(JSON.stringify(internalSelectedValue.value))

	// 获取所有选中的节点值
	let checkedValues = props.multiple ? [...(Array.isArray(internalSelectedValue.value) ? internalSelectedValue.value : [])] : null
	// 保留现有的半选状态
	const indeterminateValues = [...indeterminateKeys.value]

	if (props.multiple) {
		const isCurrentlyChecked = isNodeChecked(node)

		// 处理选中/取消选中逻辑
		if (isCurrentlyChecked) {
			// 取消选中
			checkedValues = checkedValues.filter((value) => value !== node.value)
		} else {
			// 选中
			checkedValues.push(node.value)
		}

		if (!props.checkStrictly) {
			// 获取当前节点的所有子节点值
			const childValues = getChildNodeValues(node.value)

			if (isCurrentlyChecked) {
				// 取消选中时，需要同时取消子节点的选中状态
				checkedValues = checkedValues.filter((value) => !childValues.includes(value))
			} else {
				// 选中时，同时选中所有子节点
				updateChildNodeCheckedState(node, checkedValues)
			}

			// 从半选状态中移除当前节点
			const indeterminateIndex = indeterminateValues.indexOf(node.value)
			if (indeterminateIndex !== -1) {
				indeterminateValues.splice(indeterminateIndex, 1)
			}

			// 获取当前节点的所有父节点
			const parentNodes = getParentNodeValues(node.value)
				.map((id) => nodeMap.value.get(id))
				.filter(Boolean)

			// 更新每个父节点的状态
			parentNodes.forEach((parent) => {
				updateSingleParentNodeState(parent, checkedValues, indeterminateValues)
			})
		}

		// 更新内部选中状态
		indeterminateKeys.value = indeterminateValues
	}

	if (props.closeOnSelect) {
		// 单选，没有子节点
		if (!props.multiple && !hasChildren(node)) {
			closePopup()
		}
	}

	// 更新selectedKeys - 在多选模式下保持所有选中节点的路径
	if (props.multiple) {
		// 如果是多选模式，需要保持所有选中节点的路径
		// 遍历所有选中的值，收集它们的路径
		if (Array.isArray(checkedValues) && checkedValues.length > 0) {
			// 创建一个Set来存储所有路径，避免重复
			const allPaths = new Set()

			// 遍历所有选中的节点值
			checkedValues.forEach((value) => {
				// 获取当前节点的所有父节点路径
				const parentPaths = getParentNodeValues(value)
				// 将当前节点值也添加到路径中
				allPaths.add(value)
				// 将所有父节点路径添加到Set中
				parentPaths.forEach((path) => allPaths.add(path))
			})

			// 将Set转换为数组并更新selectedKeys
			selectedKeys.value = [...allPaths]
		} else {
			selectedKeys.value = []
		}
	} else {
		// 单选模式，只保持当前选中节点的路径
		selectedKeys.value = getParentNodeValues(node.value)
	}

	internalSelectedValue.value = props.multiple ? checkedValues : node.value

	if (isEmit) {
		emit('update:modelValue', internalSelectedValue.value)
		emit('node-check', node)

		// 检查值是否发生了变化
		const newValueJson = JSON.stringify(internalSelectedValue.value)
		const oldValueJson = JSON.stringify(oldValue)

		if (newValueJson !== oldValueJson) {
			emit('change', internalSelectedValue.value, oldValue)
		}

		formContext?.validateField?.(formItemProp, 'change')
	}
}

const updateChildNodeCheckedState = (node, checkedValues) => {
	if (!node) return
	// 如果有子节点，递归更新子节点状态
	if (node.children && node.children.length) {
		node.children.forEach((child) => {
			if (!child.disabled) {
				// 避免重复添加
				if (!checkedValues.includes(child.value)) {
					checkedValues.push(child.value)
				}
				updateChildNodeCheckedState(child, checkedValues)
			}
		})
	}
}

// 更新单个父节点的状态
const updateSingleParentNodeState = (parent, checkedValues, indeterminateValues) => {
	if (!parent || !parent.children || !parent.children.length) return

	// 检查子节点状态
	const totalChildren = parent.children.length
	const checkedCount = parent.children.filter((child) => checkedValues.includes(child.value)).length
	const indeterminateCount = parent.children.filter((child) => indeterminateValues.includes(child.value)).length

	if (checkedCount === 0 && indeterminateCount === 0) {
		// 所有子节点都未选中
		// 从选中列表中移除父节点
		const index = checkedValues.indexOf(parent.value)
		if (index !== -1) {
			checkedValues.splice(index, 1)
		}
		// 从半选列表中移除父节点
		const indeterminateIndex = indeterminateValues.indexOf(parent.value)
		if (indeterminateIndex !== -1) {
			indeterminateValues.splice(indeterminateIndex, 1)
		}
	} else if (checkedCount === totalChildren) {
		// 所有子节点都选中
		// 如果不是仅叶子节点可选模式，则添加父节点到选中列表
		if (!props.leafOnly) {
			if (!checkedValues.includes(parent.value)) {
				checkedValues.push(parent.value)
			}
		}
		// 从半选列表中移除父节点
		const indeterminateIndex = indeterminateValues.indexOf(parent.value)
		if (indeterminateIndex !== -1) {
			indeterminateValues.splice(indeterminateIndex, 1)
		}
	} else {
		// 部分子节点选中或半选
		// 从选中列表中移除父节点
		const index = checkedValues.indexOf(parent.value)
		if (index !== -1) {
			checkedValues.splice(index, 1)
		}
		// 添加父节点到半选列表
		if (!indeterminateValues.includes(parent.value)) {
			indeterminateValues.push(parent.value)
		}
	}
}

// 懒加载节点子节点
const loadNodeChildren = (node) => {
	if (!node || !props.loadData || !loadingKeys.value.has(node.value)) return

	// 记录当前重试次数
	node.retryCount = node.retryCount || 0

	try {
		// 调用加载数据函数
		Promise.resolve(props.loadData(node))
			.then((children) => {
				// 处理成功加载的数据
				if (Array.isArray(children) && children.length > 0) {
					// 处理子节点数据
					const parentPaths = {}

					// 构建父节点路径信息
					for (const key in node) {
						if (key.startsWith('_path_')) {
							const pathKey = key.replace('_path_', '')
							parentPaths[pathKey] = node[key]
						}
					}

					// 处理子节点
					const processedChildren = processNode([...children], node.value, node.level + 1, parentPaths)

					// 更新节点的子节点
					node.children = processedChildren
					node.hasChildren = true

					// 清除加载状态
					loadingKeys.value.delete(node.value)
					delete node.retryCount
					delete node.loadError

					// 更新面板数据
					updatePanelsData()
				} else {
					// 没有子节点
					node.children = []
					node.hasChildren = false

					// 清除加载状态
					loadingKeys.value.delete(node.value)
					delete node.retryCount
					delete node.loadError
				}
			})
			.catch((error) => {
				console.error('Cascader: 加载节点子节点失败', error)

				// 增加重试次数
				node.retryCount += 1

				// 如果未超过最大重试次数，则延迟重试
				if (node.retryCount < props.lazyLoadRetries) {
					setTimeout(() => {
						loadNodeChildren(node)
					}, props.lazyLoadRetryDelay)
				} else {
					// 超过最大重试次数，标记为加载失败
					node.loadError = true
					loadingKeys.value.delete(node.value)

					// 发出加载错误事件
					emit('lazy-load-error', node, error)
				}
			})
	} catch (error) {
		console.error('Cascader: 执行懒加载函数失败', error)
		node.loadError = true
		loadingKeys.value.delete(node.value)
		emit('lazy-load-error', node, error)
	}
}

// 监听数据变化
watch(
	() => props.data,
	() => {
		if (!props.dataLoadFunc) {
			initTreeData()
		}
	},
	{ immediate: true, deep: true },
)

onMounted(async () => {
	if (props.dataLoadFunc) {
		const res = await props.dataLoadFunc()
		if (res || res?.list) {
			initTreeData(res || res?.list)
		}
	}
})

// 监听选中值变化，更新节点状态
watch(
	() => props.modelValue,
	(newVal, oldVal) => {
		// 更新内部选中状态
		internalSelectedValue.value = newVal !== null && newVal !== undefined ? newVal : props.multiple ? [] : null

		// 如果没有选中值，直接返回
		if (newVal === null || newVal === undefined || (Array.isArray(newVal) && newVal.length === 0)) {
			selectedKeys.value = []
			return
		}

		if (props.expandSelected) {
			// 如果是单选模式
			if (!props.multiple) {
				const node = findNodeByValue(newVal)
				if (node) {
					// 更新激活节点和面板
					updateExpandedPanels(node)
				}
			} else if (Array.isArray(newVal) && newVal.length > 0) {
				// 多选模式，选择第一个节点展开
				const node = findNodeByValue(newVal[0])
				if (node) {
					// 更新激活节点和面板
					updateExpandedPanels(node)
				}
			}
		}

		// 更新选中状态
		if (props.multiple && Array.isArray(newVal)) {
			// 多选模式下，如果不是严格检查模式，需要更新半选状态
			if (!props.checkStrictly) {
				const indeterminateValues = []

				// 一次性计算所有父节点的半选状态
				const parentNodes = new Set()
				newVal.forEach((value) => {
					getParentNodeValues(value).forEach((parentValue) => {
						parentNodes.add(parentValue)
					})
				})

				// 只处理父节点的半选状态
				parentNodes.forEach((parentValue) => {
					const parentNode = nodeMap.value.get(parentValue)
					if (parentNode && parentNode.children && parentNode.children.length) {
						updateSingleParentNodeState(parentNode, newVal, indeterminateValues)
					}
				})

				// 一次性更新半选状态
				indeterminateKeys.value = indeterminateValues
			}

			// 更新selectedKeys - 保持所有选中节点的路径
			if (Array.isArray(newVal) && newVal.length > 0) {
				// 创建一个Set来存储所有路径，避免重复
				const allPaths = new Set()

				// 遍历所有选中的节点值
				newVal.forEach((value) => {
					// 获取当前节点的所有父节点路径
					const parentPaths = getParentNodeValues(value)
					// 将当前节点值也添加到路径中
					allPaths.add(value)
					// 将所有父节点路径添加到Set中
					parentPaths.forEach((path) => allPaths.add(path))
				})

				// 将Set转换为数组并更新selectedKeys
				selectedKeys.value = [...allPaths]
			} else {
				// 如果没有选中值，清空selectedKeys
				selectedKeys.value = []
			}
		} else {
			// 单选模式
			const node = findNodeByValue(newVal)
			if (node) {
				// 更新选中的父节点路径
				selectedKeys.value = getParentNodeValues(node.value)
			} else {
				// 如果没有找到节点，清空selectedKeys
				selectedKeys.value = []
			}
		}
	},
	{ deep: true, immediate: true },
)

// 组件卸载前清理
onBeforeUnmount(() => {
	// 清除定时器
	if (filterTimer) {
		clearTimeout(filterTimer)
		filterTimer = null
	}

	// 清空加载状态
	loadingKeys.value.clear()
})

// 提供给子组件的数据
provide('cascader', {
	multiple: computed(() => props.multiple),
	checkStrictly: computed(() => props.checkStrictly),
	leafOnly: computed(() => props.leafOnly),
	modelValue: computed(() => internalSelectedValue.value),
	internalSelectedValue: computed(() => internalSelectedValue.value),
	loadingKeys: computed(() => loadingKeys.value),
	hasChildren,
	isNodeSelected,
	isNodeChecked,
	isNodeLoading,
	isNodeExpanded,
	isNodeIndeterminate,
	handleNodeCheck,
	handleNodeClick,
	loadNodeChildren,
	handleClear, // 导出清空方法
})

// 导出公共方法供外部使用
const publicMethods = {
	getChildNodeValues,
	getParentNodeValues,
	findNodeByValue,
	findNodeByFullPath,
	handleClear, // 导出清空方法
}
defineExpose(publicMethods)
</script>

<style scoped></style>
