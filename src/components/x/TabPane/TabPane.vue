<template>
	<div v-if="shouldRender" v-show="isActive" class="tab-pane">
		<slot></slot>
	</div>
</template>

<script setup lang="ts">
const props = defineProps({
	label: {
		type: String,
		required: true,
	},
	value: {
		type: [String, Number],
		required: true,
	},
})

const tabs = inject('tabs')
const isActive = computed(() => {
	const currentValue = unref(tabs.currentValue)
	return currentValue !== undefined && currentValue?.toString() === props.value?.toString()
})

// 只有非懒加载或者曾经激活过的标签页才会被渲染
const hasBeenActivated = ref(false)

watch(
	isActive,
	(val) => {
		if (val) {
			hasBeenActivated.value = true
		}
	},
	{ immediate: true },
)

const shouldRender = computed(() => {
	return !tabs.lazy || hasBeenActivated.value
})

onMounted(() => {
	tabs.registerTab({
		value: props.value,
		label: props.label,
	})
})

onBeforeUnmount(() => {
	tabs.unregisterTab({
		value: props.value,
		label: props.label,
	})
})
</script>
