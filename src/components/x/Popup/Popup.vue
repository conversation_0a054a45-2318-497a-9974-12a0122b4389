<template>
	<div :class="{ hfull: hfull }">
		<div v-if="!onlyPanel" ref="triggerContainer" class="hfull" @mouseleave="handleTriggerLeave">
			<div class="hfull cursor-pointer" v-on="eventHandlers">
				<slot :is-open="isOpen" :open="open" :close="close" />
			</div>
			<teleport to="body">
				<div
					ref="popupRef"
					v-click-outside="handleClickOutside"
					class="fixed z-5 transition-all duration-300"
					:class="[animationClass]"
					:style="popupStyle"
					@mouseenter="handlePopupEnter"
					@mouseleave="handlePopupLeave"
				>
					<XTransition mode="rebound" direction="up" :duration="0.2" @after-leave="cleanupStyles">
						<div v-show="isOpen">
							<slot name="content" :close="close" />
						</div>
					</XTransition>
				</div>
			</teleport>
		</div>
		<slot v-else name="content"></slot>
	</div>
</template>
<script setup lang="ts">
defineOptions({
	inheritAttrs: false,
})

// 定义事件
const emit = defineEmits(['open', 'close'])
// 为全局管理器定义类型
interface PopupManager {
	openPopups: Map<string, () => void>
	// eslint-disable-next-line no-unused-vars
	register: (id: string, closeFunction: () => void) => void
	// eslint-disable-next-line no-unused-vars
	unregister: (id: string) => void
	// eslint-disable-next-line no-unused-vars
	closeAllExcept: (exceptId: string) => void
	lastClickedId: string | null
	// eslint-disable-next-line no-unused-vars
	setLastClicked: (id: string) => void
}

// 判断是否在客户端环境
const isClient = typeof window !== 'undefined'

// 创建一个真正的全局单例对象，确保所有组件实例共享
// 使用window对象存储全局状态，确保所有组件实例共享同一个状态
if (isClient) {
	// 如果全局对象不存在，则创建它
	if (!window.__POPUP_MANAGER__) {
		window.__POPUP_MANAGER__ = {
			// 存储所有打开的popup
			openPopups: new Map(),

			// 注册一个打开的popup
			register(id, closeFunction) {
				this.openPopups.set(id, closeFunction)
			},

			// 注销一个popup
			unregister(id) {
				this.openPopups.delete(id)
			},

			// 关闭除指定id外的所有popup，使用requestAnimationFrame优化
			closeAllExcept(exceptId) {
				// 收集需要关闭的popup
				const toClose: Array<() => void> = []
				this.openPopups.forEach((closeFunction, id) => {
					if (id !== exceptId) {
						toClose.push(closeFunction)
					}
				})

				// 使用requestAnimationFrame批量关闭
				if (toClose.length > 0) {
					requestAnimationFrame(() => {
						toClose.forEach((closeFunction) => {
							try {
								closeFunction()
							} catch (error) {
								console.error('Failed to close popup:', error)
							}
						})
					})
				}
			},

			// 记录最后一次点击的popup ID，用于防止重复处理
			lastClickedId: null,
			setLastClicked(id) {
				this.lastClickedId = id
			},
		}
	}
}

// 获取全局管理器的引用
const getPopupManager = (): PopupManager | undefined => {
	return isClient ? window.__POPUP_MANAGER__ : undefined
}

const props = defineProps({
	hfull: {
		type: Boolean,
		default: true,
	},
	disabled: {
		type: Boolean,
		default: false,
	},
	onlyPanel: {
		type: Boolean,
		default: false,
	},
	placement: {
		type: String as PropType<
			| 'top'
			| 'bottom'
			| 'left'
			| 'right'
			| 'top-start'
			| 'top-end'
			| 'bottom-start'
			| 'bottom-end'
			| 'left-start'
			| 'left-end'
			| 'right-start'
			| 'right-end'
		>,
		default: 'bottom',
	},
	offset: { type: Number, default: 0 },
	viewportPadding: { type: Number, default: 8 },
	trigger: {
		type: String as PropType<'click' | 'hover' | 'contextmenu'>,
		default: 'click',
	},
	// 添加唯一ID
	popupId: { type: String, default: () => `popup-${Date.now()}-${Math.floor(Math.random() * 1000)}` },
	// 添加hover延迟关闭时间
	hoverDelay: { type: Number, default: 200 },
	// 添加边缘检测自动翻转功能
	autoFlip: { type: Boolean, default: true },
})

const isOpen = ref(false)
const triggerContainer = ref<HTMLElement | null>(null)
const popupRef = ref<HTMLElement | null>(null)
const popupStyle = ref({})
// 跟踪鼠标是否在popup内部
const isMouseInPopup = ref(false)
const isMouseInTrigger = ref(false)

// 在组件卸载时清理
onBeforeUnmount(() => {
	try {
		const manager = getPopupManager()
		if (manager) {
			manager.unregister(props.popupId)
		}
		clearHoverTimer()
	} catch (error) {
		console.error('Error during popup cleanup:', error)
	}
})

// 防抖函数
const debounce = <T extends (...args: any[]) => any>(fn: T, delay = 100) => {
	let timeout: number | null = null
	return (...args: Parameters<T>): void => {
		if (timeout !== null) {
			clearTimeout(timeout)
		}
		timeout = window.setTimeout(() => {
			fn(...args)
			timeout = null
		}, delay)
	}
}

const eventHandlers = computed(() => {
	switch (props.trigger) {
		case 'hover':
			return {
				mouseenter: handleTriggerEnter,
				mouseleave: handleTriggerLeave,
			}
		case 'contextmenu':
			return { contextmenu: handleContextMenu }
		case 'click': // click
			return { click: handleClick }
		default:
			// 当trigger为null、undefined或空字符串时，返回空对象
			return {}
	}
})

// Click 处理 - 关键修改点
const handleClick = (e: MouseEvent) => {
	try {
		// 阻止事件冒泡，防止触发document上的点击事件
		e.stopPropagation()

		// 记录当前点击的popup ID
		const manager = getPopupManager()
		if (manager) {
			manager.setLastClicked(props.popupId)
		}

		// 如果当前popup已经打开，则关闭它
		if (isOpen.value) {
			close()
		} else {
			// 否则，先关闭其他popup，再打开当前popup
			if (manager) {
				manager.closeAllExcept(props.popupId)
			}
			open()
		}
	} catch (error) {
		console.error('Error handling click:', error)
	}
}

// 右键菜单处理
const handleContextMenu = (e: MouseEvent) => {
	try {
		e.preventDefault()
		e.stopPropagation()

		// 记录当前点击的popup ID
		const manager = getPopupManager()
		if (manager) {
			manager.setLastClicked(props.popupId)
		}

		// 先关闭其他popup，再打开当前popup
		if (manager) {
			manager.closeAllExcept(props.popupId)
		}
		open()
	} catch (error) {
		console.error('Error handling context menu:', error)
	}
}

// Hover 处理相关函数
let hoverTimer: number | null = null

// 清除hover定时器
const clearHoverTimer = () => {
	if (hoverTimer !== null) {
		clearTimeout(hoverTimer)
		hoverTimer = null
	}
}

// 触发器鼠标进入处理
const handleTriggerEnter = () => {
	if (props.trigger !== 'hover') return
	isMouseInTrigger.value = true
	clearHoverTimer()
	open()
}

// 触发器鼠标离开处理
const handleTriggerLeave = () => {
	if (props.trigger !== 'hover') return
	isMouseInTrigger.value = false

	// 延迟关闭
	scheduleClose()
}

// 弹窗鼠标进入处理
const handlePopupEnter = () => {
	if (props.trigger !== 'hover') return
	isMouseInPopup.value = true
	clearHoverTimer()
}

// 弹窗鼠标离开处理
const handlePopupLeave = () => {
	if (props.trigger !== 'hover') return
	isMouseInPopup.value = false

	// 延迟关闭
	scheduleClose()
}

// 统一的延迟关闭逻辑
const scheduleClose = () => {
	if (!props.trigger || props.trigger !== 'hover') return

	// 只有当鼠标既不在触发器也不在弹窗内时才设置关闭定时器
	if (!isMouseInTrigger.value && !isMouseInPopup.value) {
		clearHoverTimer()
		hoverTimer = window.setTimeout(() => {
			if (!isMouseInTrigger.value && !isMouseInPopup.value) {
				close()
			}
		}, props.hoverDelay)
	}
}

// 修改点击外部处理逻辑
const handleClickOutside = () => {
	try {
		if (!props.trigger || props.trigger === 'hover') return

		// 检查是否点击了另一个popup的触发器
		const manager = getPopupManager()
		if (manager && manager.lastClickedId && manager.lastClickedId !== props.popupId) {
			// 如果点击了另一个popup的触发器，让那个popup处理关闭逻辑
			close()
			return
		}

		// 重置最后点击的ID
		if (manager) {
			manager.lastClickedId = null
		}

		// 正常关闭当前popup
		close()
	} catch (error) {
		console.error('Error handling click outside:', error)
	}
}

// 定位计算逻辑
const calculatePosition = () => {
	try {
		if (!isOpen.value || !triggerContainer.value || !popupRef.value) return

		const triggerRect = triggerContainer.value.getBoundingClientRect()
		const popupRect = popupRef.value.getBoundingClientRect()
		const viewportWidth = window.innerWidth
		const viewportHeight = window.innerHeight

		// 解析原始位置
		const [originalMainPos, subPos = 'start'] = props.placement.split('-')

		// 用于存储应用边缘检测后的实际位置
		let mainPos = originalMainPos

		// 边缘检测逻辑
		if (props.autoFlip) {
			// 上下方向的边缘检测
			if (mainPos === 'top' && triggerRect.top - popupRect.height - props.offset < props.viewportPadding) {
				// 如果上方空间不足，切换到下方
				mainPos = 'bottom'
			} else if (mainPos === 'bottom' && triggerRect.bottom + popupRect.height + props.offset > viewportHeight - props.viewportPadding) {
				// 如果下方空间不足，切换到上方
				mainPos = 'top'
			}

			// 左右方向的边缘检测
			if (mainPos === 'left' && triggerRect.left - popupRect.width - props.offset < props.viewportPadding) {
				// 如果左侧空间不足，切换到右侧
				mainPos = 'right'
			} else if (mainPos === 'right' && triggerRect.right + popupRect.width + props.offset > viewportWidth - props.viewportPadding) {
				// 如果右侧空间不足，切换到左侧
				mainPos = 'left'
			}
		}

		let x = 0,
			y = 0

		// 主定位计算
		switch (mainPos) {
			case 'top':
				y = triggerRect.top - popupRect.height - props.offset
				break
			case 'bottom':
				y = triggerRect.bottom + props.offset
				break
			case 'left':
				x = triggerRect.left - popupRect.width - props.offset
				break
			case 'right':
				x = triggerRect.right + props.offset
				break
		}

		// 子对齐调整
		switch (`${mainPos}-${subPos}`) {
			case 'top-start':
			case 'bottom-start':
				x = triggerRect.left
				break
			case 'top-end':
			case 'bottom-end':
				x = triggerRect.right - popupRect.width
				break
			case 'left-start':
			case 'right-start':
				y = triggerRect.top
				break
			case 'left-end':
			case 'right-end':
				y = triggerRect.bottom - popupRect.height
				break
		}

		// 二次校准：特殊情况处理
		// 当子对齐调整后，内容可能仍然超出视口，进行二次调整
		if (props.autoFlip) {
			// 水平方向二次校准
			if ((mainPos === 'top' || mainPos === 'bottom') && subPos === 'start') {
				// 左对齐但右边超出屏幕
				if (x + popupRect.width > viewportWidth - props.viewportPadding) {
					x = Math.max(props.viewportPadding, triggerRect.right - popupRect.width)
				}
			} else if ((mainPos === 'top' || mainPos === 'bottom') && subPos === 'end') {
				// 右对齐但左边超出屏幕
				if (x < props.viewportPadding) {
					x = Math.min(triggerRect.left, viewportWidth - popupRect.width - props.viewportPadding)
				}
			}

			// 垂直方向二次校准
			if ((mainPos === 'left' || mainPos === 'right') && subPos === 'start') {
				// 顶部对齐但底部超出屏幕
				if (y + popupRect.height > viewportHeight - props.viewportPadding) {
					y = Math.max(props.viewportPadding, triggerRect.bottom - popupRect.height)
				}
			} else if ((mainPos === 'left' || mainPos === 'right') && subPos === 'end') {
				// 底部对齐但顶部超出屏幕
				if (y < props.viewportPadding) {
					y = Math.min(triggerRect.top, viewportHeight - popupRect.height - props.viewportPadding)
				}
			}
		}

		// 视口边界调整 (基础保障)
		if (mainPos === 'top' || mainPos === 'bottom') {
			x = Math.max(x, props.viewportPadding)
			x = Math.min(x, viewportWidth - popupRect.width - props.viewportPadding)
		} else {
			y = Math.max(y, props.viewportPadding)
			y = Math.min(y, viewportHeight - popupRect.height - props.viewportPadding)
		}

		// 更新实际使用的动画方向
		currentPlacement.value = mainPos + (subPos !== 'start' ? `-${subPos}` : '')

		popupStyle.value = {
			top: `${y}px`,
			left: `${x}px`,
			maxWidth: `${viewportWidth - props.viewportPadding * 2}px`,
			maxHeight: `${viewportHeight - props.viewportPadding * 2}px`,
		}
	} catch (error) {
		console.error('Error calculating position:', error)
	}
}

// 跟踪实际使用的位置（可能因为边缘检测而改变）
const currentPlacement = ref(props.placement)

// 增加防抖时间到100ms，优化性能
const debouncedCalculatePosition = debounce(calculatePosition, 100)

// 动画类
const animationClass = computed(() => {
	const [mainPos] = currentPlacement.value.split('-')
	return {
		top: 'animate-slide-down',
		bottom: 'animate-slide-up',
		left: 'animate-slide-right',
		right: 'animate-slide-left',
	}[mainPos]
})

// 操作方法
const open = async () => {
	if (props.disabled) return
	try {
		isOpen.value = true

		// 延迟注册到全局管理器，只有在真正打开时才注册
		await nextTick()
		const manager = getPopupManager()
		if (manager) {
			manager.register(props.popupId, close)
		}

		calculatePosition()

		// 触发open事件
		emit('open')
	} catch (error) {
		console.error('Failed to open popup:', error)
		isOpen.value = false
	}
}

const cleanupStyles = () => {
	try {
		popupStyle.value = {}
	} catch (error) {
		console.error('Error cleaning up styles:', error)
	}
}

const close = () => {
	try {
		isOpen.value = false
		isMouseInPopup.value = false

		// 从全局管理器中注销
		const manager = getPopupManager()
		if (manager) {
			manager.unregister(props.popupId)
		}

		// 触发close事件
		emit('close')
	} catch (error) {
		console.error('Failed to close popup:', error)
	}
}

// 自动更新位置逻辑
let resizeObserver: ResizeObserver | null = null

watchEffect((onCleanup) => {
	try {
		if (isOpen.value) {
			window.addEventListener('resize', debouncedCalculatePosition)
			window.addEventListener('scroll', debouncedCalculatePosition, true)

			// 延迟创建ResizeObserver，只在需要时创建
			if (!resizeObserver) {
				resizeObserver = new ResizeObserver(debouncedCalculatePosition)
			}

			if (triggerContainer.value) resizeObserver.observe(triggerContainer.value)
			if (popupRef.value) resizeObserver.observe(popupRef.value)
		} else if (resizeObserver) {
			resizeObserver.disconnect()
		}

		onCleanup(() => {
			window.removeEventListener('resize', debouncedCalculatePosition)
			window.removeEventListener('scroll', debouncedCalculatePosition, true)
			if (resizeObserver) {
				resizeObserver.disconnect()
			}
			clearHoverTimer()
		})
	} catch (error) {
		console.error('Error setting up position updates:', error)
	}
})

defineExpose({ open, close, isOpen: computed(() => isOpen.value) })
</script>
