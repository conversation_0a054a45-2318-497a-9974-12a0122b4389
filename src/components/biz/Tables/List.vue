<script setup lang="ts">
const tableRef = ref()

defineExpose({
	getTableRef: () => tableRef.value,
})
</script>

<template>
	<div class="flex-1 overflow-hidden">
		<x-table ref="tableRef" ellipsis col-class="p-xxs" v-bind="$attrs">
			<template v-for="(value, key) in $slots" #[key]="slotProps" :key="key">
				<slot :name="key" v-bind="slotProps"></slot>
			</template>
		</x-table>
	</div>
</template>

<style scoped></style>
