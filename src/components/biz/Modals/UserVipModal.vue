<script setup lang="ts">
const visible = defineModel('visible', { type: Boolean, default: false })
const qrCodeUrl = ref('https://example.com/vip-qr-code') // 替换为实际的二维码图片URL
function open() {
	visible.value = true
}
defineExpose({ open })
</script>

<template>
	<x-modal v-model="visible" title="VIP会员">
		<div class="relative rounded-2xl from-white/80 to-white/60 bg-gradient-to-br p-8 shadow backdrop-blur-sm">
			<!-- 装饰背景 -->
			<div class="absolute right-0 top-0 h-32 w-32">
				<div class="absolute right-0 top-0 h-40 w-40 rounded-full bg-yellow-100/30 blur-xl -translate-x-1/2 -translate-y-1/2"></div>
			</div>

			<!-- 限时活动标签 -->
			<div class="absolute left-1/2 top-0 -translate-x-1/2 -translate-y-1/2">
				<div class="rounded-full from-yellow-500 to-yellow-600 bg-gradient-to-r px-4 py-1 text-white font-bold shadow-md text-sm">限时特惠</div>
			</div>

			<!-- 主要内容 -->
			<div class="relative text-center">
				<!-- VIP图标 -->
				<div class="mx-auto mb-6 h-16 w-16 flex items-center justify-center rounded-full from-yellow-500 to-yellow-600 bg-gradient-to-r shadow-lg">
					<i class="i-carbon:star-filled text-white text-3xl"></i>
				</div>

				<!-- 标题 -->
				<h2 class="mb-4 text-gray-800 font-bold text-2xl">尊享VIP特权</h2>

				<!-- 特权列表 -->
				<div class="grid grid-cols-2 mb-8 gap-4">
					<div class="flex items-center gap-2 rounded-xl bg-gray-100 p-3">
						<i class="i-carbon:video text-yellow-600 text-xl"></i>
						<span class="text-gray-700">专属视频课程</span>
					</div>
					<div class="flex items-center gap-2 rounded-xl bg-gray-100 p-3">
						<i class="i-carbon:document-download text-yellow-600 text-xl"></i>
						<span class="text-gray-700">资料下载</span>
					</div>
					<div class="flex items-center gap-2 rounded-xl bg-gray-100 p-3">
						<i class="i-carbon:chat text-yellow-600 text-xl"></i>
						<span class="text-gray-700">AI助教</span>
					</div>
					<div class="flex items-center gap-2 rounded-xl bg-gray-100 p-3">
						<i class="i-carbon:badge text-yellow-600 text-xl"></i>
						<span class="text-gray-700">考试特权</span>
					</div>
				</div>

				<!-- 二维码区域 -->
				<div class="mb-6">
					<p class="mb-4 text-gray-600">扫描下方二维码，咨询开通或续费VIP</p>
					<div class="inline-block rounded-xl bg-white p-4 shadow-lg">
						<x-image class="h-10rem" src="/images/qrcode.jpg"></x-image>
					</div>
				</div>

				<!-- 提示文字 -->
				<p class="text-gray-500 text-sm">开通VIP会员，享受更多专属特权</p>
			</div>
		</div>
	</x-modal>
</template>

<style scoped></style>
