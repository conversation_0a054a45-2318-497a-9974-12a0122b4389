<script setup lang="ts">
defineProps({
	labelPosition: { type: String, default: 'top' },
	labelWidth: { type: String, default: '100%' },
})

const XFormRef = ref()
function submit() {
	XFormRef.value?.submit()
}
function reset() {
	XFormRef.value?.reset()
}
function clearValidate() {
	XFormRef.value?.clearValidate()
}
function validate() {
	XFormRef.value?.validate()
}
defineExpose({ submit, reset, clearValidate, validate })
</script>

<template>
	<x-form ref="XFormRef" :label-position="labelPosition" :label-width="labelWidth">
		<slot></slot>
	</x-form>
</template>

<style scoped></style>
