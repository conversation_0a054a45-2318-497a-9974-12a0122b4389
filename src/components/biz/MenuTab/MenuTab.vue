<script setup lang="ts">
const props = defineProps({
	bg: {
		type: String,
		default: themeColors.primary_30,
	},
	radius: {
		type: String,
		default: '0.45rem',
	},
	data: {
		type: Array,
		default: () => [],
	},
})
const selectedValue = defineModel()
const router = useRouter()
xThemeSetCssVar('--menu-tab-active-color', props.bg)
xThemeSetCssVar('--menu-tab-radius', props.radius)

function handleClose() {
	if (props.data?.length === 1) {
		router.push('/')
	}
}
</script>

<template>
	<div class="hfull flex flex-1 overflow-x-auto overflow-y-hidden text-sm">
		<XTabs
			v-model="selectedValue"
			class="wfull"
			:data="data"
			label-key="label"
			value-key="value"
			arc-tab
			type="card"
			closeable
			contextmenu
			tab-class="h2.5rem px-xl"
			@close="handleClose"
			@change="(item) => item?._path_path && router.push(item._path_path)"
		></XTabs>
	</div>
</template>

<style scoped lang="less"></style>
