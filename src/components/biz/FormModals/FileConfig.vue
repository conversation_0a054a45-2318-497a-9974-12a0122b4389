<template>
	<BizModalsSave v-model="visible" :title="title">
		<div v-loading="computedLoading" class="pb-base">
			<BizFormForm :model="model" :rules="rules" label-width="100px" @submit="handleSubmit">
				<div class="grid grid-cols-2 gap-x-base">
					<XFormItem label="配置名" prop="name">
						<XInput v-model="model.name" placeholder="请输入配置名" @keyup.enter.stop="handleSubmit" />
					</XFormItem>
					<XFormItem label="存储器" prop="storage">
						<XSelect v-model="model.storage" :disabled="model.id" tag-group-name="ADMIN文件存储器" tag-value-format="number" />
					</XFormItem>
					<XFormItem label="备注" prop="remark">
						<XInput v-model="model.remark" placeholder="请输入备注" @keyup.enter.stop="handleSubmit" />
					</XFormItem>
					<XFormItem label="是否主配置" prop="master">
						<XRadioGroup v-model="model.master" tag-group-name="是否" tag-value-format="boolean" />
					</XFormItem>
				</div>
				<div v-if="model.storage == 20">
					<XFormItem label="节点地址" prop="config.endpoint">
						<XInput v-model="model.config.endpoint" placeholder="请输入节点地址" @keyup.enter.stop="handleSubmit" />
					</XFormItem>
					<XFormItem label="存储bucket" prop="config.bucket">
						<XInput v-model="model.config.bucket" placeholder="请输入存储bucket" @keyup.enter.stop="handleSubmit" />
					</XFormItem>
					<XFormItem label="accessKey" prop="config.accessKey">
						<XInput v-model="model.config.accessKey" placeholder="请输入accessKey" @keyup.enter.stop="handleSubmit" />
					</XFormItem>
					<XFormItem label="accessSecret" prop="config.accessSecret">
						<XInput v-model="model.config.accessSecret" placeholder="请输入accessSecret" @keyup.enter.stop="handleSubmit" />
					</XFormItem>
				</div>
				<XFormItem label="自定义域名" prop="config.domain" :required="model.storage == 10">
					<XInput v-model="model.config.domain" placeholder="请输入自定义域名" @keyup.enter.stop="handleSubmit" />
				</XFormItem>
			</BizFormForm>
		</div>
	</BizModalsSave>
</template>
<script setup lang="ts">
const visible = ref(false)
const model = ref({
	id: undefined,
	name: undefined,
	storage: undefined,
	remark: undefined,
	master: undefined,
	config: {},
})
const rules = reactive({
	name: [{ required: true, message: '配置名不能为空' }],
	storage: [{ required: true, message: '存储器不能为空' }],
	master: [{ required: true, message: '是否为主配置不能为空' }],
	config: [{ required: true, message: '存储配置不能为空' }],
	'config.endpoint': [{ required: true, message: '节点地址不能为空' }],
	'config.bucket': [{ required: true, message: '存储bucket不能为空' }],
	'config.accessKey': [{ required: true, message: 'accessKey不能为空' }],
	'config.accessSecret': [{ required: true, message: 'accessSecret不能为空' }],
})
const emit = defineEmits(['success'])
const title = ref('')
let type = ref('')
const id = ref(0)
const detailLoading = ref(false)
let run, loading
const computedLoading = computed(() => loading?.value || detailLoading.value)
const params = ref({})
function open(_type, _id, _params) {
	model.value = { config: {} }
	params.value = _params
	if (_id) {
		getDetail(_id)
	}
	visible.value = true
	type.value = _type
	title.value = _type === 'create' ? '新增' : '编辑'
	id.value = _id
	let { run: _run, loading: _loading } = xUseRequest(BIZ_FileConfig_APIS[_type], model)
	run = _run
	loading = _loading
}

async function getDetail(id) {
	detailLoading.value = true
	const res = await BIZ_FileConfig_APIS.get(id).finally(() => (detailLoading.value = false))
	Object.assign(model.value, res)
}

defineExpose({ open })
async function handleSubmit() {
	await run()
	visible.value = false
	emit('success', model.value)
}
</script>

<style scoped></style>
