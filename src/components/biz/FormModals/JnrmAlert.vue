<template>
	<BizModalsSave v-model="visible" :title="title">
		<div v-loading="computedLoading" class="pb-base">
			<BizFormForm :model="model" :rules="rules" @submit="handleSubmit">
				<div class="gap-x-base grid grid-cols-2">
					<XFormItem label="告警类型" prop="alertType">
						<XSelect :disabled="model.id" v-model="model.alertType" placeholder="请选择告警类型" tag-group-name="JNRM告警类型" />
					</XFormItem>
					<XFormItem label="告警内容" prop="alertContent">
						<XInput :disabled="model.id" v-model="model.alertContent" type="textarea" @keyup.enter.stop="handleSubmit" />
					</XFormItem>
					<XFormItem label="车牌号" prop="licensePlate">
						<XInput :disabled="model.id" v-model="model.licensePlate" placeholder="请输入车牌号" @keyup.enter.stop="handleSubmit" />
					</XFormItem>
					<XFormItem label="告警时间" prop="alertTime">
						<XDateTimePicker :disabled="model.id" v-model="model.alertTime" type="date" placeholder="选择告警时间" />
					</XFormItem>
					<XFormItem label="本系统运单号ID" prop="transportOrderId">
						<XInput :disabled="model.id" v-model="model.transportOrderId" placeholder="请输入本系统运单号ID" @keyup.enter.stop="handleSubmit" />
					</XFormItem>
					<XFormItem label="快程运单号" prop="externalOrderNo">
						<XInput :disabled="model.id" v-model="model.externalOrderNo" placeholder="请输入快程运单号" @keyup.enter.stop="handleSubmit" />
					</XFormItem>
					<XFormItem label="司机名称" prop="driverName">
						<XInput :disabled="model.id" v-model="model.driverName" placeholder="请输入司机名称" @keyup.enter.stop="handleSubmit" />
					</XFormItem>
					<XFormItem label="司机手机号码" prop="driverPhone">
						<XInput :disabled="model.id" v-model="model.driverPhone" placeholder="请输入司机手机号码" @keyup.enter.stop="handleSubmit" />
					</XFormItem>
					<XFormItem label="告警图片URL" prop="alertImageUrl">
						<XInput :disabled="model.id" v-model="model.alertImageUrl" placeholder="请输入告警图片URL" @keyup.enter.stop="handleSubmit" />
					</XFormItem>
					<XFormItem label="告警视频URL" prop="alertVideoUrl">
						<XInput :disabled="model.id" v-model="model.alertVideoUrl" placeholder="请输入告警视频URL" @keyup.enter.stop="handleSubmit" />
					</XFormItem>
					<XFormItem label="处理状态" prop="handleStatus">
						<XRadioGroup v-model="model.handleStatus" tag-group-name="JNRM告警处理状态" tag-value-format="bool" />
					</XFormItem>
				</div>
			</BizFormForm>
		</div>
	</BizModalsSave>
</template>
<script setup lang="ts">
const visible = ref(false)
const model = ref({
	id: undefined,
	alertType: undefined,
	alertContent: undefined,
	vehicleId: undefined,
	licensePlate: undefined,
	handleStatus: undefined,
	alertTime: undefined,
	alertImageUrl: undefined,
	alertVideoUrl: undefined,
	transportOrderId: undefined,
	externalOrderNo: undefined,
	driverName: undefined,
	driverPhone: undefined,
})
const rules = reactive({
	alertType: [{ required: true, message: '告警类型（1：铅封告警，2：偏离轨迹，3：异常停留，4：超时预警）不能为空' }],
	vehicleId: [{ required: true, message: '车辆ID不能为空' }],
	licensePlate: [{ required: true, message: '车牌号不能为空' }],
	handleStatus: [{ required: true, message: '处理状态（0：未处理，1：已处理）不能为空' }],
	alertTime: [{ required: true, message: '告警时间不能为空' }],
})
const emit = defineEmits(['success'])
const title = ref('')
let type = ref('')
const id = ref(0)
const detailLoading = ref(false)
const requestLoading = ref(false)
let run, data
const computedLoading = computed(() => requestLoading?.value || detailLoading.value)
const params = ref({})
function open(_type, _id, _params) {
	model.value = {}
	params.value = _params
	if (_id) {
		getDetail(_id)
	}
	visible.value = true
	type.value = _type
	title.value = _type === 'create' ? '新增' : '编辑'
	id.value = _id
	let { run: _run, loading, data: _data } = xUseRequest(BIZ_JnrmAlert_APIS[_type], model)
	run = _run
	// 监听xUseRequest返回的loading状态，同步到组件级别的loading
	watchEffect(() => {
		requestLoading.value = loading.value
	})
	data = _data
}

async function getDetail(id) {
	detailLoading.value = true
	const res = await BIZ_JnrmAlert_APIS.get(id).finally(() => (detailLoading.value = false))
	Object.assign(model.value, res)
}

defineExpose({ open })
async function handleSubmit() {
	if (!visible.value) return
	await run()
	visible.value = false
	if (data && type.value == 'create') {
		model.value.id = data
	}
	emit('success', model.value)
}
</script>

<style scoped></style>
