<template>
	<BizModalsSave v-model="visible" :title="title">
		<div v-loading="loading" class="pb-base">
			<BizFormForm :model="model" :rules="rules" @submit="handleSubmit">
				<div class="grid grid-cols-1 gap-x-base">
					<XFormItem label="角色名称" prop="">
						{{ row.name }}
					</XFormItem>
					<XFormItem label="角色标识" prop="">
						{{ row.code }}
					</XFormItem>
					<XFormItem label="数据权限" prop="dataScope" required>
						<XSelect
							v-model="model.dataScope"
							:options="[
								{ id: 1, name: '全部数据权限' },
								{ id: 3, name: '本部门数据权限' },
								{ id: 4, name: '本部门级以下数据权限' },
								{ id: 5, name: '仅本人数据权限' },
							]"
						/>
					</XFormItem>
				</div>
			</BizFormForm>
		</div>
	</BizModalsSave>
</template>
<script setup lang="ts">
const visible = ref(false)
const model = ref({
	roleId: null,
	dataScope: null,
})
const menus = ref([])
async function loadMenus() {
	loading.value = true
	const res = await BIZ_Menu_APIS.getSimpleList()
	menus.value = res
	const res2 = await BIZ_Menu_APIS.getRoleMenuList(row.value.id).finally(() => {
		loading.value = false
	})
	model.value.menuIds = res2
}
const rules = reactive({})
const emit = defineEmits(['success'])
const title = ref('')
const row = ref({})
let { run, loading } = xUseRequest(BIZ_Role_APIS.assignRoleDataScope, model)
function open(_row) {
	menus.value = []
	model.value.roleId = _row.id
	model.value.dataScope = _row.dataScope
	row.value = _row
	visible.value = true
}
defineExpose({ open })
async function handleSubmit() {
	await run()
	visible.value = false
	toast.success('操作成功')
	emit('success')
}
</script>

<style scoped></style>
