<template>
	<BizModalsSave v-model="visible" :title="title">
		<div v-loading="computedLoading" class="pb-base">
			<BizFormForm :model="model" :rules="rules" @submit="handleSubmit">
				<div class="grid grid-cols-2 gap-x-base">
					<XFormItem label="用户账号" prop="username" required>
						<XInput v-model="model.username" placeholder="请输入用户账号" />
					</XFormItem>
					<XFormItem v-if="!model.id" label="用户密码" prop="password" required>
						<XInput v-model="model.password" placeholder="请输入用户密码" />
					</XFormItem>

					<XFormItem label="用户昵称" prop="nickname" required>
						<XInput v-model="model.nickname" placeholder="请输入用户昵称" />
					</XFormItem>
					<XFormItem label="用户邮箱" prop="email">
						<XInput v-model="model.email" placeholder="请输入用户邮箱" />
					</XFormItem>
					<XFormItem label="手机号码" prop="mobile">
						<XInput v-model="model.mobile" placeholder="请输入手机号码" />
					</XFormItem>
					<XFormItem label="部门" prop="deptId" required>
						<XSelect v-model="model.deptId" placeholder="请选择部门" :remote-method="BIZ_Dept_APIS.getList" searchable />
					</XFormItem>
				</div>
				<XFormItem label="上传头像" prop="avatar">
					<XFileUpload v-model="model.avatar" accept="jpg,png" />
					<!--<div class="border-red">1</div>-->
				</XFormItem>
				<!--				<XFormItem label="帐号状态（0正常 1停用）" prop="status">
					<XRadioGroup v-model="model.status" tag-group-name="" />
				</XFormItem>
				<XFormItem label="用户性别" prop="sex">
					<XRadioGroup v-model="model.sex" tag-group-name="" />
				</XFormItem>-->
				<XFormItem label="备注" prop="remark">
					<XInput v-model="model.remark" placeholder="请输入备注" />
				</XFormItem>
			</BizFormForm>
		</div>
	</BizModalsSave>
</template>
<script setup lang="ts">
const visible = ref(false)
const model = ref({
	id: undefined,
	username: undefined,
	password: undefined,
	nickname: undefined,
	remark: undefined,
	deptId: undefined,
	postIds: undefined,
	email: undefined,
	mobile: undefined,
	sex: undefined,
	avatar: undefined,
	status: undefined,
	loginIp: undefined,
	loginDate: undefined,
	createType: undefined,
	entityId: undefined,
})
const emit = defineEmits(['success'])
const title = ref('')
const userStore = useUserStore()
let type = ref('')
const id = ref(0)
const detailLoading = ref(false)
let run, loading
const computedLoading = computed(() => loading?.value || detailLoading.value)
function open(_type, _id) {
	model.value = {}
	if (_id) {
		getDetail(_id)
	}
	visible.value = true
	type.value = _type
	title.value = _type === 'create' ? '新增' : '编辑'
	id.value = _id
	let { run: _run, loading: _loading } = xUseRequest(BIZ_Users_APIS[_type], model)
	run = _run
	loading = _loading
}

async function getDetail(id) {
	detailLoading.value = true
	const res = await BIZ_Users_APIS.get(id).finally(() => (detailLoading.value = false))
	Object.assign(model.value, res)
}

defineExpose({ open })
async function handleSubmit() {
	await run()
	if (model.value?.id === userStore.getUserinfo?.id) {
		userStore.setUserinfo(model.value)
	}
	visible.value = false
	emit('success')
}
</script>

<style scoped></style>
