<template>
	<BizModalsSave v-model="visible" :title="title">
		<div v-loading="computedLoading" class="pb-base">
			<BizFormForm :model="model" :rules="rules" @submit="handleSubmit">
				<XFormItem label="标题" prop="title">
					<XInput v-model="model.title" placeholder="请输入标题" />
				</XFormItem>
				<XFormItem label="跳转地址" prop="targetUrl">
					<XInput v-model="model.targetUrl" placeholder="请输入跳转地址" />
				</XFormItem>
				<XFormItem label="封面" prop="cover">
					<XFileUpload v-model="model.cover" placeholder="请输入跳转地址" accept="image/*" />
				</XFormItem>
				<div class="grid grid-cols-2 gap-x-base">
					<XFormItem label="是否显示" prop="isShow">
						<XRadioGroup v-model="model.isShow" tag-group-name="是否" />
					</XFormItem>
					<XFormItem label="是否置顶" prop="isTop">
						<XRadioGroup v-model="model.isTop" tag-group-name="是否" />
					</XFormItem>
				</div>
				<XFormItem label="排序值" prop="sort">
					<XInput v-model="model.sort" placeholder="请输入排序值" />
				</XFormItem>
			</BizFormForm>
		</div>
	</BizModalsSave>
</template>
<script setup lang="ts">
const visible = ref(false)
const model = ref({
	id: undefined,
	title: undefined,
	type: undefined,
	position: undefined,
	platform: undefined,
	startTime: undefined,
	endTime: undefined,
	sort: undefined,
	targetUrl: undefined,
	elements: undefined,
	isShow: undefined,
	isTop: undefined,
	cover: undefined,
})
const rules = reactive({
	title: [{ required: true, message: '标题不能为空' }],
	cover: [{ required: true, message: '封面不能为空' }],
	isShow: [{ required: true, message: '是否显示不能为空' }],
	isTop: [{ required: true, message: '是否置顶不能为空' }],
})
const emit = defineEmits(['success'])
const title = ref('')
let type = ref('')
const id = ref(0)
const detailLoading = ref(false)
let run, loading
const computedLoading = computed(() => loading?.value || detailLoading.value)
function open(_type, _id) {
	model.value = { isShow: '是', isTop: '否', position: 'home_top', platform: '微信小程序' }
	if (_id) {
		getDetail(_id)
	}
	visible.value = true
	type.value = _type
	title.value = _type === 'create' ? '新增' : '编辑'
	id.value = _id
	let { run: _run, loading: _loading } = xUseRequest(BIZ_CommonBanner_APIS[_type], model)
	run = _run
	loading = _loading
}

async function getDetail(id) {
	detailLoading.value = true
	const res = await BIZ_CommonBanner_APIS.get(id).finally(() => (detailLoading.value = false))
	Object.assign(model.value, res)
}

defineExpose({ open })
async function handleSubmit() {
	await run()
	visible.value = false
	emit('success')
}
</script>

<style scoped></style>
