<template>
	<BizModalsSave v-model="visible" :title="title">
		<div v-loading="computedLoading" class="pb-base">
			<BizFormForm :model="model" :rules="rules" @submit="handleSubmit">
				<div class="gap-x-base grid grid-cols-2">
								<XFormItem label="车辆ID" prop="vehicleId">
									<XInput v-model="model.vehicleId" placeholder="请输入车辆ID"  @keyup.enter.stop="handleSubmit" />
								</XFormItem>
								<XFormItem label="驾驶员ID" prop="driverId">
									<XInput v-model="model.driverId" placeholder="请输入驾驶员ID"  @keyup.enter.stop="handleSubmit" />
								</XFormItem>
				</div>
			</BizFormForm>
		</div>
	</BizModalsSave>
</template>
<script setup lang="ts">
	const visible = ref(false)
	const model = ref({
						id: undefined,
						vehicleId: undefined,
						driverId: undefined,
	})
	const rules = reactive({
					vehicleId: [{ required: true, message: '车辆ID不能为空' }],
					driverId: [{ required: true, message: '驾驶员ID不能为空' }],
	})
	const emit = defineEmits(['success'])
	const title = ref('')
	let type = ref('')
	const id = ref(0)
	const detailLoading = ref(false)
	const requestLoading = ref(false)
	let run, data
	const computedLoading = computed(() => requestLoading?.value || detailLoading.value)
	const params = ref({})
	function open(_type, _id, _params) {
		model.value = {}
		params.value = _params
		if (_id) {
			getDetail(_id)
		}
		visible.value = true
		type.value = _type
		title.value = _type === 'create' ? '新增' : '编辑'
		id.value = _id
		let { run: _run, loading, data: _data  } = xUseRequest(BIZ_JnrmDriverVehicle_APIS[_type], model)
		run = _run
		// 监听xUseRequest返回的loading状态，同步到组件级别的loading
		watchEffect(() => {
			requestLoading.value = loading.value
		})
		data = _data
	}

	async function getDetail(id) {
		detailLoading.value = true
		const res = await BIZ_JnrmDriverVehicle_APIS.get(id).finally(() => (detailLoading.value = false))
		Object.assign(model.value, res)
	}

	defineExpose({ open })
	async function handleSubmit() {
		if (!visible.value) return
		await run()
		visible.value = false
		if (data && type.value == 'create') {
			model.value.id = data
		}
		emit('success', model.value)
	}
</script>

<style scoped></style>
