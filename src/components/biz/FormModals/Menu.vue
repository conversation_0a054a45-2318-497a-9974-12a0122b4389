<template>
	<BizModalsSave v-model="visible" :title="title">
		<div v-loading="loading || detailLoading" class="pb-base">
			<BizFormForm :model="model" :rules="rules" @submit="handleSubmit">
				<XFormItem label="父菜单ID" prop="parentId">
					<XInput v-model="model.parentId" placeholder="请输入父菜单ID" />
				</XFormItem>
				<div class="grid grid-cols-2 gap-x-base">
					<XFormItem label="菜单名称" prop="name">
						<XInput v-model="model.name" placeholder="请输入菜单名称" />
					</XFormItem>
					<XFormItem label="菜单图标" prop="icon">
						<XInput v-model="model.icon" placeholder="请输入菜单图标" />
					</XFormItem>
					<XFormItem label="权限标识" prop="permission">
						<XInput v-model="model.permission" placeholder="请输入权限标识" />
					</XFormItem>

					<XFormItem label="路由地址" prop="path">
						<XInput v-model="model.path" placeholder="请输入路由地址" />
					</XFormItem>
					<XFormItem label="显示顺序" prop="sort">
						<XInput v-model="model.sort" placeholder="请输入显示顺序" />
					</XFormItem>
					<!--					<XFormItem label="组件路径" prop="component">
						<XInput v-model="model.component" placeholder="请输入组件路径" />
					</XFormItem>-->
					<!--					<XFormItem label="组件名" prop="componentName">
						<XInput v-model="model.componentName" placeholder="请输入组件名" />
					</XFormItem>-->
				</div>
				<!--				<XFormItem label="菜单类型" prop="type">
					<XSelect v-model="model.type" placeholder="请选择菜单类型" tag-group-name="" />
				</XFormItem>-->
				<div class="grid grid-cols-2 gap-x-base">
					<!--					<XFormItem label="菜单状态" prop="status">
						<XRadioGroup v-model="model.status" tag-group-name="是否" />
					</XFormItem>-->
					<XFormItem label="是否可见" prop="visible">
						<XRadioGroup v-model="model.visible" tag-group-name="是否" tag-value-format="BOOLEAN" />
					</XFormItem>
					<XFormItem label="是否缓存" prop="keepAlive">
						<XRadioGroup v-model="model.keepAlive" tag-group-name="是否" tag-value-format="BOOLEAN" />
					</XFormItem>
					<!--					<XFormItem label="总是显示" prop="alwaysShow">
						<XRadioGroup v-model="model.alwaysShow" tag-group-name="是否" tag-value-format="BOOLEAN" />
					</XFormItem>-->
				</div>
			</BizFormForm>
		</div>
	</BizModalsSave>
</template>
<script setup lang="ts">
const visible = ref(false)
const model = ref({
	id: undefined,
	name: undefined,
	permission: undefined,
	type: undefined,
	sort: undefined,
	parentId: undefined,
	path: undefined,
	icon: undefined,
	component: undefined,
	componentName: undefined,
	status: undefined,
	visible: undefined,
	keepAlive: undefined,
	alwaysShow: undefined,
})
const emit = defineEmits(['success'])
const title = ref('')
let type = ref('')
const id = ref(0)
const detailLoading = ref(false)
let run, loading
function open(_type, _id) {
	model.value = { visible: true, keepAlive: true }
	if (_id) {
		getDetail(_id)
	}
	visible.value = true
	type.value = _type
	title.value = _type === 'create' ? '新增' : '编辑'
	id.value = _id
	let { run: _run, loading: _loading } = xUseRequest(BIZ_Menu_APIS[_type], model)
	run = _run
	loading = _loading
}

async function getDetail(id) {
	detailLoading.value = true
	const res = await BIZ_Menu_APIS.get(id).finally(() => (detailLoading.value = false))
	Object.assign(model.value, res)
}

defineExpose({ open })
async function handleSubmit() {
	await run()
	visible.value = false
	emit('success', model.value)
}
</script>

<style scoped></style>
