<template>
	<BizModalsSave v-model="visible" :title="title">
		<div v-loading="computedLoading" class="pb-base">
			<BizFormForm :model="model" :rules="rules" @submit="handleSubmit">
				<div class="grid grid-cols-1 gap-x-base">
					<XFormItem label="铅封编号" prop="sealNo">
						<XInput v-model="model.sealNo" placeholder="请输入铅封编号" @keyup.enter.stop="handleSubmit" />
					</XFormItem>
					<XFormItem label="状态" prop="status">
						<XSelect v-model="model.status" tag-group-name="JNRM铅封状态" />
					</XFormItem>
					<XFormItem label="序列号" prop="serialNumber">
						<XInput v-model="model.serialNumber" placeholder="请输入序列号" @keyup.enter.stop="handleSubmit" />
					</XFormItem>
				</div>
			</BizFormForm>
		</div>
	</BizModalsSave>
</template>
<script setup lang="ts">
const visible = ref(false)
const model = ref({
	id: undefined,
	sealNo: undefined,
	status: undefined,
	batteryLevel: undefined,
	serialNumber: undefined,
})
const rules = reactive({
	sealNo: [{ required: true, message: '铅封编号不能为空' }],
	status: [{ required: true, message: '状态（1：正常，2：打开，3：异常）不能为空' }],
	serialNumber: [{ required: true, message: '序列号不能为空' }],
})
const emit = defineEmits(['success'])
const title = ref('')
let type = ref('')
const id = ref(0)
const detailLoading = ref(false)
const requestLoading = ref(false)
let run, data
const computedLoading = computed(() => requestLoading?.value || detailLoading.value)
const params = ref({})
function open(_type, _id, _params) {
	model.value = {}
	params.value = _params
	if (_id) {
		getDetail(_id)
	}
	visible.value = true
	type.value = _type
	title.value = _type === 'create' ? '新增' : '编辑'
	id.value = _id
	let { run: _run, loading, data: _data } = xUseRequest(BIZ_JnrmSeal_APIS[_type], model)
	run = _run
	// 监听xUseRequest返回的loading状态，同步到组件级别的loading
	watchEffect(() => {
		requestLoading.value = loading.value
	})
	data = _data
}

async function getDetail(id) {
	detailLoading.value = true
	const res = await BIZ_JnrmSeal_APIS.get(id).finally(() => (detailLoading.value = false))
	Object.assign(model.value, res)
}

defineExpose({ open })
async function handleSubmit() {
	if (!visible.value) return
	await run()
	visible.value = false
	if (data && type.value == 'create') {
		model.value.id = data
	}
	emit('success', model.value)
}
</script>

<style scoped></style>
