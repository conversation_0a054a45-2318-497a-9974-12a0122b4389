<template>
	<BizModalsSave v-model="visible" :title="title">
		<div v-loading="loading" class="pb-base">
			<BizFormForm :model="model" :rules="rules" @submit="handleSubmit">
				<div class="grid grid-cols-1 gap-x-base">
					<XFormItem label="用户名称" prop="">
						{{ row.username }}
					</XFormItem>
					<XFormItem label="用户昵称" prop="">
						{{ row.nickname }}
					</XFormItem>
					<XFormItem label="角色" prop="name">
						<XSelect v-model="model.roleIds" :options="roles" multiple />
					</XFormItem>
				</div>
			</BizFormForm>
		</div>
	</BizModalsSave>
</template>
<script setup lang="ts">
const visible = ref(false)
const model = ref({
	userId: null,
	roleIds: null,
})
const roles = ref([])
async function loadMenus() {
	loading.value = true
	const res = await BIZ_Role_APIS.getSimpleList()
	roles.value = res
	const res2 = await BIZ_Role_APIS.getUserRoleList(row.value.id).finally(() => {
		loading.value = false
	})
	console.log('res2---', res2)
	model.value.roleIds = res2
}
const rules = reactive({})
const emit = defineEmits(['success'])
const title = ref('')
const row = ref({})
let { run, loading } = xUseRequest(BIZ_Role_APIS.assignUserRoles, model)
function open(_row) {
	roles.value = []
	model.value.userId = _row.id
	model.value.roleIds = []
	row.value = _row
	visible.value = true
	loadMenus()
}
defineExpose({ open })
async function handleSubmit() {
	await run()
	visible.value = false
	toast.success('操作成功')
	emit('success')
}
</script>

<style scoped></style>
