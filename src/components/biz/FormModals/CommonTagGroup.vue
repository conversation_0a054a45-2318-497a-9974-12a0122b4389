<template>
	<BizModalsSave v-model="visible" :title="title">
		<div v-loading="loading || detailLoading" class="pb-base">
			<BizFormForm :model="model" :rules="rules" @submit="handleSubmit">
				<XFormItem required label="名称" prop="name">
					<XInput v-model="model.name" placeholder="请输入名称" />
				</XFormItem>
				<XFormItem label="描述" prop="description">
					<XInput v-model="model.description" />
				</XFormItem>
				<XFormItem label="排序" prop="sort">
					<XInput v-model="model.sort" placeholder="请输入排序" />
				</XFormItem>
			</BizFormForm>
			<div v-if="model.id" class="relative rounded mt-base">
				<BizListCommonTag :searchable="false" class="!p-0" :params="{ groupId: model.id, groupName: model.name }"></BizListCommonTag>
			</div>
		</div>
	</BizModalsSave>
</template>
<script setup lang="ts">
const visible = ref(false)
const model = ref({
	id: undefined,
	name: undefined,
	description: undefined,
	sort: undefined,
})
const emit = defineEmits(['success'])
const title = ref('')
let type = ref('')
const id = ref(0)
const detailLoading = ref(false)
let run, loading
function open(_type, _id) {
	model.value = { sort: 100 }
	if (_id) {
		getDetail(_id)
	}
	visible.value = true
	type.value = _type
	title.value = _type === 'create' ? '新增' : '编辑'
	id.value = _id
	let { run: _run, loading: _loading } = xUseRequest(BIZ_CommonTagGroup_APIS[_type], model)
	run = _run
	loading = _loading
}

async function getDetail(id) {
	detailLoading.value = true
	const res = await BIZ_CommonTagGroup_APIS.get(id).finally(() => (detailLoading.value = false))
	Object.assign(model.value, res)
}

defineExpose({ open })
async function handleSubmit() {
	await run()
	visible.value = false
	emit('success')
}
</script>

<style scoped></style>
