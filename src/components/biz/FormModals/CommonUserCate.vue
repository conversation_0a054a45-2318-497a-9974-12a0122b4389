<template>
	<BizModalsSave v-model="visible" :title="title">
		<div v-loading="computedLoading" class="pb-base">
			<BizFormForm :model="model" :rules="rules" @submit="handleSubmit">
				<div class="grid grid-cols-1 gap-x-base">
					<XFormItem v-show="isTopCate === '否'" required label="上级分类" prop="parentId">
						<XCascader v-model="model.parentId" is-flat :data-load-func="BIZ_CommonUserCate_APIS.getList" />
					</XFormItem>
					<XFormItem label="分类名称" prop="name">
						<XInput v-model="model.name" placeholder="请输入分类名称" />
					</XFormItem>
					<XFormItem label="描述" prop="description">
						<XInput v-model="model.description" type="textarea" />
					</XFormItem>
					<XFormItem label="排序" prop="sort">
						<XInput v-model="model.sort" placeholder="请输入排序" />
					</XFormItem>
					<XFormItem label="备注" prop="mark">
						<XInput v-model="model.mark" placeholder="请输入备注" />
					</XFormItem>
				</div>
			</BizFormForm>
		</div>
	</BizModalsSave>
</template>
<script setup lang="ts">
const visible = ref(false)
const model = ref({
	id: undefined,
	name: undefined,
	description: undefined,
	parentId: undefined,
	sort: undefined,
	mark: undefined,
})
const isTopCate = ref('是')
const rules = reactive({
	name: [{ required: true, message: '分类名称不能为空' }],
	parentId: [{ required: true, message: '上级分类不能为空' }],
	sort: [{ required: true, message: '排序不能为空' }],
})
const emit = defineEmits(['success'])
const title = ref('')
let type = ref('')
const id = ref(0)
const detailLoading = ref(false)
let run, loading
const computedLoading = computed(() => loading?.value || detailLoading.value)
function open(_type, _id) {
	isTopCate.value = _type === 'create' && _id ? '否' : '是'
	model.value = { parentId: isTopCate.value === '是' ? 0 : _id, sort: 100 }
	if (_type === 'update' && _id) {
		getDetail(_id)
	}
	visible.value = true
	type.value = _type
	title.value = _type === 'create' ? '新增' : '编辑'
	id.value = _id
	let { run: _run, loading: _loading } = xUseRequest(BIZ_CommonUserCate_APIS[_type], model)
	run = _run
	loading = _loading
}

async function getDetail(id) {
	detailLoading.value = true
	const res = await BIZ_CommonUserCate_APIS.get(id).finally(() => (detailLoading.value = false))
	Object.assign(model.value, res)
}

defineExpose({ open })
async function handleSubmit() {
	await run()
	visible.value = false
	emit('success', model.value)
}
</script>

<style scoped></style>
