<template>
	<BizModalsSave v-model="visible" :title="title">
		<div v-loading="computedLoading" class="pb-base">
			<BizFormForm :model="model" :rules="rules" @submit="handleSubmit">
				<XFormItem label="分类" prop="cateId">
					<XCascader v-model="model.cateId" is-flat leaf-only :data-load-func="BIZ_CommonUserCate_APIS.getList" />
				</XFormItem>
				<div class="grid grid-cols-2 gap-x-base">
					<XFormItem label="用户名" prop="username">
						<XInput v-model="model.username" placeholder="请输入用户名(账号)" />
					</XFormItem>
					<XFormItem label="密码" prop="password">
						<XInput v-model="model.password" placeholder="请输入密码" />
					</XFormItem>
					<XFormItem label="真实姓名" prop="realName">
						<XInput v-model="model.realName" placeholder="请输入真实姓名" />
					</XFormItem>
					<XFormItem label="昵称" prop="nickname">
						<XInput v-model="model.nickname" placeholder="请输入昵称" />
					</XFormItem>
					<XFormItem label="手机号" prop="phone">
						<XInput v-model="model.phone" placeholder="请输入手机号" />
					</XFormItem>
					<XFormItem label="邮箱" prop="email">
						<XInput v-model="model.email" placeholder="请输入邮箱" />
					</XFormItem>
					<XFormItem label="性别" prop="gender">
						<XInput v-model="model.gender" placeholder="请输入邮箱" />
					</XFormItem>

					<XFormItem label="身份证号" prop="idCard">
						<XInput v-model="model.idCard" placeholder="请输入身份证号" />
					</XFormItem>
					<XFormItem label="注册平台" prop="platform">
						<XInput v-model="model.platform" disabled placeholder="请输入注册平台(wx:微信,app:APP,h5:H5,pc:PC)" />
					</XFormItem>
					<XFormItem label="省份" prop="province">
						<XInput v-model="model.province" placeholder="请输入省份" />
					</XFormItem>
					<XFormItem label="城市" prop="city">
						<XInput v-model="model.city" placeholder="请输入城市" />
					</XFormItem>
				</div>
				<XFormItem label="VIP到期时间" prop="vipEndTime">
					<XDateTimePicker v-model="model.vipEndTime" />
					<p class="text-warning text-xs">设置到期时间在时间范围内，则自动成为VIP</p>
				</XFormItem>
			</BizFormForm>
		</div>
	</BizModalsSave>
</template>
<script setup lang="ts">
const visible = ref(false)
const model = ref({
	cateId: undefined,
	id: undefined,
	username: undefined,
	password: undefined,
	passwordUpdateTime: undefined,
	nickname: undefined,
	avatar: undefined,
	phone: undefined,
	email: undefined,
	gender: undefined,
	birthday: undefined,
	realName: undefined,
	idCard: undefined,
	idCardVerified: undefined,
	platform: undefined,
	openid: undefined,
	unionid: undefined,
	isForbidden: undefined,
	lastLoginTime: undefined,
	lastLoginIp: undefined,
	loginFailCount: undefined,
	accountLockTime: undefined,
	level: undefined,
	points: undefined,
	source: undefined,
	inviterId: undefined,
	inviteCode: undefined,
	country: undefined,
	province: undefined,
	city: undefined,
	tags: undefined,
	vipEndTime: undefined,
})
const rules = reactive({
	cateId: [{ required: true, message: '分类不能为空' }],
	username: [{ required: true, message: '用户名不能为空' }],
	password: [{ required: true, message: '密码不能为空' }],
	realName: [{ required: true, message: '真实姓名不能为空' }],
	platform: [{ required: true, message: '注册平台(wx:微信,app:APP,h5:H5,pc:PC)不能为空' }],
	isForbidden: [{ required: true, message: '是否禁用不能为空' }],
})
const emit = defineEmits(['success'])
const title = ref('')
let type = ref('')
const id = ref(0)
const detailLoading = ref(false)
let run, loading
const computedLoading = computed(() => loading?.value || detailLoading.value)
function open(_type, _id) {
	// 2099年09月09日时间戳毫秒
	const vipEndTime = new Date('2099-09-09').getTime()
	model.value = { platform: '后台录入', vipEndTime }
	if (_id) {
		getDetail(_id)
	}
	visible.value = true
	type.value = _type
	title.value = _type === 'create' ? '新增' : '编辑'
	id.value = _id
	let { run: _run, loading: _loading } = xUseRequest(BIZ_CommonUser_APIS[_type], model)
	run = _run
	loading = _loading
}

async function getDetail(id) {
	detailLoading.value = true
	const res = await BIZ_CommonUser_APIS.get(id).finally(() => (detailLoading.value = false))
	Object.assign(model.value, res)
}

defineExpose({ open })
async function handleSubmit() {
	await run()
	visible.value = false
	emit('success')
}
</script>

<style scoped></style>
