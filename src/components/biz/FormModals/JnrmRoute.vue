<template>
	<BizModalsSave v-model="visible" :title="title">
		<div v-loading="computedLoading" class="pb-base">
			<BizFormForm :model="model" :rules="rules" @submit="handleSubmit">
				<div class="gap-x-base grid grid-cols-2">
								<XFormItem label="出发地ID" prop="departureId">
									<XInput v-model="model.departureId" placeholder="请输入出发地ID"  @keyup.enter.stop="handleSubmit" />
								</XFormItem>
								<XFormItem label="目的地ID" prop="destinationId">
									<XInput v-model="model.destinationId" placeholder="请输入目的地ID"  @keyup.enter.stop="handleSubmit" />
								</XFormItem>
								<XFormItem label="轨迹路径（JSON格式存储）" prop="trackPath">
									<XInput v-model="model.trackPath" placeholder="请输入轨迹路径（JSON格式存储）"  @keyup.enter.stop="handleSubmit" />
								</XFormItem>
								<XFormItem label="路线距离（公里）" prop="distance">
									<XInput v-model="model.distance" placeholder="请输入路线距离（公里）"  @keyup.enter.stop="handleSubmit" />
								</XFormItem>
								<XFormItem label="预估时长（分钟）" prop="estimatedDuration">
									<XInput v-model="model.estimatedDuration" placeholder="请输入预估时长（分钟）"  @keyup.enter.stop="handleSubmit" />
								</XFormItem>
								<XFormItem label="路线名称" prop="routeName">
									<XInput v-model="model.routeName" placeholder="请输入路线名称"  @keyup.enter.stop="handleSubmit" />
								</XFormItem>
				</div>
			</BizFormForm>
		</div>
	</BizModalsSave>
</template>
<script setup lang="ts">
	const visible = ref(false)
	const model = ref({
						id: undefined,
						departureId: undefined,
						destinationId: undefined,
						trackPath: undefined,
						distance: undefined,
						estimatedDuration: undefined,
						routeName: undefined,
	})
	const rules = reactive({
					departureId: [{ required: true, message: '出发地ID不能为空' }],
					destinationId: [{ required: true, message: '目的地ID不能为空' }],
	})
	const emit = defineEmits(['success'])
	const title = ref('')
	let type = ref('')
	const id = ref(0)
	const detailLoading = ref(false)
	const requestLoading = ref(false)
	let run, data
	const computedLoading = computed(() => requestLoading?.value || detailLoading.value)
	const params = ref({})
	function open(_type, _id, _params) {
		model.value = {}
		params.value = _params
		if (_id) {
			getDetail(_id)
		}
		visible.value = true
		type.value = _type
		title.value = _type === 'create' ? '新增' : '编辑'
		id.value = _id
		let { run: _run, loading, data: _data  } = xUseRequest(BIZ_JnrmRoute_APIS[_type], model)
		run = _run
		// 监听xUseRequest返回的loading状态，同步到组件级别的loading
		watchEffect(() => {
			requestLoading.value = loading.value
		})
		data = _data
	}

	async function getDetail(id) {
		detailLoading.value = true
		const res = await BIZ_JnrmRoute_APIS.get(id).finally(() => (detailLoading.value = false))
		Object.assign(model.value, res)
	}

	defineExpose({ open })
	async function handleSubmit() {
		if (!visible.value) return
		await run()
		visible.value = false
		if (data && type.value == 'create') {
			model.value.id = data
		}
		emit('success', model.value)
	}
</script>

<style scoped></style>
