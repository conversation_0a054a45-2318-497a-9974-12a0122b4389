<template>
	<BizModalsSave v-model="visible" :title="title">
		<div v-loading="computedLoading" class="pb-base">
			<BizFormForm :model="model" :rules="rules" @submit="handleSubmit">
				<div class="grid grid-cols-2 gap-x-base">
					<XFormItem label="任务名称" prop="name">
						<XInput v-model="model.name" placeholder="请输入任务名称" @keyup.enter.stop="handleSubmit" />
					</XFormItem>
					<XFormItem label="处理器的名字" prop="handlerName">
						<XInput v-model="model.handlerName" placeholder="请输入处理器的名字" @keyup.enter.stop="handleSubmit" />
					</XFormItem>
					<XFormItem label="处理器的参数" prop="handlerParam">
						<XInput v-model="model.handlerParam" placeholder="请输入处理器的参数" @keyup.enter.stop="handleSubmit" />
					</XFormItem>
					<XFormItem label="CRON 表达式" prop="cronExpression">
						<XInput v-model="model.cronExpression" placeholder="请输入CRON 表达式" @keyup.enter.stop="handleSubmit" />
					</XFormItem>
					<XFormItem label="重试次数" prop="retryCount">
						<XInput v-model="model.retryCount" placeholder="请输入重试次数" @keyup.enter.stop="handleSubmit" />
					</XFormItem>
					<XFormItem label="重试间隔" prop="retryInterval">
						<XInput v-model="model.retryInterval" placeholder="请输入重试间隔" @keyup.enter.stop="handleSubmit" />
					</XFormItem>
					<XFormItem label="监控超时时间" prop="monitorTimeout">
						<XInput v-model="model.monitorTimeout" placeholder="请输入监控超时时间" @keyup.enter.stop="handleSubmit" />
					</XFormItem>
				</div>
			</BizFormForm>
		</div>
	</BizModalsSave>
</template>
<script setup lang="ts">
const visible = ref(false)
const model = ref({
	id: undefined,
	name: undefined,
	status: undefined,
	handlerName: undefined,
	handlerParam: undefined,
	cronExpression: undefined,
	retryCount: undefined,
	retryInterval: undefined,
	monitorTimeout: undefined,
})
const rules = reactive({
	name: [{ required: true, message: '任务名称不能为空' }],
	status: [{ required: true, message: '任务状态不能为空' }],
	handlerName: [{ required: true, message: '处理器的名字不能为空' }],
	cronExpression: [{ required: true, message: 'CRON 表达式不能为空' }],
	retryCount: [{ required: true, message: '重试次数不能为空' }],
	retryInterval: [{ required: true, message: '重试间隔不能为空' }],
	monitorTimeout: [{ required: true, message: '监控超时时间不能为空' }],
})
const emit = defineEmits(['success'])
const title = ref('')
let type = ref('')
const id = ref(0)
const detailLoading = ref(false)
const requestLoading = ref(false)
let run, data
const computedLoading = computed(() => requestLoading?.value || detailLoading.value)
const params = ref({})
function open(_type, _id, _params) {
	model.value = {}
	params.value = _params
	if (_id) {
		getDetail(_id)
	}
	visible.value = true
	type.value = _type
	title.value = _type === 'create' ? '新增' : '编辑'
	id.value = _id
	let { run: _run, loading, data: _data } = xUseRequest(BIZ_Job_APIS[_type], model)
	run = _run
	// 监听xUseRequest返回的loading状态，同步到组件级别的loading
	watchEffect(() => {
		requestLoading.value = loading.value
	})
	data = _data
}

async function getDetail(id) {
	detailLoading.value = true
	const res = await BIZ_Job_APIS.get(id).finally(() => (detailLoading.value = false))
	Object.assign(model.value, res)
}

defineExpose({ open })
async function handleSubmit() {
	if (!visible.value) return
	await run()
	visible.value = false
	if (data && type.value == 'create') {
		model.value.id = data
	}
	emit('success', model.value)
}
</script>

<style scoped></style>
