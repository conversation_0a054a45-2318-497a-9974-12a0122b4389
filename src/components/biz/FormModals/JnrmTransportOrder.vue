<template>
	<BizModalsSave v-model="visible" :title="title">
		<div v-loading="computedLoading" class="pb-base">
			<BizFormForm :model="model" :rules="rules" @submit="handleSubmit">
				<div class="grid grid-cols-2 gap-x-base">
					<XFormItem label="出发地" prop="departureId">
						<XSelect
							v-model="model.departureId"
							v-model:label="model.departureName"
							:remote-method="BIZ_JnrmLocation_APIS.getPage"
							:placeholder="model.departureName || '请选择出发地'"
							@keyup.enter.stop="handleSubmit"
						/>
					</XFormItem>
					<XFormItem label="目的地" prop="destinationId">
						<XSelect
							v-model="model.destinationId"
							v-model:label="model.destinationName"
							:remote-method="BIZ_JnrmLocation_APIS.getPage"
							:placeholder="model.destinationName || '请选择目的地'"
							@change="
								(_, row) => {
									if (userStore.isPlatformSuper) {
										model.deptId = row.dept?.id
									}
								}
							"
							@keyup.enter.stop="handleSubmit"
						/>
					</XFormItem>
					<XFormItem label="运单号" prop="orderNo">
						<XInput v-model="model.orderNo" placeholder="请输入运单号" @keyup.enter.stop="handleSubmit" />
					</XFormItem>
					<XFormItem label="车辆" prop="vehicleId">
						<XSelect
							v-model="model.vehicleId"
							v-model:label="model.licensePlate"
							label-key="licensePlate"
							:remote-method="BIZ_JnrmVehicle_APIS.getPage"
							:placeholder="model.licensePlate || '请选择车辆'"
							@change="
								(_, row) => {
									model.licensePlateColor = row.licensePlateColor
								}
							"
							@keyup.enter.stop="handleSubmit"
						/>
					</XFormItem>
					<XFormItem label="驾驶员" prop="driverId">
						<XSelect
							v-model="model.driverId"
							v-model:label="model.driverName"
							:remote-method="BIZ_JnrmDriver_APIS.getPage"
							:placeholder="model.driverName || '请选择驾驶员'"
							@change="
								(_, row) => {
									model.driverPhone = row.phone
								}
							"
							@keyup.enter.stop="handleSubmit"
						/>
					</XFormItem>
					<!--					<XFormItem label="车牌号" prop="licensePlate">-->
					<!--						<XInput v-model="model.licensePlate" placeholder="请输入车牌号" @keyup.enter.stop="handleSubmit" />-->
					<!--					</XFormItem>-->
					<XFormItem label="司机手机号" prop="driverPhone">
						<XInput v-model="model.driverPhone" placeholder="请输入司机手机号" @keyup.enter.stop="handleSubmit" />
					</XFormItem>
					<XFormItem label="货物信息" prop="cargoInfo">
						<XInput v-model="model.cargoInfo" placeholder="请输入货物信息" @keyup.enter.stop="handleSubmit" />
					</XFormItem>
					<XFormItem label="货物重量(吨)" prop="cargoWeight">
						<XInput v-model="model.cargoWeight" placeholder="请输入货物重量" type="number" @keyup.enter.stop="handleSubmit" />
					</XFormItem>
					<XFormItem label="开始时间" prop="startTime">
						<XDateTimePicker v-model="model.startTime" type="datetime" display-format="YYYY-MM-DD HH:mm" placeholder="选择开始时间" />
					</XFormItem>
					<!--					<XFormItem label="送达时间" prop="deliveryTime">-->
					<!--						<XDateTimePicker v-model="model.deliveryTime" type="date" placeholder="选择送达时间" />-->
					<!--					</XFormItem>-->
					<XFormItem label="预估送达时间" prop="estimatedArrivalTime">
						<XDateTimePicker v-model="model.estimatedArrivalTime" type="datetime" display-format="YYYY-MM-DD HH:mm" placeholder="选择预估送达时间" />
					</XFormItem>
					<!--					<XFormItem label="实际送达时间" prop="actualArrivalTime">-->
					<!--						<XDateTimePicker v-model="model.actualArrivalTime" type="date" placeholder="选择实际送达时间" />-->
					<!--					</XFormItem>-->
					<XFormItem label="状态" prop="status">
						<XSelect v-model="model.status" tag-group-name="JNRM运单状态" />
					</XFormItem>
					<!--					<XFormItem label="部门" prop="deptId">-->
					<!--						<XSelect v-model="model.deptId" :remote-method="BIZ_Dept_APIS.getPage" placeholder="请输入司机手机号" @keyup.enter.stop="handleSubmit" />-->
					<!--					</XFormItem>-->
				</div>
			</BizFormForm>
		</div>
	</BizModalsSave>
</template>
<script setup lang="ts">
const visible = ref(false)
const model = ref({
	id: undefined,
	orderNo: undefined,
	powerPlantId: undefined,
	departureId: undefined,
	destinationId: undefined,
	vehicleId: undefined,
	driverId: undefined,
	licensePlate: undefined,
	licensePlateColor: undefined,
	driverPhone: undefined,
	cargoInfo: undefined,
	cargoWeight: undefined,
	startTime: undefined,
	deliveryTime: undefined,
	estimatedArrivalTime: undefined,
	actualArrivalTime: undefined,
	status: undefined,
	departureName: undefined,
	destinationName: undefined,
	driverName: undefined,
})
const rules = reactive({
	powerPlantId: [{ required: true, message: '电厂ID不能为空' }],
	departureId: [{ required: true, message: '出发地ID不能为空' }],
	destinationId: [{ required: true, message: '目的地ID不能为空' }],
	vehicleId: [{ required: true, message: '车辆ID不能为空' }],
	driverId: [{ required: true, message: '驾驶员ID不能为空' }],
	licensePlate: [{ required: true, message: '车牌号不能为空' }],
	driverPhone: [{ required: true, message: '司机手机号不能为空' }],
	status: [{ required: true, message: '状态（1：待发车，2：在途，3：已送达，4：异常，5：取消）不能为空' }],
})
const userStore = useUserStore()
const emit = defineEmits(['success'])
const title = ref('')
let type = ref('')
const id = ref(0)
const detailLoading = ref(false)
const requestLoading = ref(false)
let run, data
const computedLoading = computed(() => requestLoading?.value || detailLoading.value)
const params = ref({})
function open(_type, _id, _params) {
	model.value = {}
	params.value = _params
	if (_id) {
		getDetail(_id)
	}
	visible.value = true
	type.value = _type
	title.value = _type === 'create' ? '新增' : '编辑'
	id.value = _id
	let { run: _run, loading, data: _data } = xUseRequest(BIZ_JnrmTransportOrder_APIS[_type], model)
	run = _run
	// 监听xUseRequest返回的loading状态，同步到组件级别的loading
	watchEffect(() => {
		requestLoading.value = loading.value
	})
	data = _data
}

async function getDetail(id) {
	detailLoading.value = true
	const res = await BIZ_JnrmTransportOrder_APIS.get(id).finally(() => (detailLoading.value = false))
	Object.assign(model.value, res)
}

defineExpose({ open })
async function handleSubmit() {
	if (!visible.value) return
	await run()
	visible.value = false
	if (data && type.value == 'create') {
		model.value.id = data
	}
	emit('success', model.value)
}
</script>

<style scoped></style>
