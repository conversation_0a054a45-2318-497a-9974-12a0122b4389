<template>
	<BizModalsSave v-model="visible" :title="title">
		<div v-loading="computedLoading" class="pb-base">
			<BizFormForm :model="model" :rules="rules" @submit="handleSubmit">
				<div class="gap-x-base grid grid-cols-2">
								<XFormItem label="车辆ID" prop="vehicleId">
									<XInput v-model="model.vehicleId" placeholder="请输入车辆ID"  @keyup.enter.stop="handleSubmit" />
								</XFormItem>
								<XFormItem label="GPS时间" prop="gpsTime">
									<XDateTimePicker v-model="model.gpsTime" type="date" placeholder="选择GPS时间" />
								</XFormItem>
								<XFormItem label="经度" prop="lng">
									<XInput v-model="model.lng" placeholder="请输入经度"  @keyup.enter.stop="handleSubmit" />
								</XFormItem>
								<XFormItem label="纬度" prop="lat">
									<XInput v-model="model.lat" placeholder="请输入纬度"  @keyup.enter.stop="handleSubmit" />
								</XFormItem>
								<XFormItem label="速度（km/h）" prop="speed">
									<XInput v-model="model.speed" placeholder="请输入速度（km/h）"  @keyup.enter.stop="handleSubmit" />
								</XFormItem>
								<XFormItem label="方向（0-360度）" prop="direction">
									<XInput v-model="model.direction" placeholder="请输入方向（0-360度）"  @keyup.enter.stop="handleSubmit" />
								</XFormItem>
				</div>
			</BizFormForm>
		</div>
	</BizModalsSave>
</template>
<script setup lang="ts">
	const visible = ref(false)
	const model = ref({
						id: undefined,
						vehicleId: undefined,
						gpsTime: undefined,
						lng: undefined,
						lat: undefined,
						speed: undefined,
						direction: undefined,
	})
	const rules = reactive({
					vehicleId: [{ required: true, message: '车辆ID不能为空' }],
					lng: [{ required: true, message: '经度不能为空' }],
					lat: [{ required: true, message: '纬度不能为空' }],
	})
	const emit = defineEmits(['success'])
	const title = ref('')
	let type = ref('')
	const id = ref(0)
	const detailLoading = ref(false)
	const requestLoading = ref(false)
	let run, data
	const computedLoading = computed(() => requestLoading?.value || detailLoading.value)
	const params = ref({})
	function open(_type, _id, _params) {
		model.value = {}
		params.value = _params
		if (_id) {
			getDetail(_id)
		}
		visible.value = true
		type.value = _type
		title.value = _type === 'create' ? '新增' : '编辑'
		id.value = _id
		let { run: _run, loading, data: _data  } = xUseRequest(BIZ_JnrmVehicleGps_APIS[_type], model)
		run = _run
		// 监听xUseRequest返回的loading状态，同步到组件级别的loading
		watchEffect(() => {
			requestLoading.value = loading.value
		})
		data = _data
	}

	async function getDetail(id) {
		detailLoading.value = true
		const res = await BIZ_JnrmVehicleGps_APIS.get(id).finally(() => (detailLoading.value = false))
		Object.assign(model.value, res)
	}

	defineExpose({ open })
	async function handleSubmit() {
		if (!visible.value) return
		await run()
		visible.value = false
		if (data && type.value == 'create') {
			model.value.id = data
		}
		emit('success', model.value)
	}
</script>

<style scoped></style>
