<template>
	<BizModalsSave v-model="visible" :title="title">
		<div v-loading="computedLoading" class="pb-base">
			<BizFormForm :model="model" :rules="rules" @submit="handleSubmit">
				<div class="gap-x-base grid grid-cols-2">
								<XFormItem label="铅封编号" prop="sealNo">
									<XInput v-model="model.sealNo" placeholder="请输入铅封编号"  @keyup.enter.stop="handleSubmit" />
								</XFormItem>
								<XFormItem label="事件代码" prop="eventCode">
									<XInput v-model="model.eventCode" placeholder="请输入事件代码"  @keyup.enter.stop="handleSubmit" />
								</XFormItem>
								<XFormItem label="电量（百分比0-100）" prop="batteryLevel">
									<XInput v-model="model.batteryLevel" placeholder="请输入电量（百分比0-100）"  @keyup.enter.stop="handleSubmit" />
								</XFormItem>
								<XFormItem label="车牌号" prop="licensePlate">
									<XInput v-model="model.licensePlate" placeholder="请输入车牌号"  @keyup.enter.stop="handleSubmit" />
								</XFormItem>
								<XFormItem label="经度" prop="longitude">
									<XInput v-model="model.longitude" placeholder="请输入经度"  @keyup.enter.stop="handleSubmit" />
								</XFormItem>
								<XFormItem label="纬度" prop="latitude">
									<XInput v-model="model.latitude" placeholder="请输入纬度"  @keyup.enter.stop="handleSubmit" />
								</XFormItem>
								<XFormItem label="事件时间" prop="eventTime">
									<XDateTimePicker v-model="model.eventTime" type="date" placeholder="选择事件时间" />
								</XFormItem>
				</div>
			</BizFormForm>
		</div>
	</BizModalsSave>
</template>
<script setup lang="ts">
	const visible = ref(false)
	const model = ref({
						id: undefined,
						sealNo: undefined,
						eventCode: undefined,
						batteryLevel: undefined,
						licensePlate: undefined,
						longitude: undefined,
						latitude: undefined,
						eventTime: undefined,
	})
	const rules = reactive({
					sealNo: [{ required: true, message: '铅封编号不能为空' }],
					eventCode: [{ required: true, message: '事件代码不能为空' }],
					licensePlate: [{ required: true, message: '车牌号不能为空' }],
					eventTime: [{ required: true, message: '事件时间不能为空' }],
	})
	const emit = defineEmits(['success'])
	const title = ref('')
	let type = ref('')
	const id = ref(0)
	const detailLoading = ref(false)
	const requestLoading = ref(false)
	let run, data
	const computedLoading = computed(() => requestLoading?.value || detailLoading.value)
	const params = ref({})
	function open(_type, _id, _params) {
		model.value = {}
		params.value = _params
		if (_id) {
			getDetail(_id)
		}
		visible.value = true
		type.value = _type
		title.value = _type === 'create' ? '新增' : '编辑'
		id.value = _id
		let { run: _run, loading, data: _data  } = xUseRequest(BIZ_JnrmSealEvent_APIS[_type], model)
		run = _run
		// 监听xUseRequest返回的loading状态，同步到组件级别的loading
		watchEffect(() => {
			requestLoading.value = loading.value
		})
		data = _data
	}

	async function getDetail(id) {
		detailLoading.value = true
		const res = await BIZ_JnrmSealEvent_APIS.get(id).finally(() => (detailLoading.value = false))
		Object.assign(model.value, res)
	}

	defineExpose({ open })
	async function handleSubmit() {
		if (!visible.value) return
		await run()
		visible.value = false
		if (data && type.value == 'create') {
			model.value.id = data
		}
		emit('success', model.value)
	}
</script>

<style scoped></style>
