<template>
	<BizModalsSave v-model="visible" :title="title">
		<div v-loading="computedLoading" class="pb-base">
			<BizFormForm :model="model" :rules="rules" @submit="handleSubmit">
				<div class="grid grid-cols-2 gap-x-base">
					<XFormItem label="角色名称" prop="name">
						<XInput v-model="model.name" placeholder="请输入角色名称" />
					</XFormItem>
					<XFormItem label="角色标识" prop="code">
						<XInput v-model="model.code" placeholder="请输入角色权限字符串" />
					</XFormItem>
					<XFormItem label="显示顺序" prop="sort">
						<XInput v-model="model.sort" placeholder="请输入显示顺序" />
					</XFormItem>
					<!--					<XFormItem label="角色状态（0正常 1停用）" prop="status">
						<XRadioGroup v-model="model.status" tag-group-name="" />
					</XFormItem>-->
					<!--					<XFormItem label="角色类型" prop="type">
						<XSelect v-model="model.type" placeholder="请选择角色类型" tag-group-name="" />
					</XFormItem>-->
					<XFormItem label="备注" prop="remark">
						<XInput v-model="model.remark" placeholder="请输入备注" />
					</XFormItem>
				</div>
			</BizFormForm>
		</div>
	</BizModalsSave>
</template>
<script setup lang="ts">
const visible = ref(false)
const model = ref({
	id: undefined,
	name: undefined,
	code: undefined,
	sort: undefined,
	dataScope: undefined,
	dataScopeDeptIds: undefined,
	status: undefined,
	type: undefined,
	remark: undefined,
})
const rules = reactive({
	name: [{ required: true, message: '角色名称不能为空' }],
	code: [{ required: true, message: '角色权限字符串不能为空' }],
	sort: [{ required: true, message: '显示顺序不能为空' }],
	dataScope: [{ required: true, message: '数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限）不能为空' }],
	dataScopeDeptIds: [{ required: true, message: '数据范围(指定部门数组)不能为空' }],
	status: [{ required: true, message: '角色状态（0正常 1停用）不能为空' }],
	type: [{ required: true, message: '角色类型不能为空' }],
})
const emit = defineEmits(['success'])
const title = ref('')
let type = ref('')
const id = ref(0)
const detailLoading = ref(false)
let run, loading
const computedLoading = computed(() => loading?.value || detailLoading.value)
function open(_type, _id) {
	model.value = {}
	if (_id) {
		getDetail(_id)
	}
	visible.value = true
	type.value = _type
	title.value = _type === 'create' ? '新增' : '编辑'
	id.value = _id
	let { run: _run, loading: _loading } = xUseRequest(BIZ_Role_APIS[_type], model)
	run = _run
	loading = _loading
}

async function getDetail(id) {
	detailLoading.value = true
	const res = await BIZ_Role_APIS.get(id).finally(() => (detailLoading.value = false))
	Object.assign(model.value, res)
}

defineExpose({ open })
async function handleSubmit() {
	await run()
	visible.value = false
	emit('success')
}
</script>

<style scoped></style>
