<template>
	<BizModalsSave v-model="visible" :title="title">
		<div v-loading="computedLoading" class="pb-base">
			<BizFormForm :model="model" :rules="rules" @submit="handleSubmit">
				<div class="grid grid-cols-1 gap-x-base">
					<div class="grid grid-cols-2 gap-x-base">
						<XFormItem label="地点名称" prop="name">
							<XInput v-model="model.name" placeholder="请输入地点名称" @keyup.enter.stop="handleSubmit" />
						</XFormItem>
						<XFormItem label="类型" prop="factoryType">
							<XSelect v-model="model.factoryType" placeholder="请选择类型" tag-group-name="JNRM部门类型" />
						</XFormItem>
					</div>
					<XFormItem label="详细地址" prop="address">
						<XInput v-model="model.address" placeholder="请输入详细地址" @keyup.enter.stop="handleSubmit" />
					</XFormItem>
					<div class="grid grid-cols-2 gap-x-base">
						<XFormItem label="经度" prop="longitude">
							<XInput v-model="model.longitude" placeholder="请输入经度" @keyup.enter.stop="handleSubmit" />
						</XFormItem>
						<XFormItem label="纬度" prop="latitude">
							<XInput v-model="model.latitude" placeholder="请输入纬度" @keyup.enter.stop="handleSubmit" />
						</XFormItem>
					</div>
					<XFormItem label="电子围栏" prop="address1">
						<XInput v-model="model.address1" placeholder="请传入电子围栏数据" @keyup.enter.stop="handleSubmit" />
					</XFormItem>
				</div>
			</BizFormForm>
		</div>
	</BizModalsSave>
</template>
<script setup lang="ts">
const visible = ref(false)
const model = ref({
	id: undefined,
	name: undefined,
	address: undefined,
	longitude: undefined,
	latitude: undefined,
	type: undefined,
	factoryType: undefined,
})
const rules = reactive({
	name: [{ required: true, message: '地点名称不能为空' }],
	factoryType: [{ required: true, message: '类型不能为空' }],
})
const emit = defineEmits(['success'])
const title = ref('')
let type = ref('')
const id = ref(0)
const detailLoading = ref(false)
const requestLoading = ref(false)
let run, data
const computedLoading = computed(() => requestLoading?.value || detailLoading.value)
const params = ref({})
function open(_type, _id, _params) {
	model.value = {}
	params.value = _params
	if (_id) {
		getDetail(_id)
	}
	visible.value = true
	type.value = _type
	title.value = _type === 'create' ? '新增' : '编辑'
	id.value = _id
	let { run: _run, loading, data: _data } = xUseRequest(BIZ_JnrmLocation_APIS[_type], model)
	run = _run
	// 监听xUseRequest返回的loading状态，同步到组件级别的loading
	watchEffect(() => {
		requestLoading.value = loading.value
	})
	data = _data
}

async function getDetail(id) {
	detailLoading.value = true
	const res = await BIZ_JnrmLocation_APIS.get(id).finally(() => (detailLoading.value = false))
	Object.assign(model.value, res)
}

defineExpose({ open })
async function handleSubmit() {
	if (!visible.value) return
	await run()
	visible.value = false
	if (data && type.value == 'create') {
		model.value.id = data
	}
	emit('success', model.value)
}
</script>

<style scoped></style>
