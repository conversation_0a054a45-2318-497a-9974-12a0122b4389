<template>
	<BizModalsSave v-model="visible" :title="title">
		<div v-loading="computedLoading" class="pb-base">
			<BizFormForm :model="model" :rules="rules" @submit="handleSubmit">
				<div class="grid grid-cols-2 gap-x-base">
					<XFormItem label="配置编号" prop="configId">
						<XInput v-model="model.configId" placeholder="请输入配置编号" @keyup.enter.stop="handleSubmit" />
					</XFormItem>
					<XFormItem label="文件名" prop="name">
						<XInput v-model="model.name" placeholder="请输入文件名" @keyup.enter.stop="handleSubmit" />
					</XFormItem>
					<XFormItem label="文件路径" prop="path">
						<XInput v-model="model.path" placeholder="请输入文件路径" @keyup.enter.stop="handleSubmit" />
					</XFormItem>
					<XFormItem label="文件 URL" prop="url">
						<XInput v-model="model.url" placeholder="请输入文件 URL" @keyup.enter.stop="handleSubmit" />
					</XFormItem>
					<XFormItem label="文件类型" prop="type">
						<XSelect v-model="model.type" placeholder="请选择文件类型" tag-group-name="" />
					</XFormItem>
					<XFormItem label="文件大小" prop="size">
						<XInput v-model="model.size" placeholder="请输入文件大小" @keyup.enter.stop="handleSubmit" />
					</XFormItem>
				</div>
			</BizFormForm>
		</div>
	</BizModalsSave>
</template>
<script setup lang="ts">
const visible = ref(false)
const model = ref({
	id: undefined,
	configId: undefined,
	name: undefined,
	path: undefined,
	url: undefined,
	type: undefined,
	size: undefined,
})
const rules = reactive({
	path: [{ required: true, message: '文件路径不能为空' }],
	url: [{ required: true, message: '文件 URL不能为空' }],
	size: [{ required: true, message: '文件大小不能为空' }],
})
const emit = defineEmits(['success'])
const title = ref('')
let type = ref('')
const id = ref(0)
const detailLoading = ref(false)
let run, loading
const computedLoading = computed(() => loading?.value || detailLoading.value)
const params = ref({})
function open(_type, _id, _params) {
	model.value = {}
	params.value = _params
	if (_id) {
		getDetail(_id)
	}
	visible.value = true
	type.value = _type
	title.value = _type === 'create' ? '新增' : '编辑'
	id.value = _id
	let { run: _run, loading: _loading } = xUseRequest(BIZ_File_APIS[_type], model)
	run = _run
	loading = _loading
}

async function getDetail(id) {
	detailLoading.value = true
	const res = await BIZ_File_APIS.get(id).finally(() => (detailLoading.value = false))
	Object.assign(model.value, res)
}

defineExpose({ open })
async function handleSubmit() {
	await run()
	visible.value = false
	emit('success', model.value)
}
</script>

<style scoped></style>
