<template>
	<BizModalsSave v-model="visible" :title="title">
		<div v-loading="computedLoading" class="pb-base">
			<BizFormForm :model="model" :rules="rules" @submit="handleSubmit">
				<div class="gap-x-base grid grid-cols-2">
								<XFormItem label="运单ID" prop="transportOrderId">
									<XInput v-model="model.transportOrderId" placeholder="请输入运单ID"  @keyup.enter.stop="handleSubmit" />
								</XFormItem>
								<XFormItem label="车辆ID" prop="vehicleId">
									<XInput v-model="model.vehicleId" placeholder="请输入车辆ID"  @keyup.enter.stop="handleSubmit" />
								</XFormItem>
								<XFormItem label="铅封ID" prop="sealId">
									<XInput v-model="model.sealId" placeholder="请输入铅封ID"  @keyup.enter.stop="handleSubmit" />
								</XFormItem>
				</div>
			</BizFormForm>
		</div>
	</BizModalsSave>
</template>
<script setup lang="ts">
	const visible = ref(false)
	const model = ref({
						id: undefined,
						transportOrderId: undefined,
						vehicleId: undefined,
						sealId: undefined,
	})
	const rules = reactive({
					transportOrderId: [{ required: true, message: '运单ID不能为空' }],
					vehicleId: [{ required: true, message: '车辆ID不能为空' }],
					sealId: [{ required: true, message: '铅封ID不能为空' }],
	})
	const emit = defineEmits(['success'])
	const title = ref('')
	let type = ref('')
	const id = ref(0)
	const detailLoading = ref(false)
	const requestLoading = ref(false)
	let run, data
	const computedLoading = computed(() => requestLoading?.value || detailLoading.value)
	const params = ref({})
	function open(_type, _id, _params) {
		model.value = {}
		params.value = _params
		if (_id) {
			getDetail(_id)
		}
		visible.value = true
		type.value = _type
		title.value = _type === 'create' ? '新增' : '编辑'
		id.value = _id
		let { run: _run, loading, data: _data  } = xUseRequest(BIZ_JnrmVehicleSeal_APIS[_type], model)
		run = _run
		// 监听xUseRequest返回的loading状态，同步到组件级别的loading
		watchEffect(() => {
			requestLoading.value = loading.value
		})
		data = _data
	}

	async function getDetail(id) {
		detailLoading.value = true
		const res = await BIZ_JnrmVehicleSeal_APIS.get(id).finally(() => (detailLoading.value = false))
		Object.assign(model.value, res)
	}

	defineExpose({ open })
	async function handleSubmit() {
		if (!visible.value) return
		await run()
		visible.value = false
		if (data && type.value == 'create') {
			model.value.id = data
		}
		emit('success', model.value)
	}
</script>

<style scoped></style>
