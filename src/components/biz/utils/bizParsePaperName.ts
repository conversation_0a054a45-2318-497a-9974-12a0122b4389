/**
 * 文件名解析工具 - 用于从试卷文件名中提取元数据信息
 */

interface ParsedFileInfo {
	province?: string
	city?: string
	district?: string
	school?: string // 学校名称
	subject?: string
	stage?: string
	category?: string
	year?: string
	semester?: string // 学期
	originalFileName: string
}

/**
 * 字典数据 - 用于匹配各类信息
 */
// 地区字典 (省/市/区)
const REGION_DICT = ['上海', '北京']

// 区县字典 (只包含主要城市的部分区县示例，实际使用需要更完整的数据)
const DISTRICT_DICT = [
	// 上海所有的区
	'黄浦',
	'徐汇',
	'长宁',
	'静安',
	'普陀',
	'虹口',
	'杨浦',
	'闵行',
	'宝山',
	'嘉定',
	'浦东',
	'金山',
	'松江',
	'青浦',
	'奉贤',
	'崇明',
]

// 学科字典
const SUBJECT_DICT = '语文|数学|英语|物理|化学|生物|道法|历史|地理|科学'

// 学段字典 - 按匹配优先级排序
const STAGE_DICT = '高三|高二|高一|初三|初二|初一|小六|小五|小四|小三|小二|小一'

// 类别字典 - 按匹配优先级排序
const CATEGORY_DICT = '真题|期末|期中|一模|二模|三模|月考|合格考试|分班考试|真题汇编|期中汇编|期末汇编|一模汇编|二模汇编|合格考汇编'

// 学期字典
const SEMESTER_DICT = ['上学期', '下学期', '第一学期', '第二学期', '上册', '下册']

// 学期到简写的映射
const SEMESTER_ABBR_MAP: Record<string, string> = {
	上学期: '(上)',
	下学期: '(下)',
	第一学期: '(上)',
	第二学期: '(下)',
	上册: '(上)',
	下册: '(下)',
}

// 年份正则模式 - 匹配四位数字（1900-2099）
const YEAR_PATTERN = /\b(19\d{2}|20\d{2})\b/

// 学校名称正则 - 寻找包含"xx中学"、"xx学校"等模式的文本
const SCHOOL_PATTERN = /([^\s\d]+(?:中学|小学|学校|大学|学院|职业学校|一中|二中|三中|四中|五中))/

/**
 * 智能解析文件名中的元数据信息
 * @param fileName 文件名
 * @returns 解析后的文件信息对象
 */
export function extractDataFromName(fileName: string): ParsedFileInfo {
	// 预处理文件名：移除扩展名、清理特殊字符
	const cleanFileName = cleanName(fileName)

	// 提取元数据
	const result: ParsedFileInfo = {
		originalFileName: fileName,
	}

	// 提取年份
	const yearMatch = cleanFileName.match(YEAR_PATTERN)
	if (yearMatch) {
		result.year = yearMatch[0]
	}

	// 提取地区信息（省/市/区）
	extractRegionInfo(cleanFileName, result)

	// 提取学校名称
	extractSchoolName(cleanFileName, result)

	// 提取学科
	result.subject = findMatch(cleanFileName, SUBJECT_DICT)

	// 提取学段
	result.stage = findMatch(cleanFileName, STAGE_DICT)

	// 先提取学期，避免学期与类别冲突
	extractSemesterInfo(cleanFileName, result)

	// 提取类别
	extractCategoryInfo(cleanFileName, result)

	return result
}

/**
 * 清理文件名，移除扩展名和特殊字符
 */
function cleanName(fileName: string): string {
	// 移除扩展名
	let name = fileName.replace(/\.[^.]+$/, '')

	// 移除常见的附加标注，如"(含答案)"等
	name = name.replace(/\([^)]*\)/g, ' ')
	name = name.replace(/（[^）]*）/g, ' ') // 中文括号

	// 移除常见的后缀标注
	name = name.replace(/试题|试卷|答案|解析|附答案|附解析/g, ' ')

	// 移除"届"的标记，比如"2024届"
	name = name.replace(/\d+届/g, ' ')

	// 标准化空格和特殊字符
	name = name.replace(/[_\-~+]/g, ' ')
	name = name.replace(/\s+/g, ' ').trim()

	return name
}

/**
 * 从字符串中提取地区信息
 */
function extractRegionInfo(text: string, result: ParsedFileInfo): void {
	// 检查并处理直辖市，如上海、北京等
	for (const region of REGION_DICT) {
		if (text.includes(region)) {
			// 对于直辖市，既是省级也是市级
			result.province = region
			result.city = region

			// 尝试匹配区县，仅从省后面的内容匹配
			const regionIndex = text.indexOf(region)
			const afterRegion = text.substring(regionIndex + region.length)

			for (const district of DISTRICT_DICT) {
				if (afterRegion.includes(district)) {
					result.district = district
					break
				}
			}

			break // 找到省后跳出循环
		}
	}

	// 如果没找到省，再尝试直接匹配地级市或区县
	if (!result.province) {
		for (const district of DISTRICT_DICT) {
			if (text.includes(district)) {
				if (['市', '区', '县'].some((suffix) => text.includes(district + suffix))) {
					result.district = district
				} else {
					result.city = district
				}
				break
			}
		}
	}
}

/**
 * 提取学校名称
 */
function extractSchoolName(text: string, result: ParsedFileInfo): void {
	// 首先尝试匹配特定的学校名称（硬编码优先级）
	const specificSchools = ['大同中学', '曹杨第二中学', '延安中学']

	for (const school of specificSchools) {
		if (text.includes(school)) {
			result.school = school
			return
		}
	}

	// 移除年份，避免将年份纳入学校名称
	let cleanedText = text
	if (result.year) {
		cleanedText = cleanedText.replace(result.year, ' ')
	}

	// 使用更精确的正则表达式匹配学校名称，排除数字开头
	const schoolMatch = cleanedText.match(SCHOOL_PATTERN)
	if (schoolMatch) {
		result.school = schoolMatch[0]
		return
	}

	// 匹配特定学校模式，如"xx中学"，"xx第x中学"等
	const schoolPatterns = [
		/([^\s\d]+(?:中学|小学|学校|大学|学院|职业学校))/, // 匹配"xx中学"格式，排除数字
		/([^\s\d]+第[一二三四五六七八九十\d]+(?:中学|小学|学校))/, // 匹配"xx第x中学"格式，排除数字开头
	]

	for (const pattern of schoolPatterns) {
		const match = cleanedText.match(pattern)
		if (match) {
			result.school = match[0]
			return
		}
	}
}

/**
 * 提取学期信息
 */
function extractSemesterInfo(text: string, result: ParsedFileInfo): void {
	// 通用匹配模式
	for (const semester of SEMESTER_DICT) {
		if (text.includes(semester)) {
			result.semester = semester
			return
		}
	}

	// 特殊处理匹配模式
	if (text.includes('上学期')) {
		result.semester = '上学期'
	} else if (text.includes('上期')) {
		result.semester = '上学期'
	} else if (text.includes('下学期')) {
		result.semester = '下学期'
	} else if (text.includes('下期')) {
		result.semester = '下学期'
	}
}

/**
 * 提取考试类型/类别信息
 */
function extractCategoryInfo(text: string, result: ParsedFileInfo): void {
	// 基础类别匹配
	let baseCategory = null

	// 先尝试匹配期中/期末
	if (text.includes('期末')) {
		baseCategory = '期末'
	} else if (text.includes('期中')) {
		baseCategory = '期中'
	} else {
		// 如果不是期中/期末，尝试匹配其他类别
		const categoryPatterns = CATEGORY_DICT.split('|')
		const matches = []

		for (const pattern of categoryPatterns) {
			if (pattern && text.includes(pattern)) {
				matches.push(pattern)
			}
		}

		// 如果有匹配项，使用最长的匹配
		if (matches.length > 0) {
			baseCategory = matches.sort((a, b) => b.length - a.length)[0]
		}
	}

	// 如果没有找到类别，直接返回
	if (!baseCategory) {
		return
	}

	// 根据学期信息组装完整的类别
	if (result.semester) {
		// 获取学期的简写形式
		const semesterAbbr = SEMESTER_ABBR_MAP[result.semester]
		if (semesterAbbr) {
			// 将学期简写与类别组合
			result.category = `${semesterAbbr}${baseCategory}`
		} else {
			result.category = baseCategory
		}
	} else {
		result.category = baseCategory
	}
}

/**
 * 在文本中查找字典匹配项
 */
function findMatch(text: string, dictionary: string[] | string): string | undefined {
	if (Array.isArray(dictionary)) {
		for (const item of dictionary) {
			if (text.includes(item)) {
				return item
			}
		}
	} else if (typeof dictionary === 'string' && dictionary.includes('|')) {
		// 如果是以竖线分隔的字符串，将其拆分成数组并进行匹配
		const patterns = dictionary.split('|')
		// 记录所有匹配项
		const matches = []

		for (const pattern of patterns) {
			if (pattern && text.includes(pattern)) {
				matches.push(pattern)
			}
		}

		// 如果有匹配项，返回最长的匹配（通常更具体）
		if (matches.length > 0) {
			return matches.sort((a, b) => b.length - a.length)[0]
		}
	} else if (typeof dictionary === 'string') {
		// 如果是普通字符串，直接判断是否包含
		if (text.includes(dictionary)) {
			return dictionary
		}
	}

	return undefined
}

export const BIZ_PAPER_UTILS = {
	extractDataFromName,
}
