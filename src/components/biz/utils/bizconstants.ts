export const BIZ_CateId0_CONSTANTS = [
	{
		value: '行测',
		label: '行测',
		id: 1,
		description: '行测专项练习',
		icon: 'i-carbon:chart-bar', // 图表图标，适合表示行测
		bg: 'from-blue-600/60 to-blue-700/60',
		action: () => {
			goZsjyQuestionBank({ cateId0: 1 })
		},
	},
	{
		value: '申论',
		label: '申论',
		id: 2,
		description: '申论专项练习',
		icon: 'i-carbon:document', // 文档编辑图标，适合表示申论写作
		bg: 'from-green-600/60 to-green-700/60',
		action: () => {
			goZsjyQuestionBank({ cateId0: 2 })
		},
	},
	{
		value: '公安专业知识',
		label: '公安专业知识',
		id: 15,
		description: '公安专业知识专项练习',
		icon: 'i-carbon:security', // 安全/警徽图标，适合表示公安专业
		bg: 'from-purple-600/60 to-purple-700/60',
		action: () => {
			goZsjyQuestionBank({ cateId0: 15 })
		},
	},
]
export const BIZ_MasterLevels_CONSTATNTS = {
	0: { value: 0, label: '未学习', icon: 'i-carbon:face-dissatisfied', color: '!color-danger-600', bg: '!bg-danger-600' },
	1: { value: 1, label: '初学', icon: 'i-carbon:face-neutral', color: '!color-warning-600', bg: '!bg-warning-600' },
	3: { value: 3, label: '熟悉', icon: 'i-carbon:face-satisfied', color: '!color-success-700', bg: '!bg-success-700' },
	5: { value: 5, label: '精通', icon: 'i-carbon:face-cool', color: '!color-success-900', bg: '!bg-success-900' },
}
export const BIZ_QuestionStatus_CONSTANTS = {
	wrong: {
		bg: '!bg-danger-400',
		text: 'text-red-600',
		border: 'border-red-200',
		icon: 'i-carbon:warning-alt',
		label: '错题',
	},
	correct: {
		bg: '!bg-success-700',
		text: 'text-green-600',
		border: 'border-green-200',
		icon: 'i-carbon:checkmark',
		label: '正确',
	},
	unanswered: {
		bg: 'bg-gray-400',
		text: 'text-gray-600',
		border: 'border-gray-200',
		icon: 'i-carbon:warning',
		label: '未答',
	},
}
export const Biz_PaperType_CONTANTS = [
	{
		value: '历年真题',
		label: '历年真题',
		description: '真实考题练习，掌握命题方向，提高通过率',
		icon: 'i-carbon:document',
		bg: 'from-green-600/60 to-green-700/60',
	},
	{
		value: '全真模考',
		label: '全真模考',
		description: '完整还原考试环境，限时作答，提升应试能力',
		icon: 'i-carbon:exam-mode',
		bg: 'from-purple-600/60 to-purple-700/60',
	},
]
// 答案类型
export const BIZ_AnswerType_CONSTANTS = {
	单选: '单选',
	多选: '多选',
	主观题: '主观题',
} as const
// 考试状态常量
export const BIZ_ExamStatus_CONSTANTS = {
	待参加: '待参加',
	规则阅读: '规则阅读',
	答题中: '答题中',
	已交卷: '已交卷',
} as const
export const BIZ_ExamConfig_CONSTANTS = {
	defaultCateTimeLimit: 5 * 60,
}
export const BIZ_ExamCateRules_CONSTANTS = {
	知觉速度: {
		rule:
			'<h3 style="margin: 0px 0px 55px; padding: 0px; font-family: 方正小标宋简体; font-weight: 400; font-size: 24px; text-align: center; line-height: 30px; ">&ldquo;知觉速度与准确性&rdquo;说明</h3>\n' +
			'<p style="font-family: 宋体; font-size: 21px; text-indent: 32px; line-height: 40px; overflow: hidden; ">本部分共60题，时限10分钟，其中阅读说明2分钟，作答8分钟。此部分固定在第一部分作答，不允许先作答其他部分。阅读说明满2分钟后，可开始答题，作答满8分钟后方能继续作答其他部分。</p>\n' +
			'<p style="font-family: 宋体; font-size: 21px; text-indent: 32px; line-height: 40px; overflow: hidden; "><span style="font-weight: bold;">本部分每道试题分值相同，采用倒扣分的方式计分，扣完本部分总分为止。答对则得分，答错倒扣分，不答不得分。</span></p>\n' +
			'<p style="font-family: 宋体; font-size: 21px; text-indent: 32px; line-height: 40px; overflow: hidden; "><span style="font-weight: bold;">本部分包括三种类型的题目：</span></p>\n' +
			'<p style="font-family: 宋体; font-size: 21px; text-indent: 32px; line-height: 40px; overflow: hidden; ">&nbsp;</p>\n' +
			'<p style="font-family: 宋体; font-size: 21px; text-indent: 32px; line-height: 40px; overflow: hidden; ">第一种类型的题目：定位数字区间</p>\n' +
			'<p style="font-family: 宋体; font-size: 21px; text-indent: 32px; line-height: 40px; overflow: hidden; ">给定一个包含有一些数字区间的表格，让你确定某一给定的数字属于表格中的哪一数字区间，这一区间的标号就是正确答案。<br />例题：</p>\n' +
			'<table style="border-spacing: 0px; width: 500px; margin: 50px auto; background: #ffffff; font-family: \'Microsoft YaHei\';">\n' +
			'<tbody>\n' +
			'<tr>\n' +
			'<td style="font-family: 宋体;  cursor: default; line-height: 30px; font-size: 16px; font-weight: bold; border: 1px solid #000000; text-align: center;">①</td>\n' +
			'<td style="font-family: 宋体;  cursor: default; line-height: 30px; font-size: 16px; font-weight: bold; border: 1px solid #000000; text-align: center;">②</td>\n' +
			'<td style="font-family: 宋体;  cursor: default; line-height: 30px; font-size: 16px; font-weight: bold; border: 1px solid #000000; text-align: center;">③</td>\n' +
			'<td style="font-family: 宋体;  cursor: default; line-height: 30px; font-size: 16px; font-weight: bold; border: 1px solid #000000; text-align: center;">④</td>\n' +
			'<td style="font-family: 宋体;  cursor: default; line-height: 30px; font-size: 16px; font-weight: bold; border: 1px solid #000000; text-align: center;">⑤</td>\n' +
			'</tr>\n' +
			'<tr>\n' +
			'<td style="font-family: 宋体;  cursor: default; line-height: 30px; font-size: 16px; font-weight: bold; border: 1px solid #000000; text-align: center;">3871~4783</td>\n' +
			'<td style="font-family: 宋体;  cursor: default; line-height: 30px; font-size: 16px; font-weight: bold; border: 1px solid #000000; text-align: center;">2134~2765</td>\n' +
			'<td style="font-family: 宋体;  cursor: default; line-height: 30px; font-size: 16px; font-weight: bold; border: 1px solid #000000; text-align: center;">5763~6438</td>\n' +
			'<td style="font-family: 宋体;  cursor: default; line-height: 30px; font-size: 16px; font-weight: bold; border: 1px solid #000000; text-align: center;">8538~9921</td>\n' +
			'<td style="font-family: 宋体;  cursor: default; line-height: 30px; font-size: 16px; font-weight: bold; border: 1px solid #000000; text-align: center;">0853~2093</td>\n' +
			'</tr>\n' +
			'<tr>\n' +
			'<td style="font-family: 宋体;  cursor: default; line-height: 30px; font-size: 16px; font-weight: bold; border: 1px solid #000000; text-align: center;">7838~8420</td>\n' +
			'<td style="font-family: 宋体;  cursor: default; line-height: 30px; font-size: 16px; font-weight: bold; border: 1px solid #000000; text-align: center;">4921~5636</td>\n' +
			'<td style="font-family: 宋体;  cursor: default; line-height: 30px; font-size: 16px; font-weight: bold; border: 1px solid #000000; text-align: center;">7112~7789</td>\n' +
			'<td style="font-family: 宋体;  cursor: default; line-height: 30px; font-size: 16px; font-weight: bold; border: 1px solid #000000; text-align: center;">6780~7093</td>\n' +
			'<td style="font-family: 宋体;  cursor: default; line-height: 30px; font-size: 16px; font-weight: bold; border: 1px solid #000000; text-align: center;">6448~6721</td>\n' +
			'</tr>\n' +
			'</tbody>\n' +
			'</table>\n' +
			'<div class="topic" style="font-family: 宋体; ">\n' +
			'<p style="font-size: 15px; line-height: 40px; overflow: hidden;">例题1．6623</p>\n' +
			'<p style="font-size: 15px; line-height: 40px; overflow: hidden;">&nbsp;</p>\n' +
			'<ul style="margin: 0px; padding: 0px 0px 0px 30px; font-family: \'Microsoft YaHei\'; list-style: none; overflow: hidden; line-height: 30px;">\n' +
			'<li style="margin: 0px 25px 0px 0px; padding: 0px; font-family: 宋体; float: left;">A．①</li>\n' +
			'<li style="margin: 0px 25px 0px 0px; padding: 0px; font-family: 宋体; float: left;">B．②</li>\n' +
			'<li style="margin: 0px 25px 0px 0px; padding: 0px; font-family: 宋体; float: left;">C．③</li>\n' +
			'<li style="margin: 0px 25px 0px 0px; padding: 0px; font-family: 宋体; float: left;">D．④</li>\n' +
			'<li style="margin: 0px 25px 0px 0px; padding: 0px; font-family: 宋体; float: left;">E．⑤</li>\n' +
			'</ul>\n' +
			'<p style="font-size: 15px; line-height: 40px; overflow: hidden;">&nbsp;</p>\n' +
			'</div>\n' +
			'<div class="topic" style="font-family: 宋体; ">\n' +
			'<p style="font-size: 15px; line-height: 40px; overflow: hidden;">例题2．5022</p>\n' +
			'<p style="font-size: 15px; line-height: 40px; overflow: hidden;">&nbsp;</p>\n' +
			'<ul style="margin: 0px; padding: 0px 0px 0px 30px; font-family: \'Microsoft YaHei\'; list-style: none; overflow: hidden; line-height: 30px;">\n' +
			'<li style="margin: 0px 25px 0px 0px; padding: 0px; font-family: 宋体; float: left;">A．①</li>\n' +
			'<li style="margin: 0px 25px 0px 0px; padding: 0px; font-family: 宋体; float: left;">B．②</li>\n' +
			'<li style="margin: 0px 25px 0px 0px; padding: 0px; font-family: 宋体; float: left;">C．③</li>\n' +
			'<li style="margin: 0px 25px 0px 0px; padding: 0px; font-family: 宋体; float: left;">D．④</li>\n' +
			'<li style="margin: 0px 25px 0px 0px; padding: 0px; font-family: 宋体; float: left;">E．⑤</li>\n' +
			'</ul>\n' +
			'<p style="font-size: 15px; line-height: 40px; overflow: hidden;">&nbsp;</p>\n' +
			'</div>\n' +
			'<div style="font-family: 宋体; ">解答：</div>\n' +
			'<p style="font-family: 宋体; font-size: 21px; text-indent: 32px; line-height: 40px; overflow: hidden; ">&nbsp;</p>\n' +
			'<ul style="margin: 0px; padding: 0px 0px 0px 30px; font-family: \'Microsoft YaHei\'; list-style: none; overflow: hidden; line-height: 30px; ">\n' +
			'<li style="margin: 0px 25px 0px 0px; padding: 0px; font-family: 宋体; float: left; width: 1834.94px;">题1的数字在6448~6721之间，所在区间的标号为⑤，故答案为E；</li>\n' +
			'<li style="margin: 0px 25px 0px 0px; padding: 0px; font-family: 宋体; float: left; width: 1834.94px;">题2的数字在4921~5636之间，所在区间的标号为②，故答案为B。</li>\n' +
			'</ul>\n' +
			'<p style="font-family: 宋体; font-size: 21px; text-indent: 32px; line-height: 40px; overflow: hidden; ">&nbsp;</p>\n' +
			'<p style="font-family: 宋体; font-size: 21px; text-indent: 32px; line-height: 40px; overflow: hidden; "><span style="font-weight: bold;">第二种类型的题目：寻找相同字符数量</span></p>\n' +
			'<p style="font-family: 宋体; font-size: 21px; text-indent: 32px; line-height: 40px; overflow: hidden; ">给定一个词表，要求你将每道题所给的五个词与词表中的词相对照，找出包含在词表中词的个数，如果所给的词没有一个包含在词表中，答案即为0。<br />例题：</p>\n' +
			'<div class="box" style="margin: 20px auto; padding: 0px; font-family: 宋体; border: 1px solid #000000; width: 340px; "><span style="display: inline-block; margin: 10px;">功德</span>&nbsp;<span style="display: inline-block; margin: 10px;">供奉</span>&nbsp;<span style="display: inline-block; margin: 10px;">解脱</span>&nbsp;<span style="display: inline-block; margin: 10px;">黄昏</span>&nbsp;<span style="display: inline-block; margin: 10px;">呜咽</span>&nbsp;<span style="display: inline-block; margin: 10px;">明晰</span>&nbsp;<span style="display: inline-block; margin: 10px;">承受</span>&nbsp;<span style="display: inline-block; margin: 10px;">痴迷</span>&nbsp;<span style="display: inline-block; margin: 10px;">贿赂</span>&nbsp;<span style="display: inline-block; margin: 10px;">讽刺</span>&nbsp;<span style="display: inline-block; margin: 10px;">风韵</span>&nbsp;<span style="display: inline-block; margin: 10px;">鼎盛</span>&nbsp;<span style="display: inline-block; margin: 10px;">违反</span>&nbsp;<span style="display: inline-block; margin: 10px;">垂青</span>&nbsp;<span style="display: inline-block; margin: 10px;">表白</span></div>\n' +
			'<div class="topic" style="font-family: 宋体; ">\n' +
			'<p style="font-size: 15px; line-height: 40px; overflow: hidden;">例题3．明晰　供养　功德　割爱　违犯</p>\n' +
			'<ul style="margin: 0px; padding: 0px 0px 0px 30px; font-family: \'Microsoft YaHei\'; list-style: none; overflow: hidden; line-height: 30px;">\n' +
			'<li style="margin: 0px 25px 0px 0px; padding: 0px; font-family: 宋体; float: left;">A．0</li>\n' +
			'<li style="margin: 0px 25px 0px 0px; padding: 0px; font-family: 宋体; float: left;">B．1</li>\n' +
			'<li style="margin: 0px 25px 0px 0px; padding: 0px; font-family: 宋体; float: left;">C．2</li>\n' +
			'<li style="margin: 0px 25px 0px 0px; padding: 0px; font-family: 宋体; float: left;">D．3</li>\n' +
			'<li style="margin: 0px 25px 0px 0px; padding: 0px; font-family: 宋体; float: left;">E．4</li>\n' +
			'</ul>\n' +
			'<p style="font-size: 15px; line-height: 40px; overflow: hidden;">例题4．垂直 解放 赇贿 表现 痴呆</p>\n' +
			'<ul style="margin: 0px; padding: 0px 0px 0px 30px; font-family: \'Microsoft YaHei\'; list-style: none; overflow: hidden; line-height: 30px;">\n' +
			'<li style="margin: 0px 25px 0px 0px; padding: 0px; font-family: 宋体; float: left;">A．0</li>\n' +
			'<li style="margin: 0px 25px 0px 0px; padding: 0px; font-family: 宋体; float: left;">B．1</li>\n' +
			'<li style="margin: 0px 25px 0px 0px; padding: 0px; font-family: 宋体; float: left;">C．2</li>\n' +
			'<li style="margin: 0px 25px 0px 0px; padding: 0px; font-family: 宋体; float: left;">D．3</li>\n' +
			'<li style="margin: 0px 25px 0px 0px; padding: 0px; font-family: 宋体; float: left;">E．4</li>\n' +
			'</ul>\n' +
			'</div>\n' +
			'<div style="font-family: 宋体; ">解答：</div>\n' +
			'<p style="font-family: 宋体; font-size: 21px; text-indent: 32px; line-height: 40px; overflow: hidden; ">&nbsp;</p>\n' +
			'<ul style="margin: 0px; padding: 0px 0px 0px 30px; font-family: \'Microsoft YaHei\'; list-style: none; overflow: hidden; line-height: 30px; ">\n' +
			'<li style="margin: 0px 25px 0px 0px; padding: 0px; font-family: 宋体; float: left; width: 1834.94px;">题3中&ldquo;明晰&rdquo;、&ldquo;功德&rdquo;包含在词表中，故包含在词表中词的个数为2，答案为C；</li>\n' +
			'<li style="margin: 0px 25px 0px 0px; padding: 0px; font-family: 宋体; float: left; width: 1834.94px;">题4中所给的词没有一个包含在词表中，故答案为A。</li>\n' +
			'</ul>\n' +
			'<p style="font-family: 宋体; font-size: 21px; text-indent: 32px; line-height: 40px; overflow: hidden; ">&nbsp;</p>\n' +
			'<p style="font-family: 宋体; font-size: 21px; text-indent: 32px; line-height: 40px; overflow: hidden; "><span style="font-weight: bold;">第三种类型的题目：字符替换核对</span></p>\n' +
			'<p style="font-family: 宋体; font-size: 21px; text-indent: 32px; line-height: 40px; overflow: hidden; ">给定一个数字或字母与汉字的对应表，要求你判断题目中用数字或字母替换汉字时，哪一个选项替换得完全正确。<br />例题：</p>\n' +
			'<table style="border-spacing: 0px; width: 500px; margin: 50px auto; background: #ffffff; font-family: \'Microsoft YaHei\';">\n' +
			'<tbody>\n' +
			'<tr>\n' +
			'<td style="font-family: 宋体;  cursor: default; line-height: 30px; font-size: 16px; font-weight: bold; border: 1px solid #000000; text-align: center;">立</td>\n' +
			'<td style="font-family: 宋体;  cursor: default; line-height: 30px; font-size: 16px; font-weight: bold; border: 1px solid #000000; text-align: center;">木</td>\n' +
			'<td style="font-family: 宋体;  cursor: default; line-height: 30px; font-size: 16px; font-weight: bold; border: 1px solid #000000; text-align: center;">川</td>\n' +
			'<td style="font-family: 宋体;  cursor: default; line-height: 30px; font-size: 16px; font-weight: bold; border: 1px solid #000000; text-align: center;">州</td>\n' +
			'<td style="font-family: 宋体;  cursor: default; line-height: 30px; font-size: 16px; font-weight: bold; border: 1px solid #000000; text-align: center;">大</td>\n' +
			'<td style="font-family: 宋体;  cursor: default; line-height: 30px; font-size: 16px; font-weight: bold; border: 1px solid #000000; text-align: center;">手</td>\n' +
			'<td style="font-family: 宋体;  cursor: default; line-height: 30px; font-size: 16px; font-weight: bold; border: 1px solid #000000; text-align: center;">太</td>\n' +
			'<td style="font-family: 宋体;  cursor: default; line-height: 30px; font-size: 16px; font-weight: bold; border: 1px solid #000000; text-align: center;">平</td>\n' +
			'</tr>\n' +
			'<tr>\n' +
			'<td style="font-family: 宋体;  cursor: default; line-height: 30px; font-size: 16px; font-weight: bold; border: 1px solid #000000; text-align: center;">L</td>\n' +
			'<td style="font-family: 宋体;  cursor: default; line-height: 30px; font-size: 16px; font-weight: bold; border: 1px solid #000000; text-align: center;">0</td>\n' +
			'<td style="font-family: 宋体;  cursor: default; line-height: 30px; font-size: 16px; font-weight: bold; border: 1px solid #000000; text-align: center;">6</td>\n' +
			'<td style="font-family: 宋体;  cursor: default; line-height: 30px; font-size: 16px; font-weight: bold; border: 1px solid #000000; text-align: center;">4</td>\n' +
			'<td style="font-family: 宋体;  cursor: default; line-height: 30px; font-size: 16px; font-weight: bold; border: 1px solid #000000; text-align: center;">Q</td>\n' +
			'<td style="font-family: 宋体;  cursor: default; line-height: 30px; font-size: 16px; font-weight: bold; border: 1px solid #000000; text-align: center;">7</td>\n' +
			'<td style="font-family: 宋体;  cursor: default; line-height: 30px; font-size: 16px; font-weight: bold; border: 1px solid #000000; text-align: center;">9</td>\n' +
			'<td style="font-family: 宋体;  cursor: default; line-height: 30px; font-size: 16px; font-weight: bold; border: 1px solid #000000; text-align: center;">P</td>\n' +
			'</tr>\n' +
			'</tbody>\n' +
			'</table>\n' +
			'<div class="topic" style="font-family: 宋体; ">\n' +
			'<p style="font-size: 15px; line-height: 40px; overflow: hidden;">例题5：立川手平</p>\n' +
			'<ul style="margin: 0px; padding: 0px 0px 0px 30px; font-family: \'Microsoft YaHei\'; list-style: none; overflow: hidden; line-height: 30px;">\n' +
			'<li style="margin: 0px 25px 0px 0px; padding: 0px; font-family: 宋体; float: left;">A．LQ74</li>\n' +
			'<li style="margin: 0px 25px 0px 0px; padding: 0px; font-family: 宋体; float: left;">B．76QP</li>\n' +
			'<li style="margin: 0px 25px 0px 0px; padding: 0px; font-family: 宋体; float: left;">C．L67P</li>\n' +
			'<li style="margin: 0px 25px 0px 0px; padding: 0px; font-family: 宋体; float: left;">D．L69P</li>\n' +
			'<li style="margin: 0px 25px 0px 0px; padding: 0px; font-family: 宋体; float: left;">E．L67Q</li>\n' +
			'</ul>\n' +
			'<p style="font-size: 15px; line-height: 40px; overflow: hidden;">例题6：太州木大</p>\n' +
			'<ul style="margin: 0px; padding: 0px 0px 0px 30px; font-family: \'Microsoft YaHei\'; list-style: none; overflow: hidden; line-height: 30px;">\n' +
			'<li style="margin: 0px 25px 0px 0px; padding: 0px; font-family: 宋体; float: left;">A．Q607</li>\n' +
			'<li style="margin: 0px 25px 0px 0px; padding: 0px; font-family: 宋体; float: left;">B．940Q</li>\n' +
			'<li style="margin: 0px 25px 0px 0px; padding: 0px; font-family: 宋体; float: left;">C．9607</li>\n' +
			'<li style="margin: 0px 25px 0px 0px; padding: 0px; font-family: 宋体; float: left;">D．96Q7</li>\n' +
			'<li style="margin: 0px 25px 0px 0px; padding: 0px; font-family: 宋体; float: left;">E．049Q</li>\n' +
			'</ul>\n' +
			'</div>\n' +
			'<div style="font-family: 宋体; ">解答：</div>\n' +
			'<p style="font-family: 宋体; font-size: 21px; text-indent: 32px; line-height: 40px; overflow: hidden; ">&nbsp;</p>\n' +
			'<ul style="margin: 0px; font-family: \'Microsoft YaHei\'; list-style: none; overflow: hidden; line-height: 30px; padding-bottom: 50px;">\n' +
			'<li style="margin: 0px 25px 0px 0px; padding: 0px; font-family: 宋体; float: left; width: 1834.94px;">题5中与&ldquo;立川手平&rdquo;对应的数字或字母是&ldquo;L67P&rdquo;，故答案为C；</li>\n' +
			'<li style="margin: 0px 25px 0px 0px; padding: 0px; font-family: 宋体; float: left; width: 1834.94px;">题6中与&ldquo;太州木大&rdquo;对应的数字或字母是&ldquo;940Q&rdquo;，故答案为A。</li>\n' +
			'</ul>',
	},
	政治理论: {
		rule: '政治理论说明...',
	},
	常识判断: {
		rule: '常识说明...',
	},
	言语理解: {
		rule: '言语理解与表达说明...',
	},
	数量关系: {
		rule: '数量关系说明...',
	},
	判断推理: {
		rule: '判断推理说明...',
	},
	资料分析: {
		rule: '资料分析说明...',
	},
}
