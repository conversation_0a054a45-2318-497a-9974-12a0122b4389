import { TypeZsjyPaper, TypeZsjyQuestion, TypeZsjyQuestionData, TypeZsjyRecordInfo } from '~/types/biz/biztypes'

export const hasPermission = (_value: any[] | string) => {
	const value = Array.isArray(_value) ? _value : [_value]
	if (Array.isArray(value) && value?.length > 0) {
		const permissions = useUserStore().permissions
		const all_permission = '*:*:*'
		const permissionFlag = value
		return permissions?.some((permission: string) => {
			return all_permission === permission || permissionFlag.includes(permission)
		})
	}
	return false
}

export const BIZ_QUESTION_UTILS = {
	// 匹配考点
	matchCategoryByPoint: async (testPoint: string): Promise<number | undefined> => {
		if (!testPoint) return undefined
		try {
			const { pathMap } = await BIZ_ZsjyQuestionCate_APIS.getListMap()
			const plainText = testPoint.replace(/<[^>]*>/g, '').trim()
			return pathMap.get(plainText)
		} catch (e) {
			console.error('分类匹配失败:', e)
			return undefined
		}
	},
	processQuestion: (question: TypeZsjyQuestion) => {
		if (!question) return
		if (question.optionsJson) {
			question.options = JSON.parse(question.optionsJson)
		}
		// 处理user questions才有的字段
		question.isCollected = question?.userQuestion?.isCollected
	},
	// 处理试题列表
	processQuestionList: (questionList: TypeZsjyQuestion[], dataList: TypeZsjyQuestionData[]) => {
		if (!questionList) return
		const questionsByDataId = new Map<string, TypeZsjyQuestion[]>()
		questionList.forEach((question) => {
			BIZ_QUESTION_UTILS.processQuestion(question)
			if (question.dataId) {
				if (!questionsByDataId.has(question.dataId)) {
					questionsByDataId.set(question.dataId, [])
				}
				questionsByDataId.get(question.dataId)?.push(question)
			}
			if (dataList?.length) {
				question.data = dataList.find((data) => data.id === question.dataId)
			}
		})

		// Mark first and last questions for each dataId
		questionsByDataId.forEach((questions) => {
			if (questions.length > 0) {
				questions[0].isFirstDataQuestion = true
				questions[questions.length - 1].isLastDataQuestion = true
			}
		})
	},
	processQuestionListByCateProgress(questionList: TypeZsjyQuestion[], cateProgress) {
		if (!questionList || !cateProgress) return []

		// 创建分类ID到分类信息的映射
		const cateInfoMap = new Map()
		Object.values(cateProgress).forEach((cate) => {
			cateInfoMap.set(cate.id, cate)
		})

		// 保持分类顺序的数组和快速查找的Map
		const orderedCates = []
		const cateMap = new Map() // id => { 分类对象 + questionList }

		questionList.forEach((q) => {
			const cateId = q.cateId1
			if (!cateId) return

			// 如果分类尚未处理过
			if (!cateMap.has(cateId)) {
				const cateInfo = cateInfoMap.get(cateId)
				if (!cateInfo) return // 跳过无效分类

				// 创建带题目列表的新分类对象
				const newCate = {
					...cateInfo,
					questionList: [],
				}

				// 记录顺序并存储
				orderedCates.push(newCate)
				cateMap.set(cateId, newCate)
			}

			// 添加题目到对应分类
			cateMap.get(cateId).questionList.push(q)
		})

		return orderedCates
	},
	processRecordInfo: (info: TypeZsjyRecordInfo) => {
		info.scorePercentage = calculatePercentage(info.score, info.paperScore)
		info.correctPercentage = calculatePercentage(info.correctCount, info.questionTotal)
		info.wrongPercentage = calculatePercentage(info.wrongCount, info.questionTotal)
		info.unansweredPercentage = calculatePercentage(info.unansweredCount, info.questionTotal)
		info.completionRate = Math.min(100, Math.round(((info.correctCount + info.wrongCount) / info.questionTotal) * 100))
		info.beatPercentageData = calculateBeatPercentage(info.score, info.paperInfo)
		info.cateProgressList = []
		info.pointStatsList = []
		// 计算分类进度
		for (const [key, data] of Object.entries(info.cateProgress || {})) {
			info.cateProgressList.push({
				name: key,
				percentage: calculatePercentage(data.correctCount + data.wrongCount, data.questionTotal),
				scoreRate: calculatePercentage(data.userScore, data.score),
				correctRate: calculatePercentage(data.correctCount, data.questionTotal),
				wrongRate: calculatePercentage(data.wrongCount, data.questionTotal),
				unansweredRate: calculatePercentage(data.unansweredCount, data.questionTotal),
				...data,
			})
		}

		// 计算知识点统计
		for (const [key, data] of Object.entries(info.pointStats || {})) {
			info.pointStatsList.push({
				name: key,
				percentage: calculatePercentage(data.correctCount + data.wrongCount, data.questionTotal),
				correctRate: calculatePercentage(data.correctCount, data.questionTotal),
				wrongRate: calculatePercentage(data.wrongCount, data.questionTotal),
				unansweredRate: calculatePercentage(data.unansweredCount, data.questionTotal),
				...data,
			})
		}

		// 计算最佳和最差模块
		info.bestModule = info.cateProgressList?.reduce((prev, curr) => (curr.correctRate > prev.correctRate ? curr : prev), {
			name: '暂无数据',
			correctRate: 0,
			correctCount: 0,
			questionTotal: 0,
		})
		info.worstModule = info.cateProgressList?.reduce((prev, curr) => (curr.wrongRate > prev.wrongRate ? curr : prev), {
			name: '暂无数据',
			wrongRate: 0,
			wrongCount: 0,
			questionTotal: 0,
		})
		info.mostCompletedModule = info.cateProgressList?.reduce((prev, curr) => (curr.percentage > prev.percentage ? curr : prev), {
			name: '暂无数据',
			percentage: 0,
			correctCount: 0,
			wrongCount: 0,
			questionTotal: 0,
		})

		// 计算最佳和最差知识点
		info.bestPoint = info.pointStatsList?.reduce((prev, curr) => (curr.correctRate > prev.correctRate ? curr : prev), {
			name: '暂无数据',
			correctRate: 0,
			correctCount: 0,
			questionTotal: 0,
		})
		info.worstPoint = info.pointStatsList?.reduce((prev, curr) => (curr.wrongRate > prev.wrongRate ? curr : prev), {
			name: '暂无数据',
			wrongRate: 0,
			wrongCount: 0,
			questionTotal: 0,
		})
		info.mostCompletedPoint = info.pointStatsList?.reduce((prev, curr) => (curr.percentage > prev.percentage ? curr : prev), {
			name: '暂无数据',
			percentage: 0,
			correctCount: 0,
			wrongCount: 0,
			questionTotal: 0,
		})
	},
}

// 计算百分比
const calculatePercentage = (value: number, total: number) => {
	if (total === 0) return 0
	return X_MATH_UTILS.safeCalculateExpression(`${value}/${total}*100`, 1)
}

// 计算击败考生百分比
const calculateBeatPercentage = (score: number, paperInfo: TypeZsjyPaper) => {
	if (!score || !paperInfo) {
		return {
			percentage: 0,
			beatenPeople: 0,
			totalPeople: 0,
		}
	}
	// 处理无效数据的情况
	if (!paperInfo.practiceTotal || paperInfo.practiceTotal <= 1 || !paperInfo.scoreAverage) {
		return {
			percentage: 0,
			beatenPeople: 0,
			totalPeople: paperInfo.practiceTotal || 0,
		}
	}

	// 处理分数为0的情况
	if (score === 0) {
		return {
			percentage: 0,
			beatenPeople: 0,
			totalPeople: paperInfo.practiceTotal,
		}
	}

	// 对于小样本量（小于等于10人），使用直接排名计算
	if (paperInfo.practiceTotal <= 10) {
		// 假设当前分数高于平均分，则击败了约一半的人
		const beatenPeople = Math.floor(paperInfo.practiceTotal / 2)
		const percentage = (beatenPeople / paperInfo.practiceTotal) * 100
		return {
			percentage: Math.round(percentage),
			beatenPeople,
			totalPeople: paperInfo.practiceTotal,
		}
	}

	// 对于大样本量，使用正态分布估算
	const average = paperInfo.scoreAverage
	const totalPeople = paperInfo.practiceTotal
	const standardDeviation = X_MATH_UTILS.safeCalculateExpression(`${paperInfo.score}*0.15`) // 假设标准差为满分的15%
	const zScore = X_MATH_UTILS.safeCalculateExpression(`(${score}-${average})/${standardDeviation}`)
	const percentile = X_MATH_UTILS.safeCalculateExpression(`0.5*(1+erf(${zScore}/sqrt(2)))`)
	const percentage = X_MATH_UTILS.safeCalculateExpression(`${percentile}*100`, 1)

	// 计算击败的人数
	const beatenPeople = Math.round(totalPeople * percentile)

	return {
		percentage,
		beatenPeople,
		totalPeople,
	}
}
