<route lang="json">
{
	"meta": {
		"title": "铅封状态事件"
	}
}
</route>
<script setup lang="ts">
const props = defineProps({
	params: {
		type: Object,
		default: () => ({}),
	},
})
const computedParams = computed(() => props.params)
const queryModel = reactive({
	pageNo: 1,
	pageSize: 10,
	sealNo: null,
	eventCode: null,
	batteryLevel: null,
	licensePlate: null,
	longitude: null,
	latitude: null,
	eventTime: [],
	createTime: [],
	...computedParams.value,
})

const modalRef = ref(null)
const BizTablesListRef = ref()
const { loading, run, list, total, reset } = xUsePageRequest(BIZ_JnrmSealEvent_APIS.getPage, queryModel)
onMounted(() => {
	run()
})

async function handleDelete(id) {
	await toast.confirm('确定删除吗？')
	loading.value = true
	await BIZ_JnrmSealEvent_APIS.delete(id).finally(() => {
		loading.value = false
	})
	toast.success('删除成功')
	if (!BizTablesListRef.value?.getTableRef()?.deleteNode({ id })) {
		reset()
	}
}

function handleOpenModal(type, id) {
	modalRef.value.open(type, id, computedParams.value)
}

const columns = [
	{
		label: '铅封编号',
		prop: 'sealNo',
	},
	{
		label: '事件类型',
		prop: 'eventCode',
		tagGroupName: 'JNRM铅封状态',
	},
	{
		label: '电量（百分比0-100）',
		prop: 'batteryLevel',
	},
	{
		label: '车牌号',
		prop: 'licensePlate',
	},
	{
		label: '经度',
		prop: 'longitude',
	},
	{
		label: '纬度',
		prop: 'latitude',
	},
	{
		label: '事件时间',
		prop: 'eventTime',
		width: 150,
		formatter: X_DATE_UTILS.formatDate,
	},
	{
		label: '创建时间',
		prop: 'createTime',
		width: 150,
		formatter: X_DATE_UTILS.formatDate,
	},
	{
		label: '操作',
		prop: 'action',
	},
]
</script>
<template>
	<BizLayoutPageContentContainer>
		<BizFormModalsJnrmSealEvent ref="modalRef" @success="run()" />
		<BizCardsQuery>
			<BizFormQueryForm :model="queryModel">
				<XFormItem label="铅封编号" prop="sealNo">
					<XInput v-model="queryModel.sealNo" placeholder="请输入搜索" @keyup.enter="run" />
				</XFormItem>
				<XFormItem label="车牌号" prop="licensePlate">
					<XInput v-model="queryModel.licensePlate" placeholder="请输入搜索" @keyup.enter="run" />
				</XFormItem>
				<XFormItem label="事件时间" prop="eventTime">
					<XDateTimePicker
						v-model="queryModel.eventTime"
						value-format="YYYY-MM-DD HH:mm:ss"
						range
						type="datetime"
						placeholder="请选择搜索"
						@clear="reset"
					/>
				</XFormItem>
			</BizFormQueryForm>
			<div class="flex items-center justify-end gap-xs">
				<BizButtonsReset @click="reset" />
				<BizButtonsQuery @click="run" />
			</div>
		</BizCardsQuery>
		<div class="flex items-center justify-between py-xs">
			<div>
				<XFormItem label="事件类型" prop="eventCode">
					<XRadioGroup v-model="queryModel.eventCode" tag-group-name="JNRM铅封状态" placeholder="请输入搜索" @keyup.enter="run" />
				</XFormItem>
			</div>
			<div class="flex items-center">
				<!--				<BizButtonsCreate v-if="hasPermission('x:jnrm-seal-event:create')" @click="handleOpenModal('create')" />-->
			</div>
		</div>
		<BizCardsTable v-loading="loading">
			<BizTablesList ref="BizTablesListRef" :data="list" :columns="columns">
				<template #action="{ row, rowIndex }">
					<div class="flex items-center justify-center gap-xs">
						<BizButtonsUpdate v-if="hasPermission('x:jnrm-seal-event:update')" @click="handleOpenModal('update', row.id)" />
						<BizButtonsDelete v-if="hasPermission('x:jnrm-seal-event:delete')" @click="handleDelete(row.id)" />
					</div>
				</template>
			</BizTablesList>
			<div class="flex justify-end mt-base">
				<XPagination v-model="queryModel.pageNo" v-model:page-size="queryModel.pageSize" :total="total" @change="run" />
			</div>
		</BizCardsTable>
	</BizLayoutPageContentContainer>
</template>
<style scoped></style>
