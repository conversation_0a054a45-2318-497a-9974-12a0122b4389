<route lang="json">
{
	"meta": {
		"title": "承运单"
	}
}
</route>
<script setup lang="ts">
const props = defineProps({
	params: {
		type: Object,
		default: () => ({}),
	},
	searchable: {
		type: Boolean,
		default: true,
	},
	addable: {
		type: Boolean,
		default: true,
	},
	operable: {
		type: Boolean,
		default: true,
	},
	containerPlain: {
		type: Boolean,
		default: false,
	},
})
const userStore = useUserStore()
const computedParams = computed(() => props.params)
const queryModel = reactive({
	pageNo: 1,
	pageSize: 10,
	orderNo: null,
	deptId: null,
	powerPlantId: null,
	departureId: null,
	destinationId: null,
	vehicleId: null,
	driverId: null,
	licensePlate: null,
	driverName: null,
	driverPhone: null,
	cargoInfo: null,
	cargoWeight: null,
	startTime: [],
	deliveryTime: [],
	estimatedArrivalTime: [],
	actualArrivalTime: [],
	status: null,
	createTime: [],
	...computedParams.value,
})

const modalRef = ref(null)
const BizTablesListRef = ref()
const { loading, run, list, total, reset } = xUsePageRequest(BIZ_JnrmTransportOrder_APIS.getPage, queryModel)
onMounted(() => {
	run()
})

async function handleDelete(id) {
	await toast.confirm('确定删除吗？')
	loading.value = true
	await BIZ_JnrmTransportOrder_APIS.delete(id).finally(() => {
		loading.value = false
	})
	toast.success('删除成功')
	if (!BizTablesListRef.value?.getTableRef()?.deleteNode({ id })) {
		reset()
	}
}

function handleOpenModal(type, id) {
	modalRef.value.open(type, id, computedParams.value)
}

const columns = [
	{
		label: '运单号',
		prop: 'orderNo',
		width: 150,
	},
	{
		label: '出发地',
		prop: 'departureName',
	},
	{
		label: '目的地',
		prop: 'destinationName',
	},
	{
		label: '车牌号',
		prop: 'licensePlate',
	},
	{
		label: '司机姓名',
		prop: 'driverName',
	},
	{
		label: '司机手机号',
		prop: 'driverPhone',
	},
	{
		label: '货物信息',
		prop: 'cargoInfo',
	},
	{
		label: '货物重量(吨)',
		prop: 'cargoWeight',
	},
	{
		label: '开始时间',
		prop: 'startTime',
		width: 150,
		formatter: X_DATE_UTILS.formatDate,
	},
	{
		label: '预估送达时间',
		prop: 'estimatedArrivalTime',
		width: 150,
		formatter: X_DATE_UTILS.formatDate,
	},
	{
		label: '实际送达时间',
		prop: 'actualArrivalTime',
		width: 150,
		formatter: X_DATE_UTILS.formatDate,
	},
	{
		label: '状态',
		prop: 'status',
	},
	{
		label: '创建时间',
		prop: 'createTime',
		width: 150,
		formatter: X_DATE_UTILS.formatDate,
	},
	{
		label: '操作',
		prop: 'action',
		width: 180,
	},
]
const orderModalVisible = ref(false)
const currentOrderRow = ref(null)
function handleView(row) {
	orderModalVisible.value = true
	currentOrderRow.value = row
}
</script>
<template>
	<BizLayoutPageContentContainer :plain="containerPlain">
		<BizFormModalsJnrmTransportOrder ref="modalRef" @success="run()" />
		<BizCardsQuery v-if="searchable">
			<BizFormQueryForm :model="queryModel">
				<XFormItem v-if="userStore.isPlatformSuper" label="所属电厂" prop="deptId">
					<XSelect
						v-model="queryModel.deptId"
						:clearable="false"
						:remote-method="BIZ_Dept_APIS.getPage"
						placeholder="请输入搜索"
						@keyup.enter="run"
					/>
				</XFormItem>
				<XFormItem label="运单号" prop="orderNo">
					<XInput v-model="queryModel.orderNo" placeholder="请输入搜索" @keyup.enter="run" />
				</XFormItem>
				<XFormItem v-if="!computedParams.licensePlate" label="车牌号" prop="licensePlate">
					<XInput v-model="queryModel.licensePlate" placeholder="请输入搜索" @keyup.enter="run" />
				</XFormItem>
				<XFormItem label="司机姓名" prop="driverName">
					<XInput v-model="queryModel.driverName" placeholder="请输入搜索" @keyup.enter="run" />
				</XFormItem>
				<XFormItem label="司机手机号" prop="driverPhone">
					<XInput v-model="queryModel.driverPhone" placeholder="请输入搜索" @keyup.enter="run" />
				</XFormItem>
				<XFormItem label="状态" prop="status">
					<XSelect v-model="queryModel.status" placeholder="请选择搜索" tag-group-name="JNRM运单状态" :clearable="false" />
				</XFormItem>
				<XFormItem label="货物信息" prop="cargoInfo">
					<XInput v-model="queryModel.cargoInfo" placeholder="请输入搜索" @keyup.enter="run" />
				</XFormItem>
			</BizFormQueryForm>
			<div class="flex items-center justify-end gap-xs">
				<BizButtonsReset @click="reset" />
				<BizButtonsQuery @click="run" />
			</div>
		</BizCardsQuery>
		<div class="flex items-center justify-between py-xs">
			<div class="flex items-center">
				<BizButtonsCreate v-if="addable && hasPermission('x:jnrm-transport-order:create')" @click="handleOpenModal('create')" />
			</div>
		</div>
		<BizCardsTable v-loading="loading">
			<BizTablesList ref="BizTablesListRef" :data="list" :columns="columns">
				<template #action="{ row, rowIndex }">
					<div class="flex items-center justify-center gap-xs">
						<x-button text-mode @click="handleView(row)">查看</x-button>
						<BizButtonsUpdate v-if="hasPermission('x:jnrm-transport-order:update')" @click="handleOpenModal('update', row.id)" />
						<BizButtonsDelete v-if="hasPermission('x:jnrm-transport-order:delete')" @click="handleDelete(row.id)" />
					</div>
				</template>
			</BizTablesList>
			<div class="flex justify-end mt-base">
				<XPagination v-model="queryModel.pageNo" v-model:page-size="queryModel.pageSize" :total="total" @change="run" />
			</div>
		</BizCardsTable>
		<x-modal v-model="orderModalVisible" title="运单详情" container-class="!w-auto !min-w-50vw"></x-modal>
	</BizLayoutPageContentContainer>
</template>
<style scoped></style>
