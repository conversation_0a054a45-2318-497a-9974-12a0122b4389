<route lang="json">
{
	"meta": {
		"title": "标签分组"
	}
}
</route>
<script setup lang="ts">
const props = defineProps({
	params: {
		type: Object,
		default: () => ({}),
	},
})
const computedParams = computed(() => props.params)
const queryModel = reactive({
	pageNo: 1,
	pageSize: 10,
	createTime: [],
	name: undefined,
	description: undefined,
	sort: undefined,
	...computedParams.value,
})

const modalRef = ref(null)
const { loading, run, list, total, reset } = xUsePageRequest(BIZ_CommonTagGroup_APIS.getPage, queryModel)
onMounted(() => {
	run()
})

async function handleDelete(id, rowIndex) {
	await toast.confirm('确定删除吗？')
	loading.value = true
	await BIZ_CommonTagGroup_APIS.delete(id).finally(() => {
		loading.value = false
	})
	toast.success('删除成功')
	list.value.splice(rowIndex, 1)
}

function handleOpenModal(type, id) {
	modalRef.value.open(type, id)
}

const columns = [
	{
		label: '创建时间',
		prop: 'createTime',
		formatter: X_DATE_UTILS.formatDate,
	},
	{
		label: '名称',
		prop: 'name',
	},
	{
		label: '描述',
		prop: 'description',
	},
	{
		label: '排序',
		prop: 'sort',
	},
	{
		label: '操作',
		prop: 'action',
	},
]
</script>
<template>
	<BizLayoutPageContentContainer>
		<BizFormModalsCommonTagGroup ref="modalRef" @success="run()" />
		<BizCardsQuery>
			<BizFormQueryForm :model="queryModel">
				<XFormItem label="名称" prop="name">
					<XInput v-model="queryModel.name" placeholder="请输入搜索" @keyup.enter="run" />
				</XFormItem>
				<XFormItem label="排序" prop="sort">
					<XInput v-model="queryModel.sort" placeholder="请输入搜索" @keyup.enter="run" />
				</XFormItem>
				<XFormItem label="创建时间" prop="createTime">
					<x-date-time-picker v-model="queryModel.createTime" range @keyup.enter="run" />
				</XFormItem>
				<div class="absolute bottom-0.2rem right-0.2rem flex items-center justify-end gap-xs">
					<BizButtonsReset @click="reset" />
					<BizButtonsQuery @click="run" />
				</div>
			</BizFormQueryForm>
		</BizCardsQuery>
		<div class="flex items-center py-xs">
			<BizButtonsCreate v-if="hasPermission('x:common-tag-group:create')" class="ml-auto" @click="handleOpenModal('create')" />
		</div>
		<BizCardsTable v-loading="loading">
			<BizTablesList :data="list" :columns="columns">
				<template #action="{ row, rowIndex }">
					<div class="flex items-center justify-center gap-xs">
						<BizButtonsUpdate v-if="hasPermission('x:common-tag-group:update')" @click="handleOpenModal('update', row.id)" />
						<BizButtonsDelete v-if="hasPermission('x:common-tag-group:delete')" @click="handleDelete(row.id, rowIndex)" />
					</div>
				</template>
			</BizTablesList>
			<div class="flex justify-end mt-base">
				<XPagination v-model="queryModel.pageNo" v-model:page-size="queryModel.pageSize" :total="total" @change="run" />
			</div>
		</BizCardsTable>
	</BizLayoutPageContentContainer>
</template>
<style scoped></style>
