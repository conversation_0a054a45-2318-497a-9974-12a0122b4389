<route lang="json">
  {
  "meta": {
  "title": "车辆GPS表（分区表）"
  }
  }
</route>
<script setup lang="ts">
  const props = defineProps({
    params: {
      type: Object,
      default: () => ({}),
    },
  })
  const computedParams = computed(() => props.params)
  const queryModel = reactive({
              pageNo: 1,
        pageSize: 10,
                      vehicleId: null,
                      lng: null,
                      lat: null,
                      speed: null,
                      direction: null,
    ...computedParams.value,
  })

  const modalRef = ref(null)
  const BizTablesListRef = ref()
  const { loading, run, list, total, reset } = xUsePageRequest(BIZ_JnrmVehicleGps_APIS.getPage, queryModel)
  onMounted(() => {
    run()
  })

  async function handleDelete(id) {
    await toast.confirm('确定删除吗？')
    loading.value = true
    await BIZ_JnrmVehicleGps_APIS.delete(id).finally(() => {
      loading.value = false
    })
    toast.success('删除成功')
    if (!BizTablesListRef.value?.getTableRef()?.deleteNode({ id })) {
      reset()
    }
  }

  function handleOpenModal(type, id) {
    modalRef.value.open(type, id, computedParams.value)
  }

  const columns = [
                {
                  label: '车辆ID',
                  prop: 'vehicleId',
                },
                {
                  label: '经度',
                  prop: 'lng',
                },
                {
                  label: '纬度',
                  prop: 'lat',
                },
                {
                  label: '速度（km/h）',
                  prop: 'speed',
                },
                {
                  label: '方向（0-360度）',
                  prop: 'direction',
                },
    {
      label: '操作',
      prop: 'action',
    },
  ]
</script>
<template>
  <BizLayoutPageContentContainer>
    <BizFormModalsJnrmVehicleGps ref="modalRef" @success="run()" />
    <BizCardsQuery>
      <BizFormQueryForm :model="queryModel">
                    <XFormItem label="车辆ID" prop="vehicleId">
                      <XInput v-model="queryModel.vehicleId" placeholder="请输入搜索" @keyup.enter="run" />
                    </XFormItem>
                    <XFormItem label="经度" prop="lng">
                      <XInput v-model="queryModel.lng" placeholder="请输入搜索" @keyup.enter="run" />
                    </XFormItem>
                    <XFormItem label="纬度" prop="lat">
                      <XInput v-model="queryModel.lat" placeholder="请输入搜索" @keyup.enter="run" />
                    </XFormItem>
                    <XFormItem label="速度（km/h）" prop="speed">
                      <XInput v-model="queryModel.speed" placeholder="请输入搜索" @keyup.enter="run" />
                    </XFormItem>
                    <XFormItem label="方向（0-360度）" prop="direction">
                      <XInput v-model="queryModel.direction" placeholder="请输入搜索" @keyup.enter="run" />
                    </XFormItem>
      </BizFormQueryForm>
      <div class="absolute bottom-0.2rem right-0.2rem flex items-center justify-end gap-xs">
        <BizButtonsReset @click="reset" />
        <BizButtonsQuery @click="run" />
      </div>
    </BizCardsQuery>
    <div class="flex items-center justify-between py-xs">
      <div></div>
      <div class="flex items-center">
        <BizButtonsCreate @click="handleOpenModal('create')" v-if="hasPermission('x:jnrm-vehicle-gps:create')" />
      </div>
    </div>
    <BizCardsTable v-loading="loading">
      <BizTablesList ref="BizTablesListRef" :data="list" :columns="columns">
        <template #action="{ row, rowIndex }">
          <div class="flex items-center justify-center gap-xs">
            <BizButtonsUpdate v-if="hasPermission('x:jnrm-vehicle-gps:update')" @click="handleOpenModal('update', row.id)" />
            <BizButtonsDelete v-if="hasPermission('x:jnrm-vehicle-gps:delete')" @click="handleDelete(row.id)" />
          </div>
        </template>
      </BizTablesList>
      <div class="flex justify-end mt-base">
        <XPagination v-model="queryModel.pageNo" v-model:page-size="queryModel.pageSize" :total="total" @change="run" />
      </div>
    </BizCardsTable>
  </BizLayoutPageContentContainer>
</template>
<style scoped></style>
