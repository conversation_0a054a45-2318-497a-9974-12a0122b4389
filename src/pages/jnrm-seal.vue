<route lang="json">
{
	"meta": {
		"title": "铅封"
	}
}
</route>
<script setup lang="ts">
const props = defineProps({
	params: {
		type: Object,
		default: () => ({}),
	},
})
const computedParams = computed(() => props.params)
const queryModel = reactive({
	pageNo: 1,
	pageSize: 10,
	sealNo: null,
	status: null,
	batteryLevel: null,
	serialNumber: null,
	createTime: [],
	...computedParams.value,
})

const modalRef = ref(null)
const BizTablesListRef = ref()
const { loading, run, list, total, reset } = xUsePageRequest(BIZ_JnrmSeal_APIS.getPage, queryModel)
onMounted(() => {
	run()
})

async function handleDelete(id) {
	await toast.confirm('确定删除吗？')
	loading.value = true
	await BIZ_JnrmSeal_APIS.delete(id).finally(() => {
		loading.value = false
	})
	toast.success('删除成功')
	if (!BizTablesListRef.value?.getTableRef()?.deleteNode({ id })) {
		reset()
	}
}

function handleOpenModal(type, id) {
	modalRef.value.open(type, id, computedParams.value)
}

const columns = [
	{
		label: '铅封编号',
		prop: 'sealNo',
	},
	{
		label: '序列号',
		prop: 'serialNumber',
	},
	{
		label: '状态',
		prop: 'status',
		tagGroupName: 'JNRM铅封状态',
	},
	{
		label: '操作',
		prop: 'action',
	},
]
</script>
<template>
	<BizLayoutPageContentContainer>
		<BizFormModalsJnrmSeal ref="modalRef" @success="run()" />
		<BizCardsQuery>
			<BizFormQueryForm :model="queryModel">
				<XFormItem label="编号" prop="sealNo">
					<XInput v-model="queryModel.sealNo" placeholder="请输入搜索" @keyup.enter="run" />
				</XFormItem>
				<XFormItem label="状态" prop="status">
					<XSelect v-model="queryModel.status" placeholder="请选择搜索" tag-group-name="JNRM铅封状态" />
				</XFormItem>
			</BizFormQueryForm>
			<div class="flex items-center justify-end gap-xs">
				<BizButtonsReset @click="reset" />
				<BizButtonsQuery @click="run" />
			</div>
		</BizCardsQuery>
		<div class="flex items-center justify-between py-xs">
			<div></div>
			<div class="flex items-center">
				<BizButtonsCreate v-if="hasPermission('x:jnrm-seal:create')" @click="handleOpenModal('create')" />
			</div>
		</div>
		<BizCardsTable v-loading="loading">
			<BizTablesList ref="BizTablesListRef" :data="list" :columns="columns">
				<template #action="{ row, rowIndex }">
					<div class="flex items-center justify-center gap-xs">
						<BizButtonsUpdate v-if="hasPermission('x:jnrm-seal:update')" @click="handleOpenModal('update', row.id)" />
						<BizButtonsDelete v-if="hasPermission('x:jnrm-seal:delete')" @click="handleDelete(row.id)" />
					</div>
				</template>
			</BizTablesList>
			<div class="flex justify-end mt-base">
				<XPagination v-model="queryModel.pageNo" v-model:page-size="queryModel.pageSize" :total="total" @change="run" />
			</div>
		</BizCardsTable>
	</BizLayoutPageContentContainer>
</template>
<style scoped></style>
