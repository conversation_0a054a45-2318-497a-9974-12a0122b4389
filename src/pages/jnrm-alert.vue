<route lang="json">
{
	"meta": {
		"title": "告警"
	}
}
</route>
<script setup lang="ts">
const props = defineProps({
	params: {
		type: Object,
		default: () => ({}),
	},
	searchable: {
		type: Boolean,
		default: true,
	},
	addable: {
		type: Boolean,
		default: true,
	},
	operable: {
		type: Boolean,
		default: true,
	},
	containerPlain: {
		type: Boolean,
		default: false,
	},
})
const computedParams = computed(() => props.params)
const queryModel = reactive({
	pageNo: 1,
	pageSize: 10,
	alertType: null,
	alertContent: null,
	vehicleId: null,
	licensePlate: null,
	handleStatus: null,
	alertTime: [],
	alertImageUrl: null,
	alertVideoUrl: null,
	transportOrderId: null,
	externalOrderNo: null,
	driverName: null,
	driverPhone: null,
	createTime: [],
	...computedParams.value,
})

const modalRef = ref(null)
const BizTablesListRef = ref()
const { loading, run, list, total, reset } = xUsePageRequest(BIZ_JnrmAlert_APIS.getPage, queryModel)
onMounted(() => {
	run()
})

async function handleDelete(id) {
	await toast.confirm('确定删除吗？')
	loading.value = true
	await BIZ_JnrmAlert_APIS.delete(id).finally(() => {
		loading.value = false
	})
	toast.success('删除成功')
	if (!BizTablesListRef.value?.getTableRef()?.deleteNode({ id })) {
		reset()
	}
}

function handleOpenModal(type, id) {
	modalRef.value.open(type, id, computedParams.value)
}

const columns = [
	{
		label: '运单号',
		prop: 'transportOrderNo',
	},
	{
		label: '车牌号',
		prop: 'licensePlate',
	},
	{
		label: '告警类型',
		prop: 'alertType',
	},
	{
		label: '告警内容',
		prop: 'alertContent',
		width: 200,
	},

	{
		label: '是否处理',
		prop: 'handleStatus',
		tagGroupName: 'JNRM告警处理状态',
		tagValueFormat: 'BOOLEAN',
	},
	{
		label: '告警时间',
		prop: 'alertTime',
		width: 150,
		formatter: X_DATE_UTILS.formatDate,
	},
	{
		label: '司机姓名',
		prop: 'driverName',
	},
	{
		label: '司机手机号',
		prop: 'driverPhone',
	},
	{
		label: '操作',
		prop: 'action',
	},
]
</script>
<template>
	<BizLayoutPageContentContainer :plain="containerPlain">
		<BizFormModalsJnrmAlert ref="modalRef" @success="run()" />
		<BizCardsQuery v-if="searchable">
			<BizFormQueryForm :model="queryModel">
				<XFormItem label="是否处理" prop="handleStatus">
					<XSelect
						v-model="queryModel.handleStatus"
						placeholder="请选择搜索"
						tag-group-name="JNRM告警处理状态"
						tag-value-format="bool"
						@change="run()"
					/>
				</XFormItem>
				<XFormItem label="告警类型" prop="alertType">
					<XSelect v-model="queryModel.alertType" placeholder="请选择搜索" tag-group-name="JNRM告警类型" @change="run()" />
				</XFormItem>
				<XFormItem v-if="!computedParams.licensePlate" label="车牌号" prop="licensePlate">
					<XInput v-model="queryModel.licensePlate" placeholder="请输入搜索" @keyup.enter="run" />
				</XFormItem>
				<XFormItem label="司机姓名" prop="driverName">
					<XInput v-model="queryModel.driverName" placeholder="请输入搜索" @keyup.enter="run" />
				</XFormItem>
				<XFormItem label="司机手机号" prop="driverPhone">
					<XInput v-model="queryModel.driverPhone" placeholder="请输入搜索" @keyup.enter="run" />
				</XFormItem>

				<XFormItem label="告警时间" prop="alertTime">
					<XDateTimePicker
						v-model="queryModel.alertTime"
						value-format="YYYY-MM-DD HH:mm:ss"
						range
						type="datetime"
						placeholder="请选择搜索"
						@clear="reset"
					/>
				</XFormItem>
			</BizFormQueryForm>
			<div class="flex items-center justify-end gap-xs">
				<BizButtonsReset @click="reset" />
				<BizButtonsQuery @click="run" />
			</div>
		</BizCardsQuery>
		<div class="flex items-center justify-between py-xs">
			<div class="flex items-center gap-xl">
				<!--				<XFormItem label="处理状态" prop="handleStatus" label-class="font-bold">
					<XRadioGroup
						v-model="queryModel.handleStatus"
						placeholder="请选择搜索"
						tag-group-name="JNRM告警处理状态"
						tag-value-format="bool"
						@change="run()"
					/>
				</XFormItem>
				<XFormItem label="告警类型" prop="alertType" label-class="font-bold">
					<XRadioGroup v-model="queryModel.alertType" placeholder="请选择搜索" tag-group-name="JNRM告警类型" @change="run()" />
				</XFormItem>-->
			</div>
			<div class="flex items-center">
				<!--				<BizButtonsCreate @click="handleOpenModal('create')" v-if="hasPermission('x:jnrm-alert:create')" />-->
			</div>
		</div>
		<BizCardsTable v-loading="loading">
			<BizTablesList ref="BizTablesListRef" :data="list" :columns="columns">
				<template #action="{ row, rowIndex }">
					<div class="flex items-center justify-center gap-xs">
						<BizButtonsUpdate v-if="hasPermission('x:jnrm-alert:update')" text="查看" @click="handleOpenModal('update', row.id)" />
						<BizButtonsDelete v-if="hasPermission('x:jnrm-alert:delete')" @click="handleDelete(row.id)" />
					</div>
				</template>
			</BizTablesList>
			<div class="flex justify-end mt-base">
				<XPagination v-model="queryModel.pageNo" v-model:page-size="queryModel.pageSize" :total="total" @change="run" />
			</div>
		</BizCardsTable>
	</BizLayoutPageContentContainer>
</template>
<style scoped></style>
