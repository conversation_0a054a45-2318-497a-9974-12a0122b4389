<route lang="json">
{
	"meta": {
		"title": "BANNER"
	}
}
</route>
<script setup lang="ts">
const props = defineProps({
	params: {
		type: Object,
		default: () => ({}),
	},
})
const computedParams = computed(() => props.params)
const queryModel = reactive({
	pageNo: 1,
	pageSize: 10,
	createTime: [],
	title: undefined,
	type: undefined,
	position: undefined,
	platform: undefined,
	startTime: [],
	endTime: [],
	sort: undefined,
	targetUrl: undefined,
	elements: undefined,
	isShow: undefined,
	isTop: undefined,
	...computedParams.value,
})

const modalRef = ref(null)
const BizTablesListRef = ref()
const { loading, run, list, total, reset } = xUsePageRequest(BIZ_CommonBanner_APIS.getPage, queryModel)
onMounted(() => {
	run()
})

async function handleDelete(id) {
	await toast.confirm('确定删除吗？')
	loading.value = true
	await BIZ_CommonBanner_APIS.delete(id).finally(() => {
		loading.value = false
	})
	toast.success('删除成功')
	if (!BizTablesListRef.value?.getTableRef()?.deleteNode({ id })) {
		reset()
	}
}

function handleOpenModal(type, id) {
	modalRef.value.open(type, id)
}

const columns = [
	{
		label: '位置',
		prop: 'position',
	},
	{
		label: '标题',
		prop: 'title',
	},
	{
		label: '封面',
		prop: 'cover',
	},
	{
		label: '跳转地址',
		prop: 'targetUrl',
	},
	{
		label: '是否显示',
		prop: 'isShow',
	},
	{
		label: '是否置顶',
		prop: 'isTop',
	},
	{
		label: '排序值',
		prop: 'sort',
	},
	{
		label: '创建时间',
		prop: 'createTime',
		formatter: X_DATE_UTILS.formatDate,
	},
	{
		label: '操作',
		prop: 'action',
	},
]
</script>
<template>
	<BizLayoutPageContentContainer>
		<BizFormModalsCommonBanner ref="modalRef" @success="run()" />
		<BizCardsQuery>
			<BizFormQueryForm :model="queryModel">
				<XFormItem label="标题" prop="title">
					<XInput v-model="queryModel.title" placeholder="请输入搜索" @keyup.enter="run" />
				</XFormItem>
				<XFormItem label="跳转地址" prop="targetUrl">
					<XInput v-model="queryModel.targetUrl" placeholder="请输入搜索" @keyup.enter="run" />
				</XFormItem>
				<XFormItem label="是否显示" prop="isShow">
					<XSelect v-model="queryModel.isShow" tag-group-name="是否" />
				</XFormItem>
				<XFormItem label="是否置顶" prop="isTop">
					<XSelect v-model="queryModel.isTop" tag-group-name="是否" />
				</XFormItem>
				<XFormItem label="展示位置" prop="position">
					<XInput v-model="queryModel.position" placeholder="请输入搜索" @keyup.enter="run" />
				</XFormItem>
				<XFormItem label="创建时间" prop="createTime">
					<XDateTimePicker v-model="queryModel.createTime" value-format="YYYY-MM-DD" type="date" placeholder="请选择搜索" />
				</XFormItem>
				<div class="absolute bottom-0.2rem right-0.2rem flex items-center justify-end gap-xs">
					<BizButtonsReset @click="reset" />
					<BizButtonsQuery @click="run" />
				</div>
			</BizFormQueryForm>
		</BizCardsQuery>
		<div class="flex items-center py-xs">
			<BizButtonsCreate v-if="hasPermission('x:common-banner:create')" class="ml-auto" @click="handleOpenModal('create')" />
		</div>
		<BizCardsTable v-loading="loading">
			<BizTablesList ref="BizTablesListRef" :data="list" :columns="columns">
				<template #cover="{ row }">
					<XImage class="h2rem" :src="row.cover" />
				</template>
				<template #action="{ row, rowIndex }">
					<div class="flex items-center justify-center gap-xs">
						<BizButtonsUpdate v-if="hasPermission('x:common-banner:update')" @click="handleOpenModal('update', row.id)" />
						<BizButtonsDelete v-if="hasPermission('x:common-banner:delete')" @click="handleDelete(row.id)" />
					</div>
				</template>
			</BizTablesList>
			<div class="flex justify-end mt-base">
				<XPagination v-model="queryModel.pageNo" v-model:page-size="queryModel.pageSize" :total="total" @change="run" />
			</div>
		</BizCardsTable>
	</BizLayoutPageContentContainer>
</template>
<style scoped></style>
