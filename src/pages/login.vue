<route lang="json">
{
	"name": "adminLogin",
	"meta": {
		"layout": "none"
	}
}
</route>
<script setup lang="ts">
const appName = import.meta.env.VITE_APP_TITLE
const model = ref({ tenantName: '', username: '', password: '', captchaVerification: '' })
const userStore = useUserStore()
const { run, loading } = xUseRequest(BIZ_Auth_APIS.login, model.value)
async function handleLogin() {
	const res = await run()
	if (res.accessToken) {
		userStore.token = res.accessToken
		goHome()
	}
}

// 表单提交处理
function handleSubmit(e: Event) {
	e.preventDefault()
	handleLogin()
}
</script>

<template>
	<div class="login-container relative min-h-screen overflow-hidden">
		<!-- 动态背景层 -->
		<!--		<div class="background-overlay absolute inset-0"></div>-->
		<div class="animated-bg absolute inset-0"></div>

		<!-- 粒子效果背景 -->
		<div class="particles pointer-events-none absolute inset-0">
			<div v-for="i in 20" :key="i" class="particle absolute h-1 w-1 rounded-full bg-gray-400 opacity-20"></div>
		</div>

		<!-- 主要内容区域 -->
		<div class="main-content relative z-10 min-h-screen flex items-center justify-center px-4">
			<!-- 左侧装饰区域 -->
			<div class="left-section hidden flex-1 flex-col items-center justify-center pr-16 lg:flex">
<!--				<div class="brand-section mb-16 text-center text-white">
					<div class="logo-wrapper mb-8">
						<div class="logo-circle mx-auto h-24 w-24 flex items-center justify-center border-white/30 rounded-full border-4">
							<div class="logo-inner h-12 w-12 rounded-full"></div>
						</div>
					</div>
				</div>-->

				<!--				<div class="decorative-elements relative">
					<div class="floating-element element-1 absolute h-32 w-32 border border-white/20 rounded-full -left-8 -top-16"></div>
					<div class="floating-element element-2 absolute h-24 w-24 border border-white/20 rounded-full -right-12 -top-8"></div>
					<div class="floating-element element-3 absolute left-16 top-16 h-20 w-20 border border-white/20 rounded-full"></div>
				</div>-->
			</div>

			<!-- 右侧登录表单 -->
			<div class="right-section flex flex-1 items-center justify-center">
				<div class="login-card w-full rounded-3xl p-8">
					<div class="mb-8 text-center">
						<h2 class="mb-2 text-gray-700 font-bold text-2xl">{{ appName }}</h2>
					</div>

					<form class="w-full space-y-6" @submit="handleSubmit">
						<!-- 用户名输入 -->
						<div class="input-group relative w-full">
							<div class="input-icon pointer-events-none absolute left-4 z-10 text-gray-500">
								<svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
									<path
										d="M12 12C14.7614 12 17 9.76142 17 7C17 4.23858 14.7614 2 12 2C9.23858 2 7 4.23858 7 7C7 9.76142 9.23858 12 12 12Z"
										stroke="currentColor"
										stroke-width="2"
										stroke-linecap="round"
										stroke-linejoin="round"
									/>
									<path
										d="M20.5899 22C20.5899 18.13 16.7399 15 11.9999 15C7.25991 15 3.40991 18.13 3.40991 22"
										stroke="currentColor"
										stroke-width="2"
										stroke-linecap="round"
										stroke-linejoin="round"
									/>
								</svg>
							</div>
							<input
								v-model="model.username"
								type="text"
								required
								autocomplete="username"
								aria-label="用户名"
								class="modern-input w-full rounded-2xl py-4 pl-12 pr-4 text-gray-700 transition-all duration-300 border-0 placeholder-gray-400"
								placeholder="请输入用户名"
								@keyup.enter="handleLogin"
							/>
						</div>

						<!-- 密码输入 -->
						<div class="input-group relative w-full">
							<div class="input-icon pointer-events-none absolute left-4 z-10 text-gray-500">
								<svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
									<path
										d="M19 11H5C3.89543 11 3 11.8954 3 13V20C3 21.1046 3.89543 22 5 22H19C20.1046 22 21 21.1046 21 20V13C21 11.8954 20.1046 11 19 11Z"
										stroke="currentColor"
										stroke-width="2"
										stroke-linecap="round"
										stroke-linejoin="round"
									/>
									<path
										d="M7 11V7C7 5.67392 7.52678 4.40215 8.46447 3.46447C9.40215 2.52678 10.6739 2 12 2C13.3261 2 14.5979 2.52678 15.5355 3.46447C16.4732 4.40215 17 5.67392 17 7V11"
										stroke="currentColor"
										stroke-width="2"
										stroke-linecap="round"
										stroke-linejoin="round"
									/>
								</svg>
							</div>
							<input
								v-model="model.password"
								type="password"
								required
								autocomplete="current-password"
								aria-label="密码"
								class="modern-input w-full rounded-2xl py-4 pl-12 pr-4 text-gray-700 transition-all duration-300 border-0 placeholder-gray-400"
								placeholder="请输入密码"
								@keyup.enter="handleLogin"
							/>
						</div>

						<!-- 登录按钮 -->
						<button
							:disabled="loading"
							type="submit"
							class="modern-button relative w-full cursor-pointer overflow-hidden rounded-2xl bg-blue-600 px-6 py-4 font-semibold transition-all duration-300 text-lg border-0 text-white"
						>
							<span v-if="!loading" class="flex items-center justify-center">立即登录</span>
							<span v-else class="flex items-center justify-center gap-2">
								<svg class="loading-spinner" width="20" height="20" viewBox="0 0 24 24">
									<circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2" fill="none" stroke-dasharray="60" stroke-dashoffset="60">
										<animate attributeName="stroke-dashoffset" dur="2s" values="60;0" calcMode="linear" repeatCount="indefinite" />
									</circle>
								</svg>
								登录中...
							</span>
						</button>
					</form>

					<!-- 底部装饰 -->
					<div class="mt-8 text-center"></div>
				</div>
			</div>
		</div>
	</div>
</template>

<style scoped>
/* 背景和容器样式 */
.login-container {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	background-image: url('/images/login-bg.jpg');
	background-size: 100%;
	background-repeat: no-repeat;
	background-position: center;
	background-attachment: fixed;
}

.background-overlay {
	background: linear-gradient(135deg, rgba(102, 126, 234, 0.8) 0%, rgba(118, 75, 162, 0.8) 100%);
	backdrop-filter: blur(2px);
}

.animated-bg {
	background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
	animation: shimmer 3s infinite;
}

@keyframes shimmer {
	0% {
		transform: translateX(-100%) translateY(-100%) rotate(45deg);
	}
	100% {
		transform: translateX(100vw) translateY(100vh) rotate(45deg);
	}
}

/* 粒子动画 - 优化性能 */
.particle {
	animation: float 6s infinite ease-in-out;
	will-change: transform, opacity;
}

.particle:nth-child(odd) {
	animation-delay: -2s;
}

.particle:nth-child(3n) {
	animation-delay: -4s;
}

@keyframes float {
	0%,
	100% {
		transform: translateY(0px) rotate(0deg);
		opacity: 0.3;
	}
	50% {
		transform: translateY(-20px) rotate(180deg);
		opacity: 0.8;
	}
}

/* 粒子位置 - 使用更简洁的随机分布 */
.particle:nth-child(4n+1) { top: 20%; left: 15%; }
.particle:nth-child(4n+2) { top: 40%; left: 75%; }
.particle:nth-child(4n+3) { top: 60%; left: 35%; }
.particle:nth-child(4n) { top: 80%; left: 65%; }
.particle:nth-child(5n+1) { left: 25%; }
.particle:nth-child(5n+2) { left: 55%; }
.particle:nth-child(5n+3) { left: 85%; }
.particle:nth-child(7n+1) { animation-delay: -1s; }
.particle:nth-child(7n+2) { animation-delay: -2s; }
.particle:nth-child(7n+3) { animation-delay: -3s; }

/* 主内容区域 */
.main-content {
	max-width: 1400px;
	margin: 0 auto;
}

/* Logo和品牌动画 */
.logo-circle {
	background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1));
	backdrop-filter: blur(10px);
	animation: logoFloat 3s ease-in-out infinite;
}

@keyframes logoFloat {
	0%,
	100% {
		transform: translateY(0px);
	}
	50% {
		transform: translateY(-10px);
	}
}

.logo-inner {
	background: linear-gradient(135deg, #fff, #e0e7ff);
}





/* 右侧区域 */
.right-section {
	max-width: 480px;
	margin: 0 auto;
}

/* 登录卡片 */
.login-card {
	background: rgba(255, 255, 255, 0.85);
	backdrop-filter: blur(30px) saturate(180%);
	-webkit-backdrop-filter: blur(30px) saturate(180%);
	border: 1px solid rgba(255, 255, 255, 0.3);
	box-shadow:
		0 25px 50px rgba(0, 0, 0, 0.15),
		0 12px 24px rgba(0, 0, 0, 0.08),
		inset 0 1px 0 rgba(255, 255, 255, 0.6);
	animation: cardAppear 0.8s ease-out;
	will-change: transform, opacity;
}

@keyframes cardAppear {
	from {
		opacity: 0;
		transform: translateY(30px) scale(0.95);
	}
	to {
		opacity: 1;
		transform: translateY(0) scale(1);
	}
}

/* 输入框图标 */
.input-icon {
	top: 50%;
	transform: translateY(-50%);
	display: flex;
	align-items: center;
	height: 20px;
}

/* 现代化输入框 */
.modern-input {
	background: rgba(255, 255, 255, 0.7);
	backdrop-filter: blur(15px) saturate(150%);
	-webkit-backdrop-filter: blur(15px) saturate(150%);
	border: 1px solid rgba(255, 255, 255, 0.4);
	font-size: 16px;
	outline: none;
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modern-input:focus {
	background: rgba(255, 255, 255, 0.9);
	border-color: rgba(59, 130, 246, 0.6);
	box-shadow:
		0 0 0 3px rgba(59, 130, 246, 0.15),
		0 4px 12px rgba(59, 130, 246, 0.1);
	transform: translateY(-2px);
}

/* 自动填充样式覆盖 */
.modern-input:-webkit-autofill,
.modern-input:-webkit-autofill:hover,
.modern-input:-webkit-autofill:focus,
.modern-input:-webkit-autofill:active {
	-webkit-box-shadow: 0 0 0 30px rgba(255, 255, 255, 0.8) inset !important;
	-webkit-text-fill-color: #374151 !important;
	background-color: transparent !important;
	background-image: none !important;
	transition: background-color 5000s ease-in-out 0s;
	border: 1px solid rgba(0, 0, 0, 0.1) !important;
}

.modern-input:-webkit-autofill:focus {
	-webkit-box-shadow:
		0 0 0 30px rgba(255, 255, 255, 0.95) inset,
		0 0 0 3px rgba(59, 130, 246, 0.1) !important;
	border-color: rgba(59, 130, 246, 0.5) !important;
	transform: translateY(-2px);
}

/* 现代化按钮 */
.modern-button {
	border: none;
	box-shadow:
		0 4px 15px rgba(37, 99, 235, 0.3),
		0 2px 8px rgba(37, 99, 235, 0.2);
	outline: none;
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	position: relative;
	overflow: hidden;
}

.modern-button::before {
	content: '';
	position: absolute;
	top: 0;
	left: -100%;
	width: 100%;
	height: 100%;
	background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
	transition: left 0.5s;
}

.modern-button:hover:not(:disabled)::before {
	left: 100%;
}

.modern-button:hover:not(:disabled) {
	transform: translateY(-3px);
	box-shadow:
		0 12px 30px rgba(37, 99, 235, 0.4),
		0 6px 16px rgba(37, 99, 235, 0.3);
	background: linear-gradient(135deg, #1d4ed8, #2563eb);
}

.modern-button:active:not(:disabled) {
	transform: translateY(-1px);
	box-shadow:
		0 6px 20px rgba(37, 99, 235, 0.3),
		0 3px 10px rgba(37, 99, 235, 0.2);
}

.modern-button:disabled {
	cursor: not-allowed;
	opacity: 0.6;
	transform: none;
}

/* 加载动画 - 优化性能 */
.loading-spinner {
	animation: spin 1s linear infinite;
	will-change: transform;
}

@keyframes spin {
	from {
		transform: rotate(0deg);
	}
	to {
		transform: rotate(360deg);
	}
}

/* 性能优化 */
.modern-input,
.modern-button {
	will-change: transform, box-shadow;
}

/* 减少重绘 */
.login-container {
	transform: translateZ(0);
}

/* 响应式设计 */
@media (max-width: 1024px) {
	.main-content {
		padding-left: 1.5rem;
		padding-right: 1.5rem;
	}

	.login-card {
		padding: 1.5rem;
	}

	.right-section {
		max-width: 420px;
	}
}

@media (max-width: 640px) {
	.login-card {
		padding: 1.5rem;
		margin-left: 1rem;
		margin-right: 1rem;
	}

	.modern-input {
		padding-top: 0.75rem;
		padding-bottom: 0.75rem;
		font-size: 16px;
	}

	.modern-button {
		padding-top: 0.75rem;
		padding-bottom: 0.75rem;
		font-size: 1rem;
	}

	.right-section {
		max-width: 100%;
	}

	.login-card h2 {
		font-size: 1.5rem;
	}
}
</style>
