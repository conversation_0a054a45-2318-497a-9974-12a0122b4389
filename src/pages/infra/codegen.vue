<script setup lang="ts">
import { BIZ_CodegenTable_APIS } from '~/api/biz/admin/codegentableapi'

const props = defineProps({
	params: {
		type: Object,
		default: () => ({}),
	},
})
const computedParams = computed(() => props.params)
const queryModel = reactive({
	pageNo: 1,
	pageSize: 10,
	dataSourceConfigId: undefined,
	scene: undefined,
	tableName: undefined,
	tableComment: undefined,
	remark: undefined,
	moduleName: undefined,
	businessName: undefined,
	className: undefined,
	classComment: undefined,
	author: undefined,
	templateType: undefined,
	frontType: undefined,
	parentMenuId: undefined,
	masterTableId: undefined,
	subJoinColumnId: undefined,
	subJoinMany: undefined,
	treeParentColumnId: undefined,
	treeNameColumnId: undefined,
	createTime: [],
	adminJavaType: undefined,
	...computedParams.value,
})

const modalRef = ref(null)
const { loading, run, list, total, reset } = xUsePageRequest(BIZ_CodegenTable_APIS.getPage, queryModel)
onMounted(() => {
	run()
})

async function handleDelete(id, rowIndex) {
	await toast.confirm('确定删除吗？')
	await BIZ_CodegenTable_APIS.delete(id)
	toast.success('删除成功')
	list.value.splice(rowIndex, 1)
}

function handleOpenModal(type, id) {
	modalRef.value.open(type, id)
}

const columns = [
	{
		label: '表名称',
		prop: 'tableName',
	},
	{
		label: '表描述',
		prop: 'tableComment',
	},
	{
		label: '模块名',
		prop: 'moduleName',
	},
	{
		label: '业务名',
		prop: 'businessName',
	},
	{
		label: '类名称',
		prop: 'className',
	},
	{
		label: '前端类型',
		prop: 'frontType',
	},
	{
		label: '父菜单编号',
		prop: 'parentMenuId',
	},
	{
		label: '创建时间',
		prop: 'createTime',
		formatter: X_DATE_UTILS.formatDate,
	},
	{
		label: '后端javaType',
		prop: 'adminJavaType',
	},
	{
		label: '操作',
		prop: 'action',
		width: 250,
	},
]
/*
 * 导入表
 * */
const importVisible = ref(false)
const importSelectedData = ref([])
const importQueryModel = ref({ dataSourceConfigId: 0, name: '' })
const {
	data: importList,
	run: importQueryRun,
	reset: importQueryReset,
	loading: importQueryLoading,
} = xUseRequest(BIZ_CodegenTable_APIS.getDatabaseTableList, importQueryModel)
const confirmImportLoading = ref(false)
async function handleConfirmImport() {
	console.log('importSelectedData', importSelectedData.value)
	//importSelectedData每一项的name tables = [name1,name2]
	const tableNames = importSelectedData.value.map((item) => item.name)
	console.log('tableNames', tableNames)
	if (tableNames?.length > 0) {
		confirmImportLoading.value = true
		await BIZ_CodegenTable_APIS.createList({ tableNames, dataSourceConfigId: 0 }).finally(() => {
			importVisible.value = false
			confirmImportLoading.value = false
			reset()
		})
	}
}

/** 同步操作  */
const handleSync = async (id) => {
	await toast.confirm('确认同步吗？')
	loading.value = true
	await BIZ_CodegenTable_APIS.syncTable(id).finally(() => {
		loading.value = false
	})
	toast.success('同步成功')
}
/*
 * 编辑表
 * */
const updateVisible = ref(false)
const currentRow = ref(null)
const updateLoading = ref(false)
const updateModel = ref()
async function handleUpdate(row) {
	updateModel.value = {
		table: { parentMenuId: 0, frontType: 60 },
		columns: [],
	}
	updateLoading.value = true
	updateVisible.value = true
	const res = await BIZ_CodegenTable_APIS.get(row.id).finally(() => {
		updateLoading.value = false
	})
	updateModel.value = res
	currentRow.value = row
}
async function handleUpdateColumn() {
	updateLoading.value = true
	await BIZ_CodegenTable_APIS.update(updateModel.value).finally(() => {
		updateLoading.value = false
	})
	updateVisible.value = false
	toast.success('更新成功')
	run()
}
const updateColumns = [
	{
		label: '字段',
		prop: 'columnName',
	},
	{
		label: '字段描述',
		prop: 'columnComment',
	},
	{
		label: 'java属性',
		prop: 'javaField',
	},
	{
		label: 'HTML类型',
		prop: 'htmlType',
		width: 120,
	},
	{
		label: '字典类型',
		prop: 'name',
	},
	{
		label: '查询',
		prop: 'listOperation',
	},
	{
		label: '查询方式',
		prop: 'listOperationCondition',
	},
]
const htmlTypeOptions = [
	{
		label: '文本框',
		value: 'input',
	},
	{
		label: '下拉框',
		value: 'select',
	},
	{
		label: '单选框',
		value: 'radio',
	},
	{
		label: '复选框',
		value: 'checkbox',
	},
	{
		label: '日期控件',
		value: 'datetime',
	},
	{
		label: '富文本控件',
		value: 'editor',
	},
	{
		label: '文件上传',
		value: 'fileUpload',
	},
]
const listOperationConditionOptions = [
	{
		label: '=',
		value: '=',
	},
	{
		label: '!=',
		value: '!=',
	},
	{
		label: '>',
		value: '>',
	},
	{
		label: '>=',
		value: '>=',
	},
	{
		label: '<',
		value: '<',
	},
	{
		label: '<=',
		value: '<=',
	},
	{
		label: 'LIKE',
		value: 'LIKE',
	},
	{
		label: 'BETWEEN',
		value: 'BETWEEN',
	},
]
/*
 * 生成代码
 * */
const downloadLoading = ref(false)
async function handleDownload(row) {
	downloadLoading.value = true
	const res = await BIZ_CodegenTable_APIS.download(row.id).finally(() => {
		downloadLoading.value = false
	})
	X_DOWNLOAD_UTILS.zip(res, 'codegen-' + row.className + '.zip')
}
</script>
<template>
	<div>
		<div class="p-base">
			<BizCardsQuery>
				<BizFormQueryForm :model="queryModel">
					<XFormItem label="数据源配置的编号" prop="dataSourceConfigId">
						<XInput v-model="queryModel.dataSourceConfigId" placeholder="请输入搜索" @keyup.enter="run" />
					</XFormItem>
					<XFormItem label="生成场景" prop="scene">
						<XInput v-model="queryModel.scene" placeholder="请输入搜索" @keyup.enter="run" />
					</XFormItem>
					<XFormItem label="表名称" prop="tableName">
						<XInput v-model="queryModel.tableName" placeholder="请输入搜索" @keyup.enter="run" />
					</XFormItem>
					<XFormItem label="表描述" prop="tableComment">
						<XInput v-model="queryModel.tableComment" placeholder="请输入搜索" @keyup.enter="run" />
					</XFormItem>
					<XFormItem label="备注" prop="remark">
						<XInput v-model="queryModel.remark" placeholder="请输入搜索" @keyup.enter="run" />
					</XFormItem>
					<XFormItem label="模块名" prop="moduleName">
						<XInput v-model="queryModel.moduleName" placeholder="请输入搜索" @keyup.enter="run" />
					</XFormItem>
					<XFormItem label="业务名" prop="businessName">
						<XInput v-model="queryModel.businessName" placeholder="请输入搜索" @keyup.enter="run" />
					</XFormItem>
					<XFormItem label="类名称" prop="className">
						<XInput v-model="queryModel.className" placeholder="请输入搜索" @keyup.enter="run" />
					</XFormItem>
					<XFormItem label="类描述" prop="classComment">
						<XInput v-model="queryModel.classComment" placeholder="请输入搜索" @keyup.enter="run" />
					</XFormItem>
					<XFormItem label="作者" prop="author">
						<XInput v-model="queryModel.author" placeholder="请输入搜索" @keyup.enter="run" />
					</XFormItem>
					<XFormItem label="模板类型" prop="templateType">
						<XSelect v-model="queryModel.templateType" placeholder="请选择搜索" />
					</XFormItem>
					<XFormItem label="前端类型" prop="frontType">
						<XSelect v-model="queryModel.frontType" placeholder="请选择搜索" />
					</XFormItem>
					<XFormItem label="父菜单编号" prop="parentMenuId">
						<XInput v-model="queryModel.parentMenuId" placeholder="请输入搜索" @keyup.enter="run" />
					</XFormItem>
					<XFormItem label="主表的编号" prop="masterTableId">
						<XInput v-model="queryModel.masterTableId" placeholder="请输入搜索" @keyup.enter="run" />
					</XFormItem>
					<XFormItem label="子表关联主表的字段编号" prop="subJoinColumnId">
						<XInput v-model="queryModel.subJoinColumnId" placeholder="请输入搜索" @keyup.enter="run" />
					</XFormItem>
					<XFormItem label="主表与子表是否一对多" prop="subJoinMany">
						<XSelect v-model="queryModel.subJoinMany" placeholder="请选择搜索" />
					</XFormItem>
					<XFormItem label="树表的父字段编号" prop="treeParentColumnId">
						<XInput v-model="queryModel.treeParentColumnId" placeholder="请输入搜索" @keyup.enter="run" />
					</XFormItem>
					<XFormItem label="树表的名字字段编号" prop="treeNameColumnId">
						<XInput v-model="queryModel.treeNameColumnId" placeholder="请输入搜索" @keyup.enter="run" />
					</XFormItem>
					<XFormItem label="后端javaType" prop="adminJavaType">
						<XSelect v-model="queryModel.adminJavaType" placeholder="请选择搜索" />
					</XFormItem>
					<div class="absolute bottom-0.2rem right-0.2rem flex items-center justify-end gap-xs">
						<BizButtonsReset :loading="loading" @click="reset" />
						<BizButtonsQuery :loading="loading" @click="run" />
					</div>
				</BizFormQueryForm>
			</BizCardsQuery>
			<div class="flex items-center justify-end py-xs gap-sm">
				<BizButtonsCreate v-if="hasPermission('promotion:seckill-activity:update')" @click="handleOpenModal('create')" />
				<BizButtonsImport @click="importVisible = true" />
			</div>
			<BizCardsTable v-loading="loading">
				<BizTablesList :data="list" :columns="columns">
					<template #action="{ row }">
						<div class="flex items-center justify-center gap-xs">
							<XButton text-mode @click="handleSync(row.id)">同步</XButton>
							<BizButtonsUpdate @click="handleUpdate(row)" />
							<BizButtonsDelete @click="handleDelete(row.id, rowIndex)" />
							<XButton :loading="downloadLoading" @click="handleDownload(row)">生成代码</XButton>
						</div>
					</template>
				</BizTablesList>
				<div class="flex justify-end mt-base">
					<XPagination v-model="queryModel.pageNo" v-model:page-size="queryModel.pageSize" :total="total" @change="run" />
				</div>
			</BizCardsTable>
		</div>
		<!--导入弹窗-->
		<x-modal v-model="importVisible" title="导入">
			<BizFormQueryForm :model="importQueryModel" class="flex justify-between">
				<XFormItem label="表名称" prop="name">
					<XInput v-model="importQueryModel.name" placeholder="请输入搜索" @keyup.enter="importQueryRun" />
				</XFormItem>
				<div class="flex gap-sm">
					<BizButtonsReset :loading="importQueryLoading" @click="importQueryReset" />
					<BizButtonsQuery :loading="importQueryLoading" @click="importQueryRun" />
				</div>
			</BizFormQueryForm>
			<BizTablesList
				v-model:selected="importSelectedData"
				class="mt-base"
				:data="importList"
				selectable
				value-key="name"
				:columns="[
					{ label: '名称', prop: 'name' },
					{ label: '描述', prop: 'comment' },
				]"
			></BizTablesList>
			<div class="flex justify-end my-base">
				<XButton :loading="confirmImportLoading" @click="handleConfirmImport">确 认</XButton>
			</div>
		</x-modal>
		<!--生成配置 配置表字段-->
		<x-modal v-model="updateVisible" :loading="updateLoading" title="编辑字段">
			<div>
				<x-form-item label="父级菜单">
					<x-input v-model="updateModel.table.parentMenuId"></x-input>
				</x-form-item>
				<x-form-item class="mt-base" label="前端类型">
					<x-input v-model="updateModel.table.frontType"></x-input>
				</x-form-item>
				<x-form-item class="mt-base" label="模块名">
					<x-input v-model="updateModel.table.moduleName"></x-input>
				</x-form-item>
				<x-form-item class="mt-base" label="类名">
					<x-input v-model="updateModel.table.className"></x-input>
				</x-form-item>
				<BizTablesList class="mt-base" :data="updateModel.columns" :columns="updateColumns">
					<template #htmlType="{ row }">
						<XSelect v-model="row.htmlType" :clearable="false" label-key="label" value-key="value" :options="htmlTypeOptions"></XSelect>
					</template>
					<template #listOperationCondition="{ row }">
						<XSelect
							v-model="row.listOperationCondition"
							:clearable="false"
							label-key="label"
							value-key="value"
							:options="listOperationConditionOptions"
						></XSelect>
					</template>
				</BizTablesList>
				<div class="flex justify-end my-base">
					<BizButtonsSave @click="handleUpdateColumn" />
				</div>
			</div>
		</x-modal>
	</div>
</template>
<style scoped></style>
