<route lang="json">
{
	"meta": {
		"title": "定时任务"
	}
}
</route>
<script setup lang="ts">
const props = defineProps({
	params: {
		type: Object,
		default: () => ({}),
	},
})
const computedParams = computed(() => props.params)
const queryModel = reactive({
	pageNo: 1,
	pageSize: 10,
	name: null,
	status: null,
	handlerName: null,
	handlerParam: null,
	cronExpression: null,
	retryCount: null,
	retryInterval: null,
	monitorTimeout: null,
	createTime: [],
	...computedParams.value,
})

const modalRef = ref(null)
const BizTablesListRef = ref()
const { loading, run, list, total, reset } = xUsePageRequest(BIZ_Job_APIS.getPage, queryModel)
onMounted(() => {
	run()
})

async function handleDelete(id) {
	await toast.confirm('确定删除吗？')
	loading.value = true
	await BIZ_Job_APIS.delete(id).finally(() => {
		loading.value = false
	})
	toast.success('删除成功')
	if (!BizTablesListRef.value?.getTableRef()?.deleteNode({ id })) {
		reset()
	}
}

function handleOpenModal(type, id) {
	modalRef.value.open(type, id, computedParams.value)
}

const columns = [
	{
		label: '任务名称',
		prop: 'name',
	},
	{
		label: '任务状态',
		prop: 'status',
	},
	{
		label: '处理器的名字',
		prop: 'handlerName',
	},
	{
		label: '处理器的参数',
		prop: 'handlerParam',
	},
	{
		label: 'CRON 表达式',
		prop: 'cronExpression',
	},
	{
		label: '重试次数',
		prop: 'retryCount',
	},
	{
		label: '重试间隔',
		prop: 'retryInterval',
	},
	{
		label: '监控超时时间',
		prop: 'monitorTimeout',
	},
	{
		label: '创建时间',
		prop: 'createTime',
		width: 150,
		formatter: X_DATE_UTILS.formatDate,
	},
	{
		label: '操作',
		prop: 'action',
		width: 150,
	},
]
const updateStatusLoading = ref(false)
const handleChangeStatus = async (row) => {
	try {
		// 修改状态的二次确认
		const text = row.status === 2 ? '开启' : '关闭'
		updateStatusLoading.value = true
		await toast.confirm('确认要' + text + '定时任务编号为"' + row.id + '"的数据项?')
		const status = row.status === 2 ? 1 : 2
		await BIZ_Job_APIS.updateJobStatus(row.id, status)
		toast.success(text + '成功')
		// 刷新列表
		run()
	} finally {
		updateStatusLoading.value = false
	}
}
const runLoading = ref(false)
const handleRun = async (row) => {
	try {
		// 二次确认
		await toast.confirm('确认要立即执行一次' + row.name + '?')
		runLoading.value = true
		// 提交执行
		await BIZ_Job_APIS.runJob(row.id)
		toast.success('执行成功')
		// 刷新列表
		await run()
	} finally {
		runLoading.value = false
	}
}
</script>
<template>
	<BizLayoutPageContentContainer>
		<BizFormModalsJob ref="modalRef" @success="run()" />
		<BizCardsQuery>
			<BizFormQueryForm :model="queryModel">
				<XFormItem label="任务名称" prop="name">
					<XInput v-model="queryModel.name" placeholder="请输入搜索" @keyup.enter="run" />
				</XFormItem>
				<XFormItem label="任务状态" prop="status">
					<XSelect v-model="queryModel.status" placeholder="请选择搜索" tag-group-name="" />
				</XFormItem>
				<XFormItem label="处理器的名字" prop="handlerName">
					<XInput v-model="queryModel.handlerName" placeholder="请输入搜索" @keyup.enter="run" />
				</XFormItem>
				<XFormItem label="处理器的参数" prop="handlerParam">
					<XInput v-model="queryModel.handlerParam" placeholder="请输入搜索" @keyup.enter="run" />
				</XFormItem>
				<XFormItem label="CRON 表达式" prop="cronExpression">
					<XInput v-model="queryModel.cronExpression" placeholder="请输入搜索" @keyup.enter="run" />
				</XFormItem>
				<XFormItem label="重试次数" prop="retryCount">
					<XInput v-model="queryModel.retryCount" placeholder="请输入搜索" @keyup.enter="run" />
				</XFormItem>
				<XFormItem label="重试间隔" prop="retryInterval">
					<XInput v-model="queryModel.retryInterval" placeholder="请输入搜索" @keyup.enter="run" />
				</XFormItem>
				<XFormItem label="监控超时时间" prop="monitorTimeout">
					<XInput v-model="queryModel.monitorTimeout" placeholder="请输入搜索" @keyup.enter="run" />
				</XFormItem>
				<XFormItem label="创建时间" prop="createTime">
					<XDateTimePicker
						v-model="queryModel.createTime"
						value-format="YYYY-MM-DD HH:mm:ss"
						range
						type="datetime"
						placeholder="请选择搜索"
						@clear="reset"
					/>
				</XFormItem>
			</BizFormQueryForm>
			<div class="flex items-center justify-end gap-xs">
				<BizButtonsReset @click="reset" />
				<BizButtonsQuery @click="run" />
			</div>
		</BizCardsQuery>
		<div class="flex items-center justify-between py-xs">
			<div></div>
			<div class="flex items-center">
				<BizButtonsCreate v-if="hasPermission('infra:job:create')" @click="handleOpenModal('create')" />
			</div>
		</div>
		<BizCardsTable v-loading="loading">
			<BizTablesList ref="BizTablesListRef" :data="list" :columns="columns">
				<template #action="{ row, rowIndex }">
					<div class="flex items-center justify-center gap-xs">
						<XButton :loading="updateStatusLoading" @click="handleChangeStatus(row)">{{ row.status === 2 ? '开启' : '暂停' }}</XButton>
						<XButton text-mode :loading="updateStatusLoading" @click="handleRun(row)">执行一次</XButton>
						<BizButtonsUpdate v-if="hasPermission('infra:job:update')" @click="handleOpenModal('update', row.id)" />
						<BizButtonsDelete v-if="hasPermission('infra:job:delete')" @click="handleDelete(row.id)" />
					</div>
				</template>
			</BizTablesList>
			<div class="flex justify-end mt-base">
				<XPagination v-model="queryModel.pageNo" v-model:page-size="queryModel.pageSize" :total="total" @change="run" />
			</div>
		</BizCardsTable>
	</BizLayoutPageContentContainer>
</template>
<style scoped></style>
