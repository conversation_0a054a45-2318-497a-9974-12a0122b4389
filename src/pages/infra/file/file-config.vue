<route lang="json">
{
	"meta": {
		"title": "文件配置"
	}
}
</route>
<script setup lang="ts">
const props = defineProps({
	params: {
		type: Object,
		default: () => ({}),
	},
})
const computedParams = computed(() => props.params)
const queryModel = reactive({
	pageNo: 1,
	pageSize: 10,
	name: undefined,
	storage: undefined,
	remark: undefined,
	master: undefined,
	config: undefined,
	createTime: [],
	...computedParams.value,
})

const modalRef = ref(null)
const BizTablesListRef = ref()
const { loading, run, list, total, reset } = xUsePageRequest(BIZ_FileConfig_APIS.getPage, queryModel)
onMounted(() => {
	run()
})

async function handleDelete(id) {
	await toast.confirm('确定删除吗？')
	loading.value = true
	await BIZ_FileConfig_APIS.delete(id).finally(() => {
		loading.value = false
	})
	toast.success('删除成功')
	if (!BizTablesListRef.value?.getTableRef()?.deleteNode({ id })) {
		reset()
	}
}

function handleOpenModal(type, id) {
	modalRef.value.open(type, id, computedParams.value)
}

const columns = [
	{
		label: '配置名',
		prop: 'name',
	},
	{
		label: '存储器',
		prop: 'storage',
	},
	{
		label: '备注',
		prop: 'remark',
	},
	{
		label: '是否为主配置',
		prop: 'master',
	},
	{
		label: '创建时间',
		prop: 'createTime',
		width: 150,
		formatter: X_DATE_UTILS.formatDate,
	},
	{
		label: '操作',
		prop: 'action',
		width: 180,
	},
]

/** 测试按钮操作 */
const handleTest = async (id) => {
	const res = await BIZ_FileConfig_APIS.test(id)
	toast.confirm('上传成功：' + res)
}

/** 主配置按钮操作 */
const handleMaster = async (id) => {
	await toast.confirm('是否确认修改配置编号为"' + id + '"的数据项为主配置?')
	loading.value = true
	await BIZ_FileConfig_APIS.updateMaster(id).finally(() => {
		loading.value = false
	})
	reset()
}
</script>
<template>
	<BizLayoutPageContentContainer>
		<BizFormModalsFileConfig ref="modalRef" @success="run()" />
		<BizCardsQuery>
			<BizFormQueryForm :model="queryModel">
				<XFormItem label="配置名" prop="name">
					<XInput v-model="queryModel.name" placeholder="请输入搜索" @keyup.enter="run" />
				</XFormItem>
				<XFormItem label="备注" prop="remark">
					<XInput v-model="queryModel.remark" placeholder="请输入搜索" @keyup.enter="run" />
				</XFormItem>
				<XFormItem label="创建时间" prop="createTime">
					<XDateTimePicker
						v-model="queryModel.createTime"
						value-format="YYYY-MM-DD HH:mm:ss"
						range
						type="datetime"
						placeholder="请选择搜索"
						@clear="reset"
					/>
				</XFormItem>
				<div class="absolute bottom-0.2rem right-0.2rem flex items-center justify-end gap-xs">
					<BizButtonsReset @click="reset" />
					<BizButtonsQuery @click="run" />
				</div>
			</BizFormQueryForm>
		</BizCardsQuery>
		<div class="flex items-center justify-between py-xs">
			<div></div>
			<div class="flex items-center">
				<BizButtonsCreate v-if="hasPermission('infra:file-config:create')" @click="handleOpenModal('create')" />
			</div>
		</div>
		<BizCardsTable v-loading="loading">
			<BizTablesList ref="BizTablesListRef" :data="list" :columns="columns">
				<template #action="{ row, rowIndex }">
					<div class="flex items-center justify-center gap-xs">
						<x-button text-mode :disabled="row.master" @click="handleMaster(row.id)">主配置</x-button>
						<x-button text-mode @click="handleTest(row.id)">测试</x-button>
						<BizButtonsUpdate v-if="hasPermission('infra:file-config:update')" @click="handleOpenModal('update', row.id)" />
						<BizButtonsDelete v-if="hasPermission('infra:file-config:delete')" @click="handleDelete(row.id)" />
					</div>
				</template>
			</BizTablesList>
			<div class="flex justify-end mt-base">
				<XPagination v-model="queryModel.pageNo" v-model:page-size="queryModel.pageSize" :total="total" @change="run" />
			</div>
		</BizCardsTable>
	</BizLayoutPageContentContainer>
</template>
<style scoped></style>
