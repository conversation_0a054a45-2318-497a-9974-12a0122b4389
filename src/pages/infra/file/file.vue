<route lang="json">
{
	"meta": {
		"title": "文件"
	}
}
</route>
<script setup lang="ts">
const props = defineProps({
	params: {
		type: Object,
		default: () => ({}),
	},
})
const computedParams = computed(() => props.params)
const queryModel = reactive({
	pageNo: 1,
	pageSize: 10,
	configId: undefined,
	name: undefined,
	path: undefined,
	url: undefined,
	type: undefined,
	size: undefined,
	createTime: [],
	...computedParams.value,
})

const modalRef = ref(null)
const BizTablesListRef = ref()
const { loading, run, list, total, reset } = xUsePageRequest(BIZ_File_APIS.getPage, queryModel)
onMounted(() => {
	run()
})

async function handleDelete(id) {
	await toast.confirm('确定删除吗？')
	loading.value = true
	await BIZ_File_APIS.delete(id).finally(() => {
		loading.value = false
	})
	toast.success('删除成功')
	if (!BizTablesListRef.value?.getTableRef()?.deleteNode({ id })) {
		reset()
	}
}

function handleOpenModal(type, id) {
	modalRef.value.open(type, id, computedParams.value)
}

const columns = [
	{
		label: '配置编号',
		prop: 'configId',
		width: 100,
	},
	{
		label: '文件名',
		prop: 'name',
	},
	{
		label: '文件',
		prop: 'url',
	},
	{
		label: '文件类型',
		prop: 'type',
	},
	{
		label: '文件大小',
		prop: 'size',
	},
	{
		label: '创建时间',
		prop: 'createTime',
		width: 150,
		formatter: X_DATE_UTILS.formatDate,
	},
	{
		label: '操作',
		prop: 'action',
	},
]
</script>
<template>
	<BizLayoutPageContentContainer>
		<BizFormModalsFile ref="modalRef" @success="run()" />
		<BizCardsQuery>
			<BizFormQueryForm :model="queryModel">
				<XFormItem label="配置编号" prop="configId">
					<XInput v-model="queryModel.configId" placeholder="请输入搜索" @keyup.enter="run" />
				</XFormItem>
				<XFormItem label="文件名" prop="name">
					<XInput v-model="queryModel.name" placeholder="请输入搜索" @keyup.enter="run" />
				</XFormItem>
				<XFormItem label="文件 URL" prop="url">
					<XInput v-model="queryModel.url" placeholder="请输入搜索" @keyup.enter="run" />
				</XFormItem>
				<XFormItem label="文件类型" prop="type">
					<XSelect v-model="queryModel.type" placeholder="请选择搜索" tag-group-name="" />
				</XFormItem>
				<XFormItem label="文件大小" prop="size">
					<XInput v-model="queryModel.size" placeholder="请输入搜索" @keyup.enter="run" />
				</XFormItem>
				<XFormItem label="创建时间" prop="createTime">
					<XDateTimePicker
						v-model="queryModel.createTime"
						value-format="YYYY-MM-DD HH:mm:ss"
						range
						type="datetime"
						placeholder="请选择搜索"
						@clear="reset"
					/>
				</XFormItem>
				<div class="absolute bottom-0.2rem right-0.2rem flex items-center justify-end gap-xs">
					<BizButtonsReset @click="reset" />
					<BizButtonsQuery @click="run" />
				</div>
			</BizFormQueryForm>
		</BizCardsQuery>
		<div class="flex items-center justify-between py-xs">
			<div></div>
			<div class="flex items-center">
				<!--				<BizButtonsCreate v-if="hasPermission('infra:file:create')" @click="handleOpenModal('create')" />-->
			</div>
		</div>
		<BizCardsTable v-loading="loading">
			<BizTablesList ref="BizTablesListRef" :data="list" :columns="columns">
				<template #url="{ row }">
					<XFileLink :name="row.url" :url="row.url" />
				</template>
				<template #action="{ row, rowIndex }">
					<div class="flex items-center justify-center gap-xs">
						<BizButtonsUpdate v-if="hasPermission('infra:file:update')" @click="handleOpenModal('update', row.id)" />
						<BizButtonsDelete v-if="hasPermission('infra:file:delete')" @click="handleDelete(row.id)" />
					</div>
				</template>
			</BizTablesList>
			<div class="flex justify-end mt-base">
				<XPagination v-model="queryModel.pageNo" v-model:page-size="queryModel.pageSize" :total="total" @change="run" />
			</div>
		</BizCardsTable>
	</BizLayoutPageContentContainer>
</template>
<style scoped></style>
