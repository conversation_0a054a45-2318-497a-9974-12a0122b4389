<route lang="json">
{
	"name": "404"
}
</route>
<script setup lang="ts">
import useTyped from '~/composables/useTyped'
import { safeResolve } from '~/composables/path'

const typedRef = useTyped([' is not found!'])
</script>

<template>
	<div class="hfull wfull flex flex-wrap items-center justify-center text-center">
		<div class="font-blod w-400px">
			<div class="code text-7xl">404</div>
			<div ref="typedRef" class="content mb-5 text-3xl">The Page</div>
			<RouterLink :to="safeResolve('/')">
				<button class="rounded bg-light-800 px-5 py-2 transition text-lg" hover="shadow-md" dark="text-black">Go Home</button>
			</RouterLink>
		</div>
		<img :src="safeResolve('/notFound/32.svg')" class="w-55%" alt="page not found" />
	</div>
</template>

<style></style>
<!--{
	"meta": {
		"title": "404",
		"layout": "notFound"
	}
}-->
<!--<route lang="json">
{
	"meta": {
		"title": "400",
		"layout": "demo"
	}
}
</route>-->
