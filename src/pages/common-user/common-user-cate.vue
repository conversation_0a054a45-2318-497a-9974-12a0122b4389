<route lang="json">
{
	"meta": {
		"title": "用户分类"
	}
}
</route>
<script setup lang="ts">
const props = defineProps({
	params: {
		type: Object,
		default: () => ({}),
	},
})
const computedParams = computed(() => props.params)
const queryModel = reactive({
	pageNo: 1,
	pageSize: 10,
	name: undefined,
	description: undefined,
	parentId: undefined,
	sort: undefined,
	createTime: [],
	mark: undefined,
	...computedParams.value,
})

const modalRef = ref(null)
const BizTablesListRef = ref()
const { loading, run, list, total, reset } = xUsePageRequest(BIZ_CommonUserCate_APIS.getPage, queryModel)
onMounted(() => {
	run()
})

async function handleDelete(id) {
	await toast.confirm('确定删除吗？')
	loading.value = true
	await BIZ_CommonUserCate_APIS.delete(id).finally(() => {
		loading.value = false
	})
	toast.success('删除成功')
	if (!BizTablesListRef.value?.getTableRef()?.deleteNode({ id })) {
		reset()
	}
}

function handleOpenModal(type, id) {
	modalRef.value.open(type, id)
}

const columns = [
	{
		label: '分类名称',
		prop: 'name',
	},
	{
		label: '人数',
		prop: 'userTotal',
	},
	{
		label: '描述',
		prop: 'description',
	},
	{
		label: '上级分类',
		prop: 'parentId',
	},
	{
		label: '排序',
		prop: 'sort',
	},
	{
		label: '创建时间',
		prop: 'createTime',
		formatter: X_DATE_UTILS.formatDate,
	},
	{
		label: '备注',
		prop: 'mark',
	},
	{
		label: '操作',
		prop: 'action',
	},
]
</script>
<template>
	<BizLayoutPageContentContainer>
		<BizFormModalsCommonUserCate
			ref="modalRef"
			@success="
				(node) => {
					BizTablesListRef?.getTableRef()?.saveNode(node)
				}
			"
		/>
		<BizCardsQuery>
			<BizFormQueryForm :model="queryModel">
				<XFormItem label="分类名称" prop="name">
					<XInput v-model="queryModel.name" placeholder="请输入搜索" @keyup.enter="run" />
				</XFormItem>
				<XFormItem label="上级分类" prop="parentId">
					<XInput v-model="queryModel.parentId" placeholder="请输入搜索" @keyup.enter="run" />
				</XFormItem>
				<XFormItem label="排序" prop="sort">
					<XInput v-model="queryModel.sort" placeholder="请输入搜索" @keyup.enter="run" />
				</XFormItem>
				<XFormItem label="创建时间" prop="createTime">
					<XDateTimePicker v-model="queryModel.createTime" value-format="YYYY-MM-DD HH:mm:ss" range type="datetime" placeholder="请选择搜索" />
				</XFormItem>
				<XFormItem label="备注" prop="mark">
					<XInput v-model="queryModel.mark" placeholder="请输入搜索" @keyup.enter="run" />
				</XFormItem>
				<div class="absolute bottom-0.2rem right-0.2rem flex items-center justify-end gap-xs">
					<BizButtonsReset @click="reset" />
					<BizButtonsQuery @click="run" />
				</div>
			</BizFormQueryForm>
		</BizCardsQuery>
		<div class="flex items-center justify-between py-xs">
			<div></div>
			<div class="flex items-center">
				<BizButtonsCreate v-if="hasPermission('x:common-user-cate:create')" @click="handleOpenModal('create')" />
			</div>
		</div>
		<BizCardsTable v-loading="loading">
			<BizTablesList ref="BizTablesListRef" treeable :data="list" :columns="columns">
				<template #action="{ row, rowIndex }">
					<div class="flex items-center justify-center gap-xs">
						<x-button v-if="hasPermission('x:common-user-cate:update')" text="新增子类" text-mode @click="handleOpenModal('create', row.id)" />
						<BizButtonsUpdate v-if="hasPermission('x:common-user-cate:update')" @click="handleOpenModal('update', row.id)" />
						<BizButtonsDelete v-if="hasPermission('x:common-user-cate:delete')" @click="handleDelete(row.id)" />
					</div>
				</template>
			</BizTablesList>
			<div class="flex justify-end mt-base">
				<XPagination v-model="queryModel.pageNo" v-model:page-size="queryModel.pageSize" :total="total" @change="run" />
			</div>
		</BizCardsTable>
	</BizLayoutPageContentContainer>
</template>
<style scoped></style>
