<route lang="json">
{
	"meta": {
		"title": "用户"
	}
}
</route>
<script setup lang="ts">
const props = defineProps({
	params: {
		type: Object,
		default: () => ({}),
	},
})
const computedParams = computed(() => props.params)
const queryModel = reactive({
	isRegister: '是',
	cateId: null,
	pageNo: 1,
	pageSize: 10,
	createTime: [],
	username: undefined,
	password: undefined,
	passwordUpdateTime: [],
	nickname: undefined,
	avatar: undefined,
	phone: undefined,
	email: undefined,
	gender: undefined,
	birthday: undefined,
	realName: undefined,
	idCard: undefined,
	idCardVerified: undefined,
	platform: undefined,
	openid: undefined,
	unionid: undefined,
	isForbidden: undefined,
	lastLoginTime: [],
	lastLoginIp: undefined,
	loginFailCount: undefined,
	accountLockTime: [],
	level: undefined,
	points: undefined,
	source: undefined,
	inviterId: undefined,
	inviteCode: undefined,
	country: undefined,
	province: undefined,
	city: undefined,
	tags: undefined,
	...computedParams.value,
})

const modalRef = ref(null)
const BizTablesListRef = ref()
const { loading, run, list, total, reset } = xUsePageRequest(BIZ_CommonUser_APIS.getPage, queryModel)
onMounted(() => {
	run()
})

async function handleDelete(id) {
	await toast.confirm('确定删除吗？')
	loading.value = true
	await BIZ_CommonUser_APIS.delete(id).finally(() => {
		loading.value = false
	})
	toast.success('删除成功')
	if (!BizTablesListRef.value?.getTableRef()?.deleteNode({ id })) {
		reset()
	}
}

function handleOpenModal(type, id) {
	modalRef.value.open(type, id)
}

const columns = [
	{
		label: '路径',
		prop: 'catePath',
	},
	{
		label: '姓名',
		prop: 'realName',
	},
	{
		label: '用户名(账号)',
		prop: 'username',
	},
	{
		label: '密码',
		prop: 'password',
	},
	{
		label: '手机号',
		prop: 'phone',
	},
	/*	{
		label: '是否注册',
		prop: 'phone',
		formatter(_, row) {
			console.log('row', row)
			if (row.phone) {
				return `<span class="color-success">已注册(${row.phone})</span>`
			}
			return `<span class="color-warning">未注册</span>`
		},
	},*/
	/*	{
		label: '头像',
		prop: 'avatar',
	},*/
	/*	{
		label: '平台',
		prop: 'platform',
	},*/
	{
		label: '会员到期时间',
		prop: 'vipEndTime',
	},
	{
		label: '最后登录时间',
		prop: 'lastLoginTime',
		formatter: X_DATE_UTILS.formatDate,
	},
	/*	{
		label: '用户等级',
		prop: 'level',
	},
	{
		label: '用户标签',
		prop: 'tags',
	},*/
	{
		label: '创建时间',
		prop: 'createTime',
		formatter: X_DATE_UTILS.formatDate,
	},
	{
		label: '操作',
		prop: 'action',
	},
]
const exportLoading = ref(false)
async function handleExport() {
	exportLoading.value = true
	const res = await BIZ_CommonUser_APIS.export().finally(() => {
		exportLoading.value = false
	})
	X_DOWNLOAD_UTILS.excel(res, '用户列表.xlsx')
}
const userCateList = ref([])
async function getUserCateList() {
	const res = await BIZ_CommonUserCate_APIS.getList()
	userCateList.value = res
}
onMounted(() => {
	getUserCateList()
})
</script>
<template>
	<BizLayoutPageContentContainer>
		<BizFormModalsCommonUser ref="modalRef" @success="run()" />
		<BizCardsQuery>
			<BizFormQueryForm :model="queryModel">
				<!--				<XFormItem label="分类" prop="cateId">
					<XCascader v-model="queryModel.cateId" is-flat :data-load-func="BIZ_CommonUserCate_APIS.getList" />
				</XFormItem>-->
				<XFormItem label="真实姓名" prop="realName">
					<XInput v-model="queryModel.realName" placeholder="请输入搜索" @keyup.enter="run" />
				</XFormItem>
				<XFormItem label="账号" prop="username">
					<XInput v-model="queryModel.username" placeholder="请输入搜索" @keyup.enter="run" />
				</XFormItem>
				<XFormItem label="手机号" prop="phone">
					<XInput v-model="queryModel.phone" placeholder="请输入搜索" @keyup.enter="run" />
				</XFormItem>
				<!--				<XFormItem label="邮箱" prop="email">
					<XInput v-model="queryModel.email" placeholder="请输入搜索" @keyup.enter="run" />
				</XFormItem>-->
				<!--'identity/subject/stage/education',-->
				<XFormItem label="创建时间" prop="createTime">
					<XDateTimePicker v-model="queryModel.createTime" value-format="YYYY-MM-DD" type="date" placeholder="请选择搜索" />
				</XFormItem>
				<div class="absolute bottom-0.2rem right-0.2rem flex items-center justify-end gap-xs">
					<BizButtonsReset @click="reset" />
					<BizButtonsQuery @click="run" />
				</div>
			</BizFormQueryForm>
		</BizCardsQuery>
		<div class="flex items-center justify-between py-xs">
			<div></div>
			<div class="flex items-center gap-xs">
				<BizButtonsExport v-if="hasPermission('x:common-user:export')" :loading="exportLoading" @click="handleExport" />
				<BizButtonsCreate v-if="hasPermission('x:common-user:create')" @click="handleOpenModal('create')" />
			</div>
		</div>
		<div v-loading="loading" class="flex flex-1 overflow-hidden gap-base">
			<div class="rounded bg-white p-xs">
				<x-tree v-model="queryModel.cateId" :data="userCateList" leaf-only accordion @node-check="run"></x-tree>
			</div>
			<BizCardsTable>
				<BizTablesList ref="BizTablesListRef" :data="list" :columns="columns">
					<template #vipEndTime="{ row }">
						<div class="color-success" :class="{ '!color-danger': row.vipEndTime < Date.now() + 5 * 24 * 60 * 60 * 1000 }">
							<p>{{ X_DATE_UTILS.getRemainingTime(row.vipEndTime) }}</p>
							<p class="text-xs">{{ X_DATE_UTILS.formatDate(row.vipEndTime) }}</p>
						</div>
					</template>
					<template #action="{ row, rowIndex }">
						<div class="flex items-center justify-center gap-xs">
							<BizButtonsUpdate v-if="hasPermission('x:common-user:update')" @click="handleOpenModal('update', row.id)" />
							<BizButtonsDelete v-if="hasPermission('x:common-user:delete')" @click="handleDelete(row.id)" />
						</div>
					</template>
				</BizTablesList>
				<div class="flex justify-end mt-base">
					<XPagination v-model="queryModel.pageNo" v-model:page-size="queryModel.pageSize" :total="total" @change="run" />
				</div>
			</BizCardsTable>
		</div>
	</BizLayoutPageContentContainer>
</template>
<style scoped></style>
