<route lang="json">
  {
  "meta": {
  "title": "车辆绑定铅封"
  }
  }
</route>
<script setup lang="ts">
  const props = defineProps({
    params: {
      type: Object,
      default: () => ({}),
    },
  })
  const computedParams = computed(() => props.params)
  const queryModel = reactive({
              pageNo: 1,
        pageSize: 10,
                      transportOrderId: null,
                      vehicleId: null,
                      sealId: null,
                      createTime: [],
    ...computedParams.value,
  })

  const modalRef = ref(null)
  const BizTablesListRef = ref()
  const { loading, run, list, total, reset } = xUsePageRequest(BIZ_JnrmVehicleSeal_APIS.getPage, queryModel)
  onMounted(() => {
    run()
  })

  async function handleDelete(id) {
    await toast.confirm('确定删除吗？')
    loading.value = true
    await BIZ_JnrmVehicleSeal_APIS.delete(id).finally(() => {
      loading.value = false
    })
    toast.success('删除成功')
    if (!BizTablesListRef.value?.getTableRef()?.deleteNode({ id })) {
      reset()
    }
  }

  function handleOpenModal(type, id) {
    modalRef.value.open(type, id, computedParams.value)
  }

  const columns = [
                {
                  label: '运单ID',
                  prop: 'transportOrderId',
                },
                {
                  label: '车辆ID',
                  prop: 'vehicleId',
                },
                {
                  label: '铅封ID',
                  prop: 'sealId',
                },
                {
                  label: '创建时间',
                  prop: 'createTime',
                  width: 150,
                  formatter: X_DATE_UTILS.formatDate,
                },
    {
      label: '操作',
      prop: 'action',
    },
  ]
</script>
<template>
  <BizLayoutPageContentContainer>
    <BizFormModalsJnrmVehicleSeal ref="modalRef" @success="run()" />
    <BizCardsQuery>
      <BizFormQueryForm :model="queryModel">
                    <XFormItem label="运单ID" prop="transportOrderId">
                      <XInput v-model="queryModel.transportOrderId" placeholder="请输入搜索" @keyup.enter="run" />
                    </XFormItem>
                    <XFormItem label="车辆ID" prop="vehicleId">
                      <XInput v-model="queryModel.vehicleId" placeholder="请输入搜索" @keyup.enter="run" />
                    </XFormItem>
                    <XFormItem label="铅封ID" prop="sealId">
                      <XInput v-model="queryModel.sealId" placeholder="请输入搜索" @keyup.enter="run" />
                    </XFormItem>
                        <XFormItem label="创建时间" prop="createTime">
                          <XDateTimePicker v-model="queryModel.createTime"  value-format="YYYY-MM-DD HH:mm:ss"  range type="datetime" placeholder="请选择搜索" @clear="reset" />
                        </XFormItem>
      </BizFormQueryForm>
      <div class="absolute bottom-0.2rem right-0.2rem flex items-center justify-end gap-xs">
        <BizButtonsReset @click="reset" />
        <BizButtonsQuery @click="run" />
      </div>
    </BizCardsQuery>
    <div class="flex items-center justify-between py-xs">
      <div></div>
      <div class="flex items-center">
        <BizButtonsCreate @click="handleOpenModal('create')" v-if="hasPermission('x:jnrm-vehicle-seal:create')" />
      </div>
    </div>
    <BizCardsTable v-loading="loading">
      <BizTablesList ref="BizTablesListRef" :data="list" :columns="columns">
        <template #action="{ row, rowIndex }">
          <div class="flex items-center justify-center gap-xs">
            <BizButtonsUpdate v-if="hasPermission('x:jnrm-vehicle-seal:update')" @click="handleOpenModal('update', row.id)" />
            <BizButtonsDelete v-if="hasPermission('x:jnrm-vehicle-seal:delete')" @click="handleDelete(row.id)" />
          </div>
        </template>
      </BizTablesList>
      <div class="flex justify-end mt-base">
        <XPagination v-model="queryModel.pageNo" v-model:page-size="queryModel.pageSize" :total="total" @change="run" />
      </div>
    </BizCardsTable>
  </BizLayoutPageContentContainer>
</template>
<style scoped></style>
