<route lang="json">
{
	"meta": {
		"title": "角色信息"
	}
}
</route>
<script setup lang="ts">
const props = defineProps({
	params: {
		type: Object,
		default: () => ({}),
	},
})
const computedParams = computed(() => props.params)
const queryModel = reactive({
	pageNo: 1,
	pageSize: 10,
	name: undefined,
	code: undefined,
	sort: undefined,
	dataScope: undefined,
	dataScopeDeptIds: undefined,
	status: undefined,
	type: undefined,
	remark: undefined,
	createTime: [],
	...computedParams.value,
})

const modalRef = ref(null)
const modalRefRoleMenus = ref(null)
const modalRefRoleDataScope = ref(null)
const BizTablesListRef = ref()
const { loading, run, list, total, reset } = xUsePageRequest(BIZ_Role_APIS.getPage, queryModel)
onMounted(() => {
	run()
})

async function handleDelete(id) {
	await toast.confirm('确定删除吗？')
	loading.value = true
	await BIZ_Role_APIS.delete(id).finally(() => {
		loading.value = false
	})
	toast.success('删除成功')
	if (!BizTablesListRef.value?.getTableRef()?.deleteNode({ id })) {
		reset()
	}
}

function handleOpenModal(type, id) {
	modalRef.value.open(type, id)
}

const columns = [
	{
		label: '角色编号',
		prop: 'id',
	},
	{
		label: '角色名称',
		prop: 'name',
	},
	{
		label: '角色标识',
		prop: 'code',
	},
	{
		label: '角色类型',
		prop: 'type',
	},
	{
		label: '备注',
		prop: 'remark',
	},
	{
		label: '角色状态',
		prop: 'status',
	},
	{
		label: '创建时间',
		prop: 'createTime',
		formatter: X_DATE_UTILS.formatDate,
	},
	{
		label: '操作',
		prop: 'action',
		width: 250,
	},
]
</script>
<template>
	<BizLayoutPageContentContainer>
		<BizFormModalsRole ref="modalRef" @success="run()" />
		<BizFormModalsRoleMenus ref="modalRefRoleMenus" />
		<BizFormModalsRoleDataScope ref="modalRefRoleDataScope" />
		<BizCardsQuery>
			<BizFormQueryForm :model="queryModel">
				<XFormItem label="角色名称" prop="name">
					<XInput v-model="queryModel.name" placeholder="请输入搜索" @keyup.enter="run" />
				</XFormItem>
				<XFormItem label="角色标识" prop="code">
					<XInput v-model="queryModel.code" placeholder="请输入搜索" @keyup.enter="run" />
				</XFormItem>
				<XFormItem label="角色状态" prop="status">
					<XSelect v-model="queryModel.status" placeholder="请选择搜索" />
				</XFormItem>
				<XFormItem label="创建时间" prop="createTime">
					<XDateTimePicker v-model="queryModel.createTime" range value-format="YYYY-MM-DD" type="date" placeholder="请选择搜索" />
				</XFormItem>
				<div class="absolute bottom-0.2rem right-0.2rem flex items-center justify-end gap-xs">
					<BizButtonsReset @click="reset" />
					<BizButtonsQuery @click="run" />
				</div>
			</BizFormQueryForm>
		</BizCardsQuery>
		<div class="flex items-center py-xs">
			<BizButtonsCreate v-if="hasPermission('system:role:create')" class="ml-auto" @click="handleOpenModal('create')" />
		</div>
		<BizCardsTable v-loading="loading">
			<BizTablesList ref="BizTablesListRef" :data="list" :columns="columns">
				<template #action="{ row, rowIndex }">
					<div class="flex items-center justify-center gap-xs">
						<XButton text-mode @click="modalRefRoleMenus.open(row)">菜单权限</XButton>
						<XButton text-mode @click="modalRefRoleDataScope.open(row)">数据权限</XButton>
						<BizButtonsUpdate v-if="hasPermission('system:role:update')" @click="handleOpenModal('update', row.id)" />
						<BizButtonsDelete v-if="hasPermission('system:role:delete')" @click="handleDelete(row.id)" />
					</div>
				</template>
			</BizTablesList>
			<div class="flex justify-end mt-base">
				<XPagination v-model="queryModel.pageNo" v-model:page-size="queryModel.pageSize" :total="total" @change="run" />
			</div>
		</BizCardsTable>
	</BizLayoutPageContentContainer>
</template>
<style scoped></style>
