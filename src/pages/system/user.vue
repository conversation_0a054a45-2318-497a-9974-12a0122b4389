<route lang="json">
{
	"name": "adminMy",
	"meta": {
		"title": "用户信息"
	}
}
</route>
<script setup lang="ts">
const props = defineProps({
	params: {
		type: Object,
		default: () => ({}),
	},
})
const computedParams = computed(() => props.params)
const queryModel = reactive({
	pageNo: 1,
	pageSize: 10,
	username: undefined,
	password: undefined,
	nickname: undefined,
	remark: undefined,
	deptId: undefined,
	postIds: undefined,
	email: undefined,
	mobile: undefined,
	sex: undefined,
	avatar: undefined,
	status: undefined,
	loginIp: undefined,
	loginDate: [],
	createTime: [],
	createType: undefined,
	entityId: undefined,
	...computedParams.value,
})

const modalRef = ref(null)
const modalRefUserRoles = ref()
const BizTablesListRef = ref()
const { loading, run, list, total, reset } = xUsePageRequest(BIZ_Users_APIS.getPage, queryModel)
onMounted(() => {
	run()
})

async function handleDelete(id) {
	await toast.confirm('确定删除吗？')
	loading.value = true
	await BIZ_Users_APIS.delete(id).finally(() => {
		loading.value = false
	})
	toast.success('删除成功')
	if (!BizTablesListRef.value?.getTableRef()?.deleteNode({ id })) {
		reset()
	}
}

function handleOpenModal(type, id) {
	modalRef.value.open(type, id)
}

const columns = [
	{
		label: '用户账号',
		prop: 'username',
	},
	{
		label: '用户昵称',
		prop: 'nickname',
	},
	{
		label: '备注',
		prop: 'remark',
	},
	/*	{
		label: '部门ID',
		prop: 'deptId',
	},
	{
		label: '岗位编号数组',
		prop: 'postIds',
	},*/
	{
		label: '用户邮箱',
		prop: 'email',
	},
	{
		label: '手机号码',
		prop: 'mobile',
	},
	/*	{
		label: '用户性别',
		prop: 'sex',
	},*/
	{
		label: '头像地址',
		prop: 'avatar',
	},
	{
		label: '帐号状态',
		prop: 'status',
	},
	/*{
		label: '最后登录IP',
		prop: 'loginIp',
	},*/
	{
		label: '最后登录时间',
		prop: 'loginDate',
		formatter: X_DATE_UTILS.formatDate,
	},
	{
		label: '创建时间',
		prop: 'createTime',
		formatter: X_DATE_UTILS.formatDate,
	},
	{
		label: '创建类型',
		prop: 'createType',
	},
	{
		label: '操作',
		prop: 'action',
		width: 180,
	},
]
</script>
<template>
	<BizLayoutPageContentContainer>
		<BizFormModalsUsers ref="modalRef" @success="run()" />
		<BizFormModalsUserRoles ref="modalRefUserRoles" />
		<BizCardsQuery>
			<BizFormQueryForm :model="queryModel">
				<XFormItem label="用户账号" prop="username">
					<XInput v-model="queryModel.username" placeholder="请输入搜索" @keyup.enter="run" />
				</XFormItem>
				<XFormItem label="用户昵称" prop="nickname">
					<XInput v-model="queryModel.nickname" placeholder="请输入搜索" @keyup.enter="run" />
				</XFormItem>
				<XFormItem label="备注" prop="remark">
					<XInput v-model="queryModel.remark" placeholder="请输入搜索" @keyup.enter="run" />
				</XFormItem>
				<XFormItem label="用户邮箱" prop="email">
					<XInput v-model="queryModel.email" placeholder="请输入搜索" @keyup.enter="run" />
				</XFormItem>
				<XFormItem label="手机号码" prop="mobile">
					<XInput v-model="queryModel.mobile" placeholder="请输入搜索" @keyup.enter="run" />
				</XFormItem>
				<XFormItem label="创建时间" prop="createTime">
					<XDateTimePicker v-model="queryModel.createTime" value-format="YYYY-MM-DD" type="date" placeholder="请选择搜索" />
				</XFormItem>
				<XFormItem label="创建类型" prop="createType">
					<XSelect v-model="queryModel.createType" placeholder="请选择搜索" />
				</XFormItem>
				<div class="absolute bottom-0.2rem right-0.2rem flex items-center justify-end gap-xs">
					<BizButtonsReset @click="reset" />
					<BizButtonsQuery @click="run" />
				</div>
			</BizFormQueryForm>
		</BizCardsQuery>
		<div class="flex items-center py-xs">
			<BizButtonsCreate v-if="hasPermission('x:users:create')" class="ml-auto" @click="handleOpenModal('create')" />
		</div>
		<BizCardsTable v-loading="loading">
			<BizTablesList :data="list" :columns="columns">
				<template #avatar="{ row }">
					<XImage class="h2rem" :src="row.avatar"></XImage>
				</template>
				<template #action="{ row }">
					<div class="flex items-center justify-center gap-xs">
						<XButton v-if="hasPermission('x:users:update')" text-mode @click="modalRefUserRoles.open(row)">分配角色</XButton>
						<BizButtonsUpdate @click="handleOpenModal('update', row.id)" />
						<BizButtonsDelete v-if="hasPermission('x:users:delete')" @click="handleDelete(row.id)" />
					</div>
				</template>
			</BizTablesList>
			<div class="flex justify-end mt-base">
				<XPagination v-model="queryModel.pageNo" v-model:page-size="queryModel.pageSize" :total="total" @change="run" />
			</div>
		</BizCardsTable>
	</BizLayoutPageContentContainer>
</template>
<style scoped></style>
