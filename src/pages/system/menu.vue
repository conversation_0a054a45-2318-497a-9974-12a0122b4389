<route lang="json">
{
	"meta": {
		"title": "菜单"
	}
}
</route>
<script setup lang="ts">
const props = defineProps({
	params: {
		type: Object,
		default: () => ({}),
	},
})
const computedParams = computed(() => props.params)
const queryModel = reactive({
	pageNo: 1,
	pageSize: 10,
	name: undefined,
	permission: undefined,
	type: undefined,
	sort: undefined,
	parentId: undefined,
	path: undefined,
	icon: undefined,
	component: undefined,
	componentName: undefined,
	status: undefined,
	visible: undefined,
	keepAlive: undefined,
	alwaysShow: undefined,
	createTime: [],
	...computedParams.value,
})

const modalRef = ref(null)
const BizTablesListRef = ref()
const { loading, run, data: list, reset } = xUseRequest(BIZ_Menu_APIS.getList, queryModel)
onMounted(() => {
	run()
})
async function handleDelete(id) {
	await toast.confirm('确定删除吗？')
	loading.value = true
	await BIZ_Menu_APIS.delete(id).finally(() => {
		loading.value = false
	})
	toast.success('删除成功')
	if (!BizTablesListRef.value?.getTableRef()?.deleteNode({ id })) {
		reset()
	}
}

function handleOpenModal(type, id) {
	modalRef.value.open(type, id)
}

const columns = [
	{
		label: '菜单名称',
		prop: 'name',
		width: 200,
	},
	{
		label: 'ID',
		prop: 'id',
		width: 100,
	},
	{
		label: '顺序',
		prop: 'sort',
		width: 80,
	},
	/*	{
    label: '权限标识',
    prop: 'permission',
  },*/
	/*{
    label: '菜单类型',
    prop: 'type',
  },
  {
    label: '显示顺序',
    prop: 'sort',
  },*/
	/*	{
    label: '父菜单ID',
    prop: 'parentId',
  },*/
	{
		label: '路由地址',
		prop: 'path',
	},
	{
		label: '菜单图标',
		prop: 'icon',
	},
	/*	{
    label: '组件名',
    prop: 'componentName',
  },*/
	{
		label: '是否可见',
		prop: 'visible',
	},
	{
		label: '是否缓存',
		prop: 'keepAlive',
	},
	{
		label: '创建时间',
		prop: 'createTime',
		formatter: X_DATE_UTILS.formatDate,
	},
	{
		label: '操作',
		prop: 'action',
	},
]
</script>
<template>
	<BizLayoutPageContentContainer>
		<BizFormModalsMenu
			ref="modalRef"
			@success="
				(node) => {
					BizTablesListRef?.getTableRef()?.saveNode(node)
				}
			"
		/>
		<BizCardsQuery>
			<BizFormQueryForm :model="queryModel">
				<XFormItem label="菜单名称" prop="name">
					<XInput v-model="queryModel.name" placeholder="请输入搜索" @keyup.enter="run" />
				</XFormItem>
				<XFormItem label="权限标识" prop="permission">
					<XInput v-model="queryModel.permission" placeholder="请输入搜索" @keyup.enter="run" />
				</XFormItem>
				<XFormItem label="菜单类型" prop="type">
					<XSelect v-model="queryModel.type" placeholder="请选择搜索" />
				</XFormItem>
				<XFormItem label="显示顺序" prop="sort">
					<XInput v-model="queryModel.sort" placeholder="请输入搜索" @keyup.enter="run" />
				</XFormItem>
				<XFormItem label="父菜单ID" prop="parentId">
					<XInput v-model="queryModel.parentId" placeholder="请输入搜索" @keyup.enter="run" />
				</XFormItem>
				<XFormItem label="路由地址" prop="path">
					<XInput v-model="queryModel.path" placeholder="请输入搜索" @keyup.enter="run" />
				</XFormItem>
				<XFormItem label="菜单图标" prop="icon">
					<XInput v-model="queryModel.icon" placeholder="请输入搜索" @keyup.enter="run" />
				</XFormItem>
				<XFormItem label="组件路径" prop="component">
					<XInput v-model="queryModel.component" placeholder="请输入搜索" @keyup.enter="run" />
				</XFormItem>
				<XFormItem label="组件名" prop="componentName">
					<XInput v-model="queryModel.componentName" placeholder="请输入搜索" @keyup.enter="run" />
				</XFormItem>
				<XFormItem label="菜单状态" prop="status">
					<XSelect v-model="queryModel.status" placeholder="请选择搜索" />
				</XFormItem>
				<XFormItem label="是否可见" prop="visible">
					<XSelect v-model="queryModel.visible" placeholder="请选择搜索" />
				</XFormItem>
				<XFormItem label="是否缓存" prop="keepAlive">
					<XSelect v-model="queryModel.keepAlive" placeholder="请选择搜索" />
				</XFormItem>
				<XFormItem label="是否总是显示" prop="alwaysShow">
					<XSelect v-model="queryModel.alwaysShow" placeholder="请选择搜索" />
				</XFormItem>
				<div class="absolute bottom-0.2rem right-0.2rem flex items-center justify-end gap-xs">
					<BizButtonsReset @click="reset" />
					<BizButtonsQuery @click="run" />
				</div>
			</BizFormQueryForm>
		</BizCardsQuery>
		<div class="flex items-center py-xs">
			<BizButtonsCreate v-if="hasPermission('system:menu:create')" class="ml-auto" @click="handleOpenModal('create')" />
		</div>
		<BizCardsTable v-loading="loading" class="">
			<BizTablesList ref="BizTablesListRef" treeable :data="list" :columns="columns">
				<template #action="{ row }">
					<div class="flex items-center justify-center gap-xs">
						<BizButtonsUpdate v-if="hasPermission('system:menu:update')" @click="handleOpenModal('update', row.id)" />
						<BizButtonsDelete v-if="hasPermission('system:menu:delete')" @click="handleDelete(row.id)" />
					</div>
				</template>
			</BizTablesList>
		</BizCardsTable>
	</BizLayoutPageContentContainer>
</template>
<style scoped></style>
