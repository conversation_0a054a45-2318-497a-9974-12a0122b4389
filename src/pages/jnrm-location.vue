<route lang="json">
{
	"meta": {
		"title": "地点"
	}
}
</route>
<script setup lang="ts">
const props = defineProps({
	params: {
		type: Object,
		default: () => ({}),
	},
})
const computedParams = computed(() => props.params)
const queryModel = reactive({
	pageNo: 1,
	pageSize: 10,
	name: null,
	address: null,
	longitude: null,
	latitude: null,
	type: null,
	factoryType: null,
	createTime: [],
	...computedParams.value,
})

const modalRef = ref(null)
const BizTablesListRef = ref()
const { loading, run, list, total, reset } = xUsePageRequest(BIZ_JnrmLocation_APIS.getPage, queryModel)
onMounted(() => {
	run()
})

async function handleDelete(id) {
	await toast.confirm('确定删除吗？')
	loading.value = true
	await BIZ_JnrmLocation_APIS.delete(id).finally(() => {
		loading.value = false
	})
	toast.success('删除成功')
	if (!BizTablesListRef.value?.getTableRef()?.deleteNode({ id })) {
		reset()
	}
}

function handleOpenModal(type, id) {
	modalRef.value.open(type, id, computedParams.value)
}

const columns = [
	{
		label: '类型',
		prop: 'factoryType',
	},
	{
		label: '名称',
		prop: 'name',
	},
	{
		label: '详细地址',
		prop: 'address',
	},
	{
		label: '经度',
		prop: 'longitude',
	},
	{
		label: '纬度',
		prop: 'latitude',
	},
	{
		label: '创建时间',
		prop: 'createTime',
		width: 150,
		formatter: X_DATE_UTILS.formatDate,
	},
	{
		label: '操作',
		prop: 'action',
	},
]
</script>
<template>
	<BizLayoutPageContentContainer>
		<BizFormModalsJnrmLocation ref="modalRef" @success="run()" />
		<BizCardsQuery>
			<BizFormQueryForm :model="queryModel">
				<XFormItem label="名称" prop="name">
					<XInput v-model="queryModel.name" placeholder="请输入搜索" @keyup.enter="run" />
				</XFormItem>
				<XFormItem label="详细地址" prop="address">
					<XInput v-model="queryModel.address" placeholder="请输入搜索" @keyup.enter="run" />
				</XFormItem>
				<XFormItem label="类型" prop="factoryType">
					<XSelect v-model="queryModel.factoryType" placeholder="请选择搜索" :clearable="false" tag-group-name="JNRM部门类型" @change="run()" />
				</XFormItem>
			</BizFormQueryForm>
			<div class="flex items-center justify-end gap-xs">
				<BizButtonsReset @click="reset" />
				<BizButtonsQuery @click="run" />
			</div>
		</BizCardsQuery>
		<div class="flex items-center justify-between py-xs">
			<div class="flex gap-xl"></div>
			<div class="flex items-center">
				<BizButtonsCreate v-if="hasPermission('x:jnrm-location:create')" @click="handleOpenModal('create')" />
			</div>
		</div>
		<BizCardsTable v-loading="loading">
			<BizTablesList ref="BizTablesListRef" :data="list" :columns="columns">
				<template #action="{ row, rowIndex }">
					<div class="flex items-center justify-center gap-xs">
						<BizButtonsUpdate v-if="hasPermission('x:jnrm-location:update')" @click="handleOpenModal('update', row.id)" />
						<BizButtonsDelete v-if="hasPermission('x:jnrm-location:delete')" @click="handleDelete(row.id)" />
					</div>
				</template>
			</BizTablesList>
			<div class="flex justify-end mt-base">
				<XPagination v-model="queryModel.pageNo" v-model:page-size="queryModel.pageSize" :total="total" @change="run" />
			</div>
		</BizCardsTable>
	</BizLayoutPageContentContainer>
</template>
<style scoped></style>
