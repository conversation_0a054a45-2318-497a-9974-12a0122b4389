<route lang="json">
{
	"meta": {
		"title": "车辆"
	}
}
</route>
<script setup lang="ts">
import JnrmTransportOrder from '~/pages/jnrm-transport-order.vue'
import JnrmAlert from '~/pages/jnrm-alert.vue'
const props = defineProps({
	params: {
		type: Object,
		default: () => ({}),
	},
})
const computedParams = computed(() => props.params)
const queryModel = reactive({
	pageNo: 1,
	pageSize: 10,
	licensePlate: null,
	licenseUrl: null,
	nextInspectionDate: [],
	vehicleType: null,
	loadCapacity: null,
	vehicleBrand: null,
	isDisabled: null,
	isInspectionExpired: null,
	createTime: [],
	...computedParams.value,
})

const modalRef = ref(null)
const BizTablesListRef = ref()
const { loading, run, list, total, reset } = xUsePageRequest(BIZ_JnrmVehicle_APIS.getPage, queryModel)
onMounted(() => {
	run()
})

async function handleDelete(id) {
	await toast.confirm('确定删除吗？')
	loading.value = true
	await BIZ_JnrmVehicle_APIS.delete(id).finally(() => {
		loading.value = false
	})
	toast.success('删除成功')
	if (!BizTablesListRef.value?.getTableRef()?.deleteNode({ id })) {
		reset()
	}
}

function handleOpenModal(type, id) {
	modalRef.value.open(type, id, computedParams.value)
}

const columns = [
	{
		label: '车牌号',
		prop: 'licensePlate',
	},
	{
		label: '车牌颜色',
		prop: 'licensePlateColor',
		tagGroupName: 'JNRM车牌颜色',
	},
	{
		label: '下次年检日期',
		prop: 'nextInspectionDate',
		width: 150,
		formatter: X_DATE_UTILS.formatOnlyDate,
	},
	{
		label: '车型',
		prop: 'vehicleType',
	},
	{
		label: '载重吨位',
		prop: 'loadCapacity',
	},
	{
		label: '车辆品牌',
		prop: 'vehicleBrand',
	},
	{
		label: '行驶证',
		prop: 'licenseUrl',
		type: 'img',
	},
	// {
	// 	label: '禁用',
	// 	prop: 'isDisabled',
	// 	tagGroupName: '是否',
	// 	tagValueFormat: 'bool',
	// },
	{
		label: '操作',
		prop: 'action',
		width: 180,
	},
]
const orderModalVisible = ref(false)
const alertModalVisible = ref(false)
const currentOrderRow = ref(null)
const currentAlertRow = ref(null)
function handleOrderOpen(row) {
	orderModalVisible.value = true
	currentOrderRow.value = row
}
function handleAlertOpen(row) {
	alertModalVisible.value = true
	currentAlertRow.value = row
}
</script>
<template>
	<BizLayoutPageContentContainer>
		<BizFormModalsJnrmVehicle ref="modalRef" @success="run()" />
		<BizCardsQuery>
			<BizFormQueryForm :model="queryModel">
				<XFormItem label="车牌号" prop="licensePlate">
					<XInput v-model="queryModel.licensePlate" placeholder="请输入搜索" @keyup.enter="run" />
				</XFormItem>
				<!--				<XFormItem label="车型" prop="vehicleType">
					<XInput v-model="queryModel.vehicleType" placeholder="请输入搜索" tag-group-name="" />
				</XFormItem>
				<XFormItem label="车辆品牌" prop="vehicleBrand">
					<XInput v-model="queryModel.vehicleBrand" placeholder="请输入搜索" @keyup.enter="run" />
				</XFormItem>-->
				<!--				<XFormItem label="是否禁用" prop="isDisabled">
					<XSelect v-model="queryModel.isDisabled" placeholder="请选择搜索" tag-group-name="是否" tag-value-format="bool" />
				</XFormItem>-->
				<XFormItem label="年检是否过期" prop="isInspectionExpired" label-width="6rem">
					<XSelect
						v-model="queryModel.isInspectionExpired"
						:clearable="false"
						placeholder="请选择搜索"
						tag-group-name="是否"
						tag-value-format="bool"
					/>
				</XFormItem>
			</BizFormQueryForm>
			<div class="flex items-center justify-end gap-xs">
				<BizButtonsReset @click="reset" />
				<BizButtonsQuery @click="run" />
			</div>
		</BizCardsQuery>
		<div class="flex items-center justify-between py-xs">
			<div></div>
			<div class="flex items-center">
				<BizButtonsCreate v-if="hasPermission('x:jnrm-vehicle:create')" @click="handleOpenModal('create')" />
			</div>
		</div>
		<BizCardsTable v-loading="loading">
			<BizTablesList ref="BizTablesListRef" :data="list" :columns="columns">
				<template #action="{ row, rowIndex }">
					<div class="flex items-center justify-center gap-xs">
						<XButton text-mode @click="handleOrderOpen(row)">运单记录</XButton>
						<XButton text-mode @click="handleAlertOpen(row)">告警记录</XButton>
						<div></div>
						<BizButtonsUpdate v-if="hasPermission('x:jnrm-vehicle:update')" @click="handleOpenModal('update', row.id)" />
						<BizButtonsDelete v-if="hasPermission('x:jnrm-vehicle:delete')" @click="handleDelete(row.id)" />
					</div>
				</template>
			</BizTablesList>
			<div class="flex justify-end mt-base">
				<XPagination v-model="queryModel.pageNo" v-model:page-size="queryModel.pageSize" :total="total" @change="run" />
			</div>
		</BizCardsTable>
		<XModal v-model="orderModalVisible" container-class="!w-auto !min-w-50rem" title="运单记录">
			<JnrmTransportOrder :params="{ licensePlate: currentOrderRow?.licensePlate }" container-plain :addable="false" :operable="false" />
		</XModal>
		<XModal v-model="alertModalVisible" container-class="!w-auto !min-w-50rem" title="告警记录">
			<jnrm-alert :params="{ licensePlate: currentAlertRow?.licensePlate }" container-plain :addable="false" :operable="false" />
		</XModal>
	</BizLayoutPageContentContainer>
</template>
<style scoped></style>
