{"name": "tov-template", "version": "1.19.0", "description": "vite + vue3 + ts 开箱即用现代开发模板 | vite + vue3 + ts out-of-the-box modern development template", "type": "module", "scripts": {"dev": "vite", "test": "vitest", "build": "vite build", "prepare": "husky install", "dev:host": "vite --host", "dev:open": "vite --open", "preview": "vite preview", "coverage": "vitest --coverage", "preinstall": "npx only-allow pnpm", "typecheck": "npx vue-tsc --noEmit", "preview:host": "vite preview --host", "preview:open": "vite preview --open", "lint": "eslint --ext .ts,.js,.jsx,.vue .", "release": "plop --plopfile scripts/release.cjs", "auto:remove": "plop --plopfile scripts/remove.cjs", "auto:create": "plop --plopfile scripts/create.cjs", "build:debug": "cross-env NODE_ENV=debug vite build", "safe:init": "plop --plopfile scripts/safe-init.cjs", "deps:fresh": "plop --plopfile scripts/deps-fresh.cjs", "lint:fix": "eslint --fix --ext .ts,.js,.jsx,.vue,.cjs ."}, "engines": {"node": ">=20.17.0"}, "packageManager": "pnpm@8.15.9", "devDependencies": {"@iconify-json/bi": "^1.1.22", "@iconify-json/carbon": "^1.2.5", "@iconify-json/ep": "^1.2.2", "@iconify-json/fluent": "^1.1.56", "@iconify-json/gis": "^1.2.3", "@iconify-json/healthicons": "^1.2.10", "@iconify-json/heroicons": "^1.2.2", "@iconify-json/hugeicons": "^1.2.6", "@iconify-json/ic": "^1.1.17", "@iconify-json/icon-park-outline": "^1.1.15", "@iconify-json/line-md": "^1.2.5", "@iconify-json/material-symbols": "^1.1.82", "@iconify-json/mdi": "^1.2.1", "@iconify-json/mingcute": "^1.2.3", "@iconify-json/ph": "^1.2.2", "@iconify-json/solar": "^1.2.0", "@iconify-json/streamline": "^1.2.5", "@iconify-json/tabler": "^1.2.19", "@iconify-json/tdesign": "^1.2.8", "@intlify/unplugin-vue-i18n": "^3.0.1", "@types/file-saver": "^2.0.7", "@types/ityped": "^1.0.3", "@types/node": "^20.17.19", "@typescript-eslint/parser": "7.18.0", "@unocss/eslint-config": "0.62.3", "@unocss/reset": "^0.62.3", "@vitejs/plugin-vue": "^5.2.1", "@vitejs/plugin-vue-jsx": "^3.1.0", "@vue/test-utils": "^2.4.6", "@vueuse/components": "^10.11.1", "@vueuse/core": "^10.11.1", "@vueuse/integrations": "^10.11.1", "axios": "^1.7.7", "browserslist": "^4.23.3", "c8": "^9.1.0", "changelogen": "^0.5.5", "consola": "^3.2.3", "cross-env": "^7.0.3", "defu": "^6.1.4", "echarts": "^5.5.1", "eslint": "8.57.0", "eslint-config-prettier": "9.1.0", "eslint-plugin-prettier": "5.2.1", "eslint-plugin-vue": "^9.28.0", "fs-extra": "^11.2.0", "husky": "^9.1.5", "ityped": "^1.0.3", "jsdom": "^26.0.0", "kolorist": "^1.8.0", "lint-staged": "^15.2.10", "local-pkg": "^0.5.0", "markdown-it-prism": "^2.3.0", "mockjs": "^1.1.0", "nprogress": "^0.2.0", "perfect-debounce": "^1.0.0", "pinia": "^2.2.2", "pinia-plugin-persistedstate": "^3.2.3", "plop": "^4.0.1", "prettier": "^3.3.3", "prism-theme-vars": "^0.2.5", "simple-git": "^3.26.0", "taze": "^0.16.7", "terser": "^5.31.6", "typescript": "^5.5.4", "unplugin-auto-import": "^0.18.2", "unplugin-vue-components": "^0.27.4", "unplugin-vue-markdown": "^0.26.2", "unplugin-vue-router": "^0.10.8", "vite": "^6.2.3", "vite-auto-import-resolvers": "^3.2.1", "vite-layers": "^0.5.2", "vite-plugin-compression": "^0.5.1", "vite-plugin-env-types": "^0.1.4", "vite-plugin-legacy-swc": "^1.2.3", "vite-plugin-mock": "2.9.8", "vite-plugin-removelog": "^0.2.2", "vite-plugin-use-modules": "^1.4.8", "vite-plugin-vue-devtools": "^7.4.4", "vite-plugin-vue-meta-layouts": "^0.4.3", "vitest": "^1.6.1", "vue": "^3.5.3", "vue-dark-switch": "^1.0.6", "vue-echarts": "^6.7.3", "vue-i18n": "^9.14.0", "vue-request": "2.0.4", "vue-router": "^4.4.3", "vue-toastification": "2.0.0-rc.5"}, "lint-staged": {"*.{js,jsx,ts,tsx,vue}": "eslint --cache --fix"}, "overrides": {"sourcemap-codec": "npm:@jridgewell/sourcemap-codec@latest", "array-includes": "npm:@nolyfill/array-includes@latest", "array.prototype.findlastindex": "npm:@nolyfill/array.prototype.findlastindex@latest", "array.prototype.flat": "npm:@nolyfill/array.prototype.flat@latest", "array.prototype.flatmap": "npm:@nolyfill/array.prototype.flatmap@latest", "arraybuffer.prorotype.slice": "npm:@nolyfill/arraybuffer.prorotype.slice@latest", "function.prototype.name": "npm:@nolyfill/function.prototype.name@latest", "has": "npm:@nolyfill/has@latest", "is-regex": "npm:@nolyfill/is-regex@latest", "object-keys": "npm:@nolyfill/object-keys@latest", "object.assign": "npm:@nolyfill/object.assign@latest", "object.entries": "npm:@nolyfill/object.entries@latest", "object.fromentries": "npm:@nolyfill/object.fromentries@latest", "object.values": "npm:@nolyfill/object.values@latest", "vue-demi": "npm:vue-demi@latest"}, "repository": {"url": "https://github.com/dishait/tov-template"}, "browserslist": [">= 0.25%", "last 2 versions", "not dead", "not ie <= 11", "Android >= 4.0", "iOS >= 8"], "dependencies": {"@hufe921/canvas-editor-plugin-floating-toolbar": "^0.0.4", "@iconify-json/svg-spinners": "^1.2.2", "@tinymce/tinymce-vue": "^6.1.0", "@types/chroma-js": "^3.1.1", "@unocss/runtime": "^0.65.1", "async-validator": "^4.2.5", "chroma-js": "^3.1.2", "dayjs": "2.0.0-alpha.4", "docx": "^9.5.0", "fabric": "^6.6.1", "file-saver": "^2.0.5", "gsap": "^3.12.5", "html-docx-ts": "^0.0.5", "lightningcss": "^1.29.3", "lodash": "^4.17.21", "mammoth": "^1.9.0", "proj4": "^2.15.0", "qs": "^6.14.0", "unocss": "66.1.0-beta.7", "uuid": "^11.0.3", "vjmap": "^1.0.149"}}